package com.wangda.oa.modules.workflow.constant;

/**
 * 流程常量信息
 * <AUTHOR>
 * @date 2021/5/12 12:46
 */
public class ProcessConstants {

    /**
     * 动态数据
     */
    public static final String DATA_TYPE = "dynamic";

    /**
     * 候选人
     */
    public static final String USER_TYPE_USERS = "candidateUsers";

    /**
     * 审批组
     */
    public static final String USER_TYPE_ROUPS = "candidateGroups";

    /**
     * 单个审批人
     */
    public static final String PROCESS_APPROVAL = "approval";

    /**
     * 会签人员
     */
    public static final String PROCESS_MULTI_INSTANCE_USER = "userList";

    /**
     * nameapace
     */
    public static final String NAMASPASE = "http://flowable.org/bpmn";

    /**
     * 会签节点
     */
    public static final String PROCESS_MULTI_INSTANCE = "multiInstance";

    /**
     * 自定义属性 dataType
     */
    public static final String PROCESS_CUSTOM_DATA_TYPE = "dataType";

    /**
     * 自定义属性 userType
     */
    public static final String PROCESS_CUSTOM_USER_TYPE = "userType";

    /**
     * 初始化人员
     */
    public static final String PROCESS_INITIATOR = "INITIATOR";

    /**
     * 意见事件类型
     */
    public static final String PROCESS_COMMENT_TYPE_EVENT = "event";

    /**
     * 自定义接口
     */
    public static final String PROCESS_CUSTOM_ENDPOINT_API = "customEndpointApi";

    // 多端支持：pc,zwdd,app
    public static final String BPM_MUTLI_DEVICE_SUPPORT = "mutliDeviceSupport";

    // 流程发起人 @role.everyone
    public static final String BPM_PROCESS_STARTER = "processStarter";

    // 流程意见排序 正序:false（默认），倒序:true
    public static final String BPM_PROCESS_REMARK_SORT = "processRemarkSort";

    // 打印模版
    public static final String BPM_PROCESS_PRINT_TEMPLATE_KEY = "printTemplateKey";

    /**
     * 流程跳过
     */
    public static final String FLOWABLE_SKIP_EXPRESSION_ENABLED = "_FLOWABLE_SKIP_EXPRESSION_ENABLED";

    // 标题
    public static final String BPM_BACKLOG_SUBJECT_RULE = "backlogSubjectRule";
    // 待阅
    public static final String BPM_BACKLOG_DOCUMENT_REVIEW = "backlogDocumentReview";

    /**
     * 流程结束标识
     * 会签完成后，其他未签到节点的删除原因
     */
    public static final String DELETE_REASON_END = "MI_END";
    public static final String DELETE_REASON_EXECUTION_END = "Delete MI execution";
    public static final String DELETE_REASON_CHANGE = " activity to ";

    /**
     * 多实例变量
     */
    public static final String NUMBER_OF_INSTANCES = "nrOfInstances";
    public static final String NUMBER_OF_COMPLETED_INSTANCES = "nrOfCompletedInstances";
    public static final String NUMBER_OF_ACTIVE_INSTANCES = "nrOfActiveInstances";

    /**
     * 任务相关
     */
    public static final String BPM_WD_NAMESPACE = "http://www.hzwangda.com/bpmn-extension";
    public static final String BPM_TASK_TASKNAME = "taskName";
    // 节点处理方式
    public static final String BPM_TASK_TASKEXECTYPE = "taskExecType";
    public static final String BPM_TASK_TASKEXECTYPE_BLANK = "blank";


    // 单个审批人
    public static final String BPM_TASK_ASSIGNEE = "assignee";
    // 审批人列表缓存
    public static final String BPM_TASK_ASSIGNEE_CACHE = "assigneeCache";
    // 任务接收人
    public static final String BPM_TASK_TO_ASSIGNEES = "taskToAssignees";
    // 候选指定人
    public static final String BPM_TASK_ASSIGNEE_RULE = "assigneeRule";
    public static final String BPM_TASK_DEF_KEY = "taskDefKey";
    public static final String BPM_TASK_MESSAGE = "message";
    // 人员选择器类别
    public static final String BPM_TASK_USERSELECTOR = "userSelector";
    // 是否填写意见
    public static final String BPM_TASK_REMARKDISABLE = "remarkDisable";
    // 意见是否允许为空
    public static final String BPM_TASK_REMARKBLANKALLOW = "remarkBlankAllow";
    // 是否上传附件
    public static final String BPM_TASK_REMARKATTACHMENTALLOW = "remarkAttachmentAllow";
    // 通知方式
    public static final String BPM_TASK_REMINDWAY = "remindWay";
    // 意见上表单
    public static final String BPM_TASK_REMARKTOFORMFIELD = "remarkToFormField";
    // 是否手写签名
    public static final String BPM_TASK_REMARKSEAL = "remarkSeal";
    // 是否预签
    public static final String BPM_TASK_PRESIGN = "preSign";
    // 是否覆盖本人意见
    public static final String BPM_TASK_REMARKCOVERD = "remarkCovered";
    // 意见不上规则
    public static final String BPM_TASK_REMARKEXCLUDEREGEX = "remarkExcludeRegex";
    // 是否预签意见
    public static final String BPM_TASK_PRESIGNREMARK = "preSignRemark";
    // 是否使用预签意见
    public static final String BPM_TASK_USEPRESIGNREMARK = "usePreSignRemark";
    // 预签抄送
    public static final String BPM_TASK_PRESIGNCOPYTO = "preSignCopyTo";

    /**
     * 连线
     */
    // 连线是否重写用户任务规则
    public static final String BPM_BPM_USERTASK_OVERRIDE = "userTaskOverride";
    // 候选指定人
    public static final String BPM_FLOW_CANDIDATE_RULE = "candidateRule";

    /**
     * 表单相关
     */
    // 表单结构
    public static final String BPM_FORM_JSON = "formJson";
    public static final String BPM_MOBILE_FORM_JSON = "mobileFormJson";

    // 表单数据
    public static final String BPM_FORM_DATA = "formData";

    // 表单样式
    public static final String BPM_FORM_SKIN = "formSkin";
    public static final String BPM_FORM_PC_SKIN = "formPcSkin";

    /**
     * 处理单数据
     */
    public static final String BPM_BPM_DATA = "bpmData";

    // 启动人
    public static final String BPM_BPM_STARTEDBY = "startedBy";

    // 预签选中处理人
    public static final String BPM_BPM_PRESIGNASSIGNEE = "preSignAssignee";

    // 预签意见
    public static final String BPM_BPM_PRESIGNREMARK = "preSignRemark";

    // 预签选中抄送人
    public static final String BPM_BPM_PRESIGNCOPYTOASSIGNEE = "preSignCopyToAssignee";

    // 审批意见
    public static final String BPM_BPM_APPROVALREMARK = "approvalRemark";

    // 抄送人员
    public static final String BPM_BPM_COPYTO_PREFIX = "copyToAssignees_";

    /**
     * 设备类型-移动
     */
    public static final String BPM_DEVICE_MOBILE = "mobile";

    // 默认设置
    public static final String BPM_RWH_DEFAULT = "rwh_default";

    /**
     * 表达式-获取负责人
     */
    // 部门负责人
    public static final String BPM_EXPRESSION_HEADOFUSER = "@position.headOfCurrentUser";
    // 分管领导
    public static final String BPM_EXPRESSION_LEADERSHIPOFCURRENTUSER = "@position.leadershipOfCurrentUser";
    // 单位负责人
    public static final String BPM_EXPRESSION_HEADOFUNIT = "@position.headOfUnit";
    // 厅领导
    public static final String BPM_EXPRESSION_HALLLEADERSHIP = "@position.hallLeadership";
    // 流程发起人
    public static final String BPM_EXPRESSION_STARTEROFPROCESS = "@position.starterOfProcess";
    // 节点审批人
    public static final String BPM_EXPRESSION_APPROVEOFPROCESS = "@position.approveOfProcess";
    //流程管理员
    public static final String BPM_EXPRESSION_POSITIONBPMADMIN = "@position.bpmAdmin";
    // 自定义业务人员
    public static final String BPM_EXPRESSION_CUSTOMBIZUSER = "@position.customBizUser";

    public static final String BPM_EXPRESSION_CURRENTUSERISHEAD = "@flowExpression.currentUserIsHead";
    public static final String BPM_EXPRESSION_CURRENTUSERISNORMAL = "@flowExpression.currentUserIsNormal";
    public static final String BPM_EXPRESSION_CURRENTUSERISLEADERSHIP = "@flowExpression.currentUserIsLeadership";

    // 表达式-返回前端值
    public static final String BPM_SEQUENCEFLOW_EXPRESSION_PREEXPRESSION = "preExpression";
    public static final String BPM_SEQUENCEFLOW_EXPRESSION_FORNTPREEXPRESSION = "forntPreExpression";
    public static final String BPM_SEQUENCEFLOW_EXPRESSION_FLOWTYPE = "flowType";
    public static final String BPM_SEQUENCEFLOW_EXPRESSION_ISDEFAULTFLOW = "isDefaultFlow";
    public static final String BPM_SEQUENCEFLOW_EXPRESSION_FLOWGROUP = "flowGroup";
    // 是否隐藏
    public static final String BPM_SEQUENCEFLOW_EXPRESSION_FLOWHIDDEN = "flowHidden";

    // 浙政钉待办相关
    public static final String BPM_REDIS_DINGDING_TASK_BIZ = "BPM_REDIS_DINGDING_TASK_BIZ";

    public static final String BPM_REDIS_DINGDING_TASK_PACKAGE = "BPM_REDIS_DINGDING_TASK_PACKAGE";

    /**
     * 业务表单相关
     */
    // 表单模版编号
    public static final String BPM_FORM_TEMPLATE_ID = "templateId";
    public static final String BPM_FORM_MOBILE_TEMPLATE_ID = "mobileTemplateId";

    // 表单标题
    public static final String BPM_FORM_TITLE = "bpmFormTitle";

    // 待阅
    public static final String BPM_FORM_DOCUMENT_REVIEW = "bpmFormDocumentReview";

    // 创建待办政务钉钉失败列表
    public static final String FAIL_ZWDD_CREATETASK = "zwddCreateTask";

    // 完成待办政务钉钉完成失败列表
    public static final String FAIL_ZWDD_TASKFINISH = "zwddTaskFinish";

    // 取消待办政务钉钉取消失败列表
    public static final String FAIL_ZWDD_TASKCANCEL = "zwddTaskCancel";

    //通知失败对应redis的key
    public static final String FAIL_MESSAGE = "failMessage";

    /**
     * 查询状态-待办
     */
    public static final int QUERY_STATUS_DB = 0;

    /**
     * 查询状态-已办未完结
     */
    public static final int QUERY_STATUS_YJWWJ = 1;

    /**
     * 查询状态-已办已完结
     */
    public static final int QUERY_STATUS_YJYWJ = 2;

    /**
     * 查询状态-待阅
     */
    public static final int QUERY_STATUS_DY = 3;

}
