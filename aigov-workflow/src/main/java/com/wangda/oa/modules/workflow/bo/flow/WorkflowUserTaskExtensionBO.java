package com.wangda.oa.modules.workflow.bo.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @date 2021/4/16 上午9:25
 */
@Data
public class WorkflowUserTaskExtensionBO {
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "流程定义id")
    private String flowDefineId;

    @ApiModelProperty(value = "环节Key")
    private String linkKey;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Column(name = "name")
    @ApiModelProperty(value = "名称")
    private String name;

    @Column(name = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "表单默认读写隐状态(R:读,R:写,H:隐藏)")
    private String formRead;

    @ApiModelProperty(value = "意见绑定规则")
    private String opinionBindingRule;

    @ApiModelProperty(value = "提示语")
    private String prompt;

    @ApiModelProperty(value = "PC表单样式")
    private String formSkin;

    @ApiModelProperty(value = "移动端表单样式")
    private String mobileFormSkin;
}
