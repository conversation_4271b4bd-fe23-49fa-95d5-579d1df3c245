package com.wangda.oa.modules.workflow.factory;

import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.common.engine.impl.de.odysseus.el.TreeValueExpression;
import org.flowable.common.engine.impl.el.ExpressionManager;
import org.flowable.common.engine.impl.el.FlowableExpressionFactory;
import org.flowable.common.engine.impl.el.JuelExpression;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * flowable 选人条件表达式执行
 * <AUTHOR>
 * @date 2022-06-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class SimpleUserExpressionExecuteCmd implements Command<List<SimpleUserDto>> {

    /**
     * 执行
     */
    private String executionId;
    private String conditionExpression;

    private final static String EXPRESSION = "\\(\\w*,";
    private final static Pattern EXPRESSION_PATTERN = Pattern.compile(EXPRESSION);
    private final static String DECODE_DOLLAR_SIGN = "&";


    public SimpleUserExpressionExecuteCmd(String executionId, String conditionExpression) {
        this.executionId = executionId;
        this.conditionExpression = conditionExpression;
    }

    @Override
    public List<SimpleUserDto> execute(CommandContext commandContext) {
        List<SimpleUserDto> result = new ArrayList<>();
        ExpressionManager expressionManager = CommandContextUtil.getProcessEngineConfiguration(commandContext).getExpressionManager();
        Expression expression = expressionManager.createExpression(conditionExpression);

        ExecutionEntity executionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).findById(executionId);
        if(Objects.isNull(executionEntity)) {
            executionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).create();

            String variableName = null;
            String expressionText = expression.getExpressionText();
            Matcher matcher = EXPRESSION_PATTERN.matcher(expressionText);
            while(matcher.find()) {
                variableName = matcher.group();
            }

            if(StringUtils.isNotEmpty(variableName)) {
                List<HistoricTaskInstance> historicTaskInstanceList = CommandContextUtil.getProcessEngineConfiguration(commandContext).getHistoryService().createHistoricTaskInstanceQuery().executionId(executionId).includeProcessVariables().list();
                Map<String, Object> variables = historicTaskInstanceList.get(0).getProcessVariables();
                variableName = variableName.substring(1, variableName.length() - 1);
                String username = variables.get(variableName).toString();

                username = username.replaceAll("\\$", DECODE_DOLLAR_SIGN);// encode replacement;
                expressionText = expressionText.replaceAll(EXPRESSION, "(\"" + username + "\",");
                expressionText = expressionText.replaceAll(DECODE_DOLLAR_SIGN, "\\$");// decode replacement;

                FlowableExpressionFactory flowableExpressionFactory = new FlowableExpressionFactory();
                TreeValueExpression treeValueExpression = flowableExpressionFactory.createValueExpression(expressionManager.getElContext(executionEntity), expressionText, Object.class);
                expression = new JuelExpression(expressionManager, treeValueExpression, expressionText);
            }else {
                return result;
            }
        }
        result = (List<SimpleUserDto>) expression.getValue(executionEntity);
        return result;
    }

}
