package com.wangda.oa.modules.workflow.listener;

import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowNextDto;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description 自动设置处理人
 */
@Slf4j
@Component
public class TaskCreateListener implements TaskListener {

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private UserService sysUserService;

    private static TaskCreateListener taskCreateListener;

    // 解决监听器中Bean获取不到问题
    @PostConstruct
    public void init() {
        taskCreateListener = this;
        taskCreateListener.flowTaskService = this.flowTaskService;
        taskCreateListener.repositoryService = this.repositoryService;
        taskCreateListener.historyService = this.historyService;
        taskCreateListener.sysUserService = this.sysUserService;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        if(StringUtils.isNotBlank(delegateTask.getAssignee())) {
            return;
        }
        HistoricProcessInstance historicProcessInstance = taskCreateListener.historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(delegateTask.getProcessInstanceId())
                .singleResult();
        UserDto startUser = taskCreateListener.sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
        BpmnModel bpmnModel = taskCreateListener.repositoryService.getBpmnModel(delegateTask.getProcessDefinitionId());

        FlowNextDto flowNextDto = taskCreateListener.flowTaskService.currentUserTaskHandle(bpmnModel, delegateTask.getTaskDefinitionKey(), startUser, delegateTask.getExecutionId());
        if(Objects.nonNull(flowNextDto)) {
            delegateTask.setAssignee(flowNextDto.taskAssigneeFormatToString());
        }
        log.info("TaskCreateListener", delegateTask);
    }
}
