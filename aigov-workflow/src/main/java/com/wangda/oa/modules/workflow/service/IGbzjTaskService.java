package com.wangda.oa.modules.workflow.service;

import com.wangda.boot.platform.base.ResultJson;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/26
 * @description 干部之家接口
 */
public interface IGbzjTaskService {

    /**
     * OA任务推送
     * @param procInstId
     * @param title
     * @param sourceUrl   链接（任务详情-可办理/可查看任务）
     * @param executorId  执行人dingAccountId
     * @param priority    级别（普通/一般/紧急）
     * @param receiveTime 接收时间
     * @param sourceCode  来源code，（唯一标识: 来源任务id+来源应用）
     * @return ResultJson
     */
    ResultJson pushOaTask(String procInstId, String title, String sourceUrl, String executorId, String priority, String receiveTime, String sourceCode);

    /**
     * OA任务完成推送
     * @param procInstId
     * @param sourceCode 外部来源code（唯一标识: 来源任务id+来源应用）
     * @return ResultJson
     */
    ResultJson completeOaTask(String procInstId, String sourceCode);
}
