package com.wangda.oa.modules.workflow.bo.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/4/16 上午9:25
 */
@Data
public class WorkflowUserTaskButtonBO {
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "用户任务定义ID")
    private Long taskDefinitionId;

    @ApiModelProperty(value = "流程定义id")
    private String flowDefineId;

    @ApiModelProperty(value = "环节Key")
    private String linkKey;

    @ApiModelProperty(value = "操作按钮Key")
    private String buttonKey;

    @ApiModelProperty(value = "按钮图标(索引)")
    private String buttonIcon;

    @ApiModelProperty(value = "操作按钮名称")
    private String buttonName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否常用按钮(false:否,true:是)")
    private Boolean commonly;

    @ApiModelProperty(value = "按钮显示规则")
    private String buttonShowRule;
}
