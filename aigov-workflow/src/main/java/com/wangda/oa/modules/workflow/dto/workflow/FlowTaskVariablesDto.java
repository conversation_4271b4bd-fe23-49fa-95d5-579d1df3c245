package com.wangda.oa.modules.workflow.dto.workflow;

import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>工作流任务返回数据结构<p>
 *
 * <AUTHOR>
 * @date 2021-04-03
 */
@Data
@Builder
@ApiModel("工作流任务相关-返回数据结构")
public class FlowTaskVariablesDto implements Serializable {

    private static final long serialVersionUID = 710130391061438167L;

    @ApiModelProperty("表单样式")
    private String formSkin;
    private String formPcSkin;

    @ApiModelProperty("表单模版编号")
    private Long formTemplateId;

    @ApiModelProperty("表单结构")
    private String formJson;

    @ApiModelProperty("表单数据")
    private Object formData;

    @ApiModelProperty("表单读写隐状态")
    private FlowTaskFormRwDto taskFormRw;

    @ApiModelProperty("处理单")
    private FlowTaskProcessDto processJson;

    @ApiModelProperty("预签处理")
    private List<FlowTaskProcessDto> preSign;

    @ApiModelProperty("预签抄送处理")
    private List<FlowNextDto> preSignCopyTo;

    @ApiModelProperty("处理单数据")
    private List<FlowTaskDto> bpmData;

    @ApiModelProperty("抄送数据")
    private List<FlowTaskCopyToDto> copyToData;

    @ApiModelProperty("流程状态")
    private ProcStatusEnum procStatus;

    @ApiModelProperty("申请人信息")
    private FlowTaskApplicantDto applicantInfo;

    @ApiModelProperty("阅读状态")
    private Boolean userHasRead;

    @ApiModelProperty("后续预签节点数据")
    private List<FlowNextPreSignTaskDto> nextPreSignData;

    @ApiModelProperty("流程名称")
    private String processName;

    @ApiModelProperty("流程版本")
    private Integer processVersion;

    @ApiModelProperty("移动端表单json")
    private String mobileFormJson;

    private Long mobileFormTemplateId;
}
