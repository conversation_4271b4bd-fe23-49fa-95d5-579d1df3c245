package com.wangda.oa.modules.workflow.dto.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/3/28 15:50
 * @description 表单意见
 * {'remark':'','username','nickName','deptId','deptName','processedTime','fj'}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlowFormRemarkDto implements Serializable {

    private static final long serialVersionUID = 8923968005948770258L;

    /**
     * 意见
     */
    private String remark;

    /**
     * 用户
     */
    private String username;

    /**
     * 用户名称
     */
    private String nickName;

    /**
     * 部门编号
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 处理时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date processedTime;

    /**
     * 意见附件，多个逗号分隔
     */
    private String fj;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FlowFormRemarkDto that = (FlowFormRemarkDto) o;
        return username.equals(that.username) && nickName.equals(that.nickName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(username, nickName);
    }
}
