package com.wangda.oa.modules.workflow.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationTextBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.config.SendTaskMessageConfig;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.workflow.ProcessArchive;
import com.wangda.oa.modules.workflow.dto.workflow.ProcessCommentArchiveDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.factory.IFlowFormHandleFactory;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.service.ILabTaskService;
import com.wangda.oa.modules.workflow.service.ISysAppService;
import com.wangda.oa.modules.workflow.service.IZwddService;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.Process;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.event.impl.FlowableEntityEventImpl;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityImpl;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description 流程完成监听
 */
@Slf4j
@Component
public class FlowableGlobalProcessCompleteListener implements FlowableEventListener {

    @Autowired
    private FormTemplateService formTemplateService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private UserService sysUserService;

    @Autowired
    private FlowInstanceService flowInstanceService;

    @Autowired
    private RestUrlComponent restUrlComponent;

    @Autowired
    private SendTaskMessageConfig sendTaskMessageConfig;

    @Autowired
    private IFlowFormHandleFactory flowFormHandleFactory;

    // zwdd
    @Resource
    private ZwddProperties zwddProperties;
    @Autowired(required = false)
    private IZwddService zwddService;
    @Autowired(required = false)
    private ISysAppService sysAppService;

    @Autowired(required = false)
    private ILabTaskService labTaskService;


    @Override
    public void onEvent(FlowableEvent flowableEvent) {
//        if (flowableEvent instanceof FlowableProcessCancelledEventImpl) {
//            FlowableProcessCancelledEventImpl event = (FlowableProcessCancelledEventImpl) flowableEvent;
//        }

        FlowableEntityEventImpl event = (FlowableEntityEventImpl) flowableEvent;
        ExecutionEntityImpl executionEntity = (ExecutionEntityImpl) event.getEntity();

        if(Objects.isNull(executionEntity.getVariable(ProcessConstants.BPM_FORM_TEMPLATE_ID))) {
            throw new FlowableException("流程完成监听，获取模版编号出错");
        }

        // 意见归档
        List<ProcessCommentArchiveDto> commentArchives = new ArrayList<>();
        List<Comment> commentList = taskService.getProcessInstanceComments(executionEntity.getProcessInstanceId());
        for (Comment comment: commentList) {
            if (comment.getType() != ProcessConstants.PROCESS_COMMENT_TYPE_EVENT) {
                HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery().taskId(comment.getTaskId()).singleResult();
                UserDto sysUser = sysUserService.findByName(comment.getUserId());

                ProcessCommentArchiveDto processCommentArchiveDto = ProcessCommentArchiveDto.builder()
                        .type(comment.getType())
                        .comment(comment.getFullMessage())
                        .username(comment.getUserId())
                        .time(comment.getTime())
                        .taskId(comment.getTaskId())
                        .procInstanceId(comment.getProcessInstanceId())
                        .deptName(sysUser.getDept().getName())
                        .taskName(task.getName())
                        .procInstanceName(executionEntity.getName())
                        .build();
                commentArchives.add(processCommentArchiveDto);
            }
        }
        flowInstanceService.archiveComment(commentArchives);


        String procStatus = executionEntity.getProcessInstanceBusinessStatus();
        if(Objects.isNull(procStatus) || ProcStatusEnum.INPROGRESS.getValue().equals(procStatus)) {
            procStatus = ProcStatusEnum.TG.getValue();
        }

        // 流程信息固化
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(executionEntity.getProcessInstanceId()).singleResult();
        UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
        ProcessArchive processArchive = ProcessArchive.builder()
                .username(startUser.getUsername())
                .deptName(startUser.getDept().getName())
                .startTime(historicProcessInstance.getStartTime())
                .endTime(new Date())
                .procInstanceId(executionEntity.getProcessInstanceId())
                .procInstanceName(historicProcessInstance.getName())
                .procDefKey(historicProcessInstance.getProcessDefinitionKey())
                .status(procStatus)
                .build();
        flowInstanceService.archiveProcess(processArchive);

        try {
            // 完成处理自定义业务数据
            flowFormHandleFactory.handleFlowBizData(executionEntity.getProcessInstanceId(), historicProcessInstance.getProcessDefinitionKey(), ProcStatusEnum.getProcStatusEnumByValue(procStatus));

            // 发送政务钉钉通知，发送表单打印地址
            if(sendTaskMessageConfig.getSendZwdd()) {
                // bpm定义模型
                BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
                Process process = bpmnModel.getProcess(null);
                String processPrintTemplateKey = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_PRINT_TEMPLATE_KEY);

                // 出差报销、公差备案审核通过抄送对应流程管理员
                if(StringUtils.isNotBlank(processPrintTemplateKey) && procStatus.equals(ProcStatusEnum.TG.getValue())) {
                    SysUserPlatform creatorUserPlatform = sysAppService.getByUserId(historicProcessInstance.getStartUserId(), zwddProperties.getType());
                    if(Objects.isNull(creatorUserPlatform)) {
                        throw new CustomException("流程完成通知发送错误，没有平台流程发起人"+executionEntity.getStartUserId());
                    }

                    StringBuilder notificationSubject = new StringBuilder(DateUtil.format(historicProcessInstance.getStartTime(), "yyyy.MM.dd"))
                            .append("提交的")
                            .append(executionEntity.getName())
                            .append("已审核完成，请到电脑端下载打印 ");

                    String notificationSubjectContent = notificationSubject.insert(0, "您").toString();
                    WorkNotificationTextBO workNotificationTextBO = WorkNotificationTextBO.builder()
                            .content(notificationSubjectContent)
                            .build();
                    WorkNotificationBO workNotificationBO = WorkNotificationBO.builder()
                            .receiverIds(creatorUserPlatform.getPlatformUserId())
                            .bizMsgId(executionEntity.getId())
                            .type(0)
                            .textBO(workNotificationTextBO)
                            .build();

                    // 给发起人发送通知
                    zwddService.workNotification(workNotificationBO);

                    //获取对应的流程管理员
                    List<User> users = sysUserService.findByRoleAuthorityKey(historicProcessInstance.getProcessDefinitionName());
                    if(!CollectionUtils.isEmpty(users)){
                        StringBuilder printUrlBuilder = new StringBuilder(restUrlComponent.getServerUrl())
                                .append("/ureport/pdf/show?_u=file:")
                                .append(processPrintTemplateKey)
                                .append(".ureport.xml&procInstId=")
                                .append(executionEntity.getProcessInstanceId());

                        StringBuilder sysUserPlatformIds = new StringBuilder();
                        for (User user:users) {
                            SysUserPlatform sysUserPlatform = sysAppService.getByUserNameAndType(user.getUsername(),zwddProperties.getType());
                            sysUserPlatformIds.append(sysUserPlatform.getId()).append(",");
                        }
                        if(sysUserPlatformIds.length() > 0) {
                            sysUserPlatformIds = sysUserPlatformIds.deleteCharAt(sysUserPlatformIds.length()-1);
                            String notificationSubjectForAdmin = notificationSubject.insert(0, creatorUserPlatform.getPlatformUserName()).append(printUrlBuilder).toString();

                            WorkNotificationTextBO workNotificationTextBOForAdmin = WorkNotificationTextBO.builder()
                                    .content(notificationSubjectForAdmin)
                                    .build();
                            WorkNotificationBO workNotificationBOForAdmin = WorkNotificationBO.builder()
                                    .receiverIds(sysUserPlatformIds.toString())
                                    .bizMsgId(executionEntity.getId())
                                    .type(0)
                                    .textBO(workNotificationTextBOForAdmin)
                                    .build();
                            //给对应流程管理员发送通知
                            zwddService.workNotification(workNotificationBOForAdmin);
                        }
                    }
                }
            }

            // 发送之江实验室
            if(sendTaskMessageConfig.getSendZhejianglab()) {

                // 更新流程实例状态
                String completeBpmResult = labTaskService.updateBpmInstance(Long.parseLong(executionEntity.getProcessInstanceId()), 1, 1);
                log.info("完成流程结果：" + completeBpmResult);

                //主送和分发人员创建待阅任务
                String createReadResult = labTaskService.createBpmReadTask(Long.parseLong(executionEntity.getProcessInstanceId()));
                log.info("主送分发人员创建待阅结果：" + createReadResult);

                // 表单数据
                String formData = JSON.toJSONString(executionEntity.getVariable(ProcessConstants.BPM_FORM_DATA));
                JSONObject formDataJsonObj = new JSONObject(formData);
                JSONObject zwJsonObj = formDataJsonObj.getJSONObject("zw");
                //当前表单最新正文id
                if(zwJsonObj != null){
                    Long storageId = zwJsonObj.getLong("storageId");
                    //当前表单的是否推送实验室发文字段
                    String push = formDataJsonObj.getStr("push");

                    // 推送实验室发文
                    if("是".equals(push)) {
                        String pushResult = labTaskService.pushLabDocument(Long.parseLong(executionEntity.getProcessInstanceId()), storageId);
                        log.info("推送实验室发文结果：" + pushResult);
                    }
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
            log.error("发送通知，发送表单打印地址错误" + e.getMessage());
        }

        // 如果状态不在进行中说明已设值，返回
        if(!(ProcStatusEnum.INPROGRESS.getValue().equals(procStatus)) && !(ProcStatusEnum.TG.getValue().equals(procStatus))) {
            return;
        }

        // 更新表单状态
        try {
            runtimeService.updateBusinessStatus(executionEntity.getProcessInstanceId(), procStatus);
            String templateId = executionEntity.getVariable(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString();
            formTemplateService.updateFormStatus(Long.parseLong(templateId), executionEntity.getProcessInstanceId(), ProcStatusEnum.valueOf(procStatus));
        }catch (Exception e) {
            e.printStackTrace();
            throw new FlowableException("更新流程表单状态出错", e);
        }

    }

    @Override
    public boolean isFailOnException() {
        return false;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }
}
