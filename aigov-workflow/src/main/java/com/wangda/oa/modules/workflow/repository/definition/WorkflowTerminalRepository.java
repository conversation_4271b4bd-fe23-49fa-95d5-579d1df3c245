package com.wangda.oa.modules.workflow.repository.definition;

import com.wangda.oa.modules.workflow.domain.definition.WorkflowTerminal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/4/14 上午11:07
 */
public interface WorkflowTerminalRepository extends JpaRepository<WorkflowTerminal,Long>, JpaSpecificationExecutor<WorkflowTerminal> {

    /**
     * 根据定义id删除数据
     * @param flowDefineId
     */
    @Transactional
    void deleteByFlowDefineId(String flowDefineId);
}
