package com.wangda.oa.modules.workflow.domain.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR> leec
 * @description: 加减签Vo
 * @date : 2021/07/15
 */
@Getter
@Setter
public class AddSignTaskVo {

    @NotBlank
    @ApiModelProperty("流程实例Id")
    private String processInstanceId;

    @NotBlank
    @ApiModelProperty("目标节点")
    private String targetTaskKey;

    @ApiModelProperty("处理用户 username,node")
    private List<TaskAssigneeVo> taskAssignee;
}
