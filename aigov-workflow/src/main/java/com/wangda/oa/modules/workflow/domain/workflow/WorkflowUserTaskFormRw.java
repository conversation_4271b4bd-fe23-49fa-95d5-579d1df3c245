package com.wangda.oa.modules.workflow.domain.workflow;

import com.wangda.boot.platform.base.BaseDomain;
import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * 流程-用户任务表单读写定义表
 *
 * <AUTHOR>
 * @date 2021/4/15下午7:47
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_workflow_user_task_form_rw")
public class WorkflowUserTaskFormRw extends BaseDomain {

    @Column(name = "task_definition_id")
    @ApiModelProperty(value = "用户任务定义ID")
    private Long taskDefinitionId;

    @Column(name = "flow_define_id")
    @ApiModelProperty(value = "流程部署id")
    private String flowDefineId;

    @Column(name = "link_key")
    @ApiModelProperty(value = "环节Key")
    private String linkKey;

    @ApiModelProperty(value = "表单模板id")
    @OneToOne
    @org.hibernate.annotations.ForeignKey(name = "none")
    private WdFormTemplate template;

    @Column(name = "sub_form_id")
    @ApiModelProperty(value = "子表单标识")
    private String subFormId;

    @Column(name = "form_field_identifier")
    @ApiModelProperty(value = "表单字段标识")
    private String formFieldIdentifier;

    @Column(name = "read_state")
    @ApiModelProperty(value = "读写隐状态(R:读,W:写,H:隐藏)")
    private String readState;

}
