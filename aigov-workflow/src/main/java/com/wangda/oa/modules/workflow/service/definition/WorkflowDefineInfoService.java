package com.wangda.oa.modules.workflow.service.definition;

import com.wangda.oa.modules.workflow.bo.flow.InfoBO;
import com.wangda.oa.modules.workflow.bo.flow.ListBO;
import com.wangda.oa.modules.workflow.bo.flow.WorkflowDefineInfoBO;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowClassify;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowDefineInfo;
import com.wangda.oa.modules.workflow.dto.workflow.WorkflowDefineInfoListDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/13 下午8:09
 */
public interface WorkflowDefineInfoService {

    /**
     * 新增或修改定义表
     * @param bo
     * @return
     */
    Long addOrUpdate(WorkflowDefineInfoBO bo);

    /**
     * 查询流程定义列表
     * @param bo
     * @return
     */
    WorkflowDefineInfoListDto getList(ListBO bo);

    /**
     * 查询流程定义详情
     * @param bo
     * @return
     */
    WorkflowDefineInfo getById(InfoBO bo);

    /**
     * 获取类别集合
     * @return
     */
    List<WorkflowClassify> getClassifyList();

    /**
     * 删除
     * @param bo
     * @return
     */
    Boolean delete(WorkflowDefineInfoBO bo);
}
