package com.wangda.oa.modules.workflow.service.definition.impl;

import com.wangda.oa.modules.workflow.domain.definition.WorkflowDeployForm;
import com.wangda.oa.modules.workflow.domain.workflow.vo.DeployFormVo;
import com.wangda.oa.modules.workflow.repository.definition.WorkflowDeployFormRepository;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.service.definition.WorkflowDeployFormService;
import com.wangda.oa.utils.ValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 流程实例关联表单Service业务层处理
 * <AUTHOR>
 * @date 2021-04-03
 */
@Service
public class WorkflowDeployFormServiceImpl implements WorkflowDeployFormService {

    @Autowired
    private WorkflowDeployFormRepository workflowDeployFormRepository;

    @Autowired
    private WdFormTemplateRepository wdFormTemplateRepository;

    /**
     * 查询流程实例关联表单
     *
     * @param id 流程实例关联表单ID
     * @return 流程实例关联表单
     */
    @Override
    public WorkflowDeployForm selectWorkflowDeployFormById(Long id)
    {
        return null; // sysDeployFormMapper.selectWorkflowDeployFormById(id);
    }

    /**
     * 查询流程实例关联表单列表
     *
     * @param workflowDeployForm 流程实例关联表单
     * @return 流程实例关联表单
     */
    @Override
    public List<WorkflowDeployForm> selectWorkflowDeployFormList(WorkflowDeployForm workflowDeployForm)
    {
        return null; //sysDeployFormMapper.selectWorkflowDeployFormList(sysDeployForm);
    }

    /**
     * 新增流程实例关联表单
     *
     * @param workflowDeployForm 流程实例关联表单
     * @return 结果
     */
    @Override
    public int insertWorkflowDeployForm(WorkflowDeployForm workflowDeployForm)
    {
        return 0; //sysDeployFormMapper.insertWorkflowDeployForm(sysDeployForm);
    }

    /**
     * 修改流程实例关联表单
     *
     * @param workflowDeployForm 流程实例关联表单
     * @return 结果
     */
    @Override
    public int updateWorkflowDeployForm(WorkflowDeployForm workflowDeployForm)
    {
        return 0; //sysDeployFormMapper.updateWorkflowDeployForm(sysDeployForm);
    }

    /**
     * 批量删除流程实例关联表单
     *
     * @param ids 需要删除的流程实例关联表单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkflowDeployFormByIds(Long[] ids)
    {
        return 0; //sysDeployFormMapper.deleteWorkflowDeployFormByIds(ids);
    }

    /**
     * 删除流程实例关联表单信息
     *
     * @param id 流程实例关联表单ID
     * @return 结果
     */
    @Override
    @Transactional
    public void deleteWorkflowDeployFormById(Long id) {
        workflowDeployFormRepository.deleteById(id);
    }

    /**
     * 查询流程挂着的表单
     *
     * @param deployId
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public DeployFormVo selectWorkflowDeployFormByDeployId(String deployId) {
        return workflowDeployFormRepository.findByDeployId(deployId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDeployForm(WorkflowDeployForm resources) {
        workflowDeployFormRepository.save(resources);
    }

    @Override
    public void updateDeployForm(WorkflowDeployForm resources){
        WorkflowDeployForm workflowDeployForm = workflowDeployFormRepository.findById(resources.getId()).orElseGet(WorkflowDeployForm::new);
        ValidationUtil.isNull(workflowDeployForm.getId(), "workflowDeployForm", "formId", resources.getId());
        workflowDeployForm.copy(resources);
        workflowDeployFormRepository.save(workflowDeployForm);
    }
}
