package com.wangda.oa.modules.workflow.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-04-03
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流任务用户相关-返回参数")
public class FlowTaskUserDto implements Serializable {

    @ApiModelProperty("执行人Id")
    private Long assigneeId;

    @ApiModelProperty("执行人用户名")
    private String assigneeUsername;

    @ApiModelProperty("执行人名称")
    private String assigneeName;

    @ApiModelProperty("部门名称")
    private String deptName;

}
