package com.wangda.oa.modules.workflow.domain.form.mapstruct;

import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
public class UserNameMapperUtil {

    @Autowired
    private UserService userService;

    public SimpleUserDto toUserDto(String operator) {
        if(StringUtils.isEmpty(operator)) {
            return new SimpleUserDto();
        }
        UserDto user = userService.findByName(operator);
        if(Objects.nonNull(user)) {
            SimpleUserDto simpleUserDto = SimpleUserDto.builder()
                    .id(user.getId())
                    .userName(user.getUsername())
                    .nickName(user.getNickName())
                    .build();
            return simpleUserDto;
        }else {
            return new SimpleUserDto();
        }
    }
}
