package com.wangda.oa.modules.workflow.service.form.impl;

import com.wangda.oa.modules.workflow.domain.form.HistoricalFlowFormData;
import com.wangda.oa.modules.workflow.repository.form.HistoricalFlowFormDataRepository;
import com.wangda.oa.modules.workflow.service.form.HistoricalFlowFormDataService;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class HistoricalFlowFormDataServiceImpl implements HistoricalFlowFormDataService {

    @Resource
    private HistoricalFlowFormDataRepository historicalFlowFormDataRepository;

    @Override
    public HistoricalFlowFormData save(HistoricalFlowFormData historicalFlowFormData) {
        if(StringUtils.isEmpty(historicalFlowFormData.getOperator())) {
            historicalFlowFormData.setOperator(SecurityUtils.getCurrentUsername());
        }
        return historicalFlowFormDataRepository.save(historicalFlowFormData);
    }

    @Override
    public Page queryAllByProcInstId(String procInstId, Pageable pageable) {
        return historicalFlowFormDataRepository.findAllByProcInstIdOrderByCreateDateDesc(procInstId, pageable);
    }

    @Override
    public HistoricalFlowFormData getHistoricalFormData(Long id) {
        return historicalFlowFormDataRepository.getOne(id);
    }

    @Override
    public void deleteAllFormDataByProcInstId(String procInstId) {
        historicalFlowFormDataRepository.deleteAllByProcInstId(procInstId);
    }

    @Override
    public void deleteById(Long id) {
        historicalFlowFormDataRepository.deleteById(id);
    }
}
