package com.wangda.oa.modules.workflow.domain.form;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

/**
 * 表单模板
 *
 * <AUTHOR>
 * @date 2020/12/22下午02:06
 */
@Data
@Entity
@Table(name = "wd_form_template")
public class WdFormTemplate extends BaseDomain {

    @Column(name = "title")
    @ApiModelProperty(value = "命名")
    private String title;

    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @Column(name = "form_json")
    @ApiModelProperty(value = "表单JSON数据")
    @Lob
    private String formJson;

    @Column(name = "user_id")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @Column(name = "class_name")
    @ApiModelProperty(value = "对应的实体类名")
    private String className;

    @Column(name = "shared")
    @ApiModelProperty(value = "是否共享(0:不共享,1:共享)")
    private Integer shared=0;

    @Column(name = "creator_name")
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    @Column(name = "category")
    @ApiModelProperty(value = "类别")
    private String category;

    @Column(name = "app_path")
    @ApiModelProperty(value = "移动端")
    private String appPath;
}
