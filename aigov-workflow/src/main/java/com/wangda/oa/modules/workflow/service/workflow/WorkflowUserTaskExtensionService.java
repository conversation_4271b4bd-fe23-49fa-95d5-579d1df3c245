package com.wangda.oa.modules.workflow.service.workflow;

import com.wangda.oa.modules.workflow.bo.flow.*;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import com.wangda.oa.modules.workflow.dto.workflow.UserTaskListDto;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午8:33
 */
public interface WorkflowUserTaskExtensionService {

    /**
     * 新增或修改用户任务定义扩展
     * @param bo
     * @return
     */
    Long addOrUpdateExtension(WorkflowUserTaskExtensionBO bo);

    /**
     * 新增或修改用户任务操作按钮定义
     * @param bo
     * @return
     */
    Long addOrUpdateButton(WorkflowUserTaskButtonBO bo);

    /**
     * 新增或修改用户任务表单读写定义
     * @param bo
     * @return
     */
    Long addOrUpdateFormRw(WorkflowUserTaskFormRwBO bo);

    /**
     * 查询用户任务定义扩展列表
     * @param bo
     * @return
     */
    UserTaskListDto getList(ListBO bo);

    /**
     * 删除用户任务定义扩展
     * @param bo
     * @return
     */
    Boolean deleteExtension(InfoBO bo);

    /**
     * 删除用户任务表单读写定义
     * @param bo
     * @return
     */
    Boolean deleteFormRw(InfoBO bo);

    /**
     * 删除用户任务操作按钮定义
     * @param bo
     * @return
     */
    Boolean deleteButton(InfoBO bo);

    /**
     * 根据key查询详情
     * @param bo
     * @return
     */
    WorkflowUserTaskExtension getInfoByKey(GetInfoByKeyBO bo);

}
