package com.wangda.oa.modules.workflow.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description
 */
@Slf4j
public class ProcStartListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) {
        log.info("ProcStartListener", delegateExecution);

    }
}
