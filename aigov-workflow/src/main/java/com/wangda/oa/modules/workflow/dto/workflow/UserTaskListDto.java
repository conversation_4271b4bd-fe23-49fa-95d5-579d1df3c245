package com.wangda.oa.modules.workflow.dto.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/16 上午10:40
 */
@Data
public class UserTaskListDto {
    @ApiModelProperty(value = "内容")
    private List<WorkflowUserTaskExtension> content;
    @ApiModelProperty(value = "总数")
    private Long totalElements;
}
