package com.wangda.oa.modules.workflow.dto.workflow;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/3/28 19:48
 */
@Data
public class FlowSaveXmlVo implements Serializable {

    /**
     * 流程部署key
     */
    private String deployId;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 流程分类
     */
    private String category;

    /**
     * xml 文件
     */
    private String xml;

    /**
     * 是否覆盖
     */
    private Boolean override = false;
}
