package com.wangda.oa.modules.workflow.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-01-14
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流任务办理人")
public class FlowTaskAssigneeDto implements Serializable {

    private static final long serialVersionUID = 6817447358143013723L;

    @ApiModelProperty("任务Id")
    private String taskId;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务key")
    private String taskDefKey;

    @ApiModelProperty("执行人名称")
    private String assigneeName;

    @ApiModelProperty("流程Id")
    private String procInstId;

    @ApiModelProperty("状态,1:进行中；2:锁定")
    private int status;

}
