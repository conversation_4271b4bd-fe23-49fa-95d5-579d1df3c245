package com.wangda.oa.modules.workflow.domain.workflow;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/8
 * @description 流程信息归档
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Builder
public class ProcessArchive extends BaseDomain {

    @ApiModelProperty(value = "提交人用户名")
    private String username;

    @ApiModelProperty(value = "提交人部门名称")
    private String deptName;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "完成时间")
    private Date endTime;

    @ApiModelProperty(value = "状态", notes = "ProcStatusEnum")
    private String status;

    @ApiModelProperty(value = "流程实例名称")
    private String procInstanceName;

    @ApiModelProperty(value = "流程实例ID")
    @Column(unique = true)
    private String procInstanceId;

    @ApiModelProperty(value = "流程定义Key")
    private String procDefKey;

}
