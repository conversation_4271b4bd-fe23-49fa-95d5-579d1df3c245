package com.wangda.oa.modules.workflow.enums.workflow;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> leec
 * @description: 流程消息通知方式
 * @date : 2021/09/03
 */
@Getter
@AllArgsConstructor
public enum TaskRemindWayEnum {

    ZWDD("zwdd-ding", "政务钉钉"),
    SMS("sms", "短信");

    private String value;
    private String name;

    /**
     * 通过type获取
     * @param type
     * @return
     */
    public static TaskRemindWayEnum getEnumInfoByType(String type) {
        for (TaskRemindWayEnum e : TaskRemindWayEnum.values()) {
            if (e.name().equals(type)) {
                return e;
            }
        }
        return null;
    }
}
