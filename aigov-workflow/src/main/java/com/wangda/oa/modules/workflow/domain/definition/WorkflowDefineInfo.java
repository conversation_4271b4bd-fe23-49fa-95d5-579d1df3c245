package com.wangda.oa.modules.workflow.domain.definition;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;

/**
 * 流程定义表
 *
 * <AUTHOR>
 * @date 2021/4/13下午6:42
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_workflow_define_info")
public class WorkflowDefineInfo extends BaseDomain {

    @Column(name = "process_definition_id")
    @ApiModelProperty(value = "流程总标识(XXX:XX:XX)")
    private String processDefinitionId;

    @Column(name = "process_definition_key")
    @ApiModelProperty(value = "流程标识")
    private String processDefinitionKey;

    @Column(name = "version_number")
    @ApiModelProperty(value = "版本号")
    private Integer versionNumber;

    @Column(name = "latest_version")
    @ApiModelProperty(value = "最新版本(0:否,1:是)")
    private Integer latestVersion;

    @Column(name = "name")
    @ApiModelProperty(value = "流程名称")
    private String name;

    @ApiModelProperty(value = "流程分类")
    @OneToOne
    @org.hibernate.annotations.ForeignKey(name = "none")
    private WorkflowClassify classify;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Column(name = "mainProcess")
    @ApiModelProperty(value = "是否主流程(0:否,1:是)")
    private Integer mainProcess;

    @Column(name = "icon")
    @ApiModelProperty(value = "流程图标id(看菜单图标怎么存)")
    private String icon;

    @ApiModelProperty(value = "多端支持")
    @OneToMany(targetEntity = WorkflowTerminal.class)
    @JoinColumn(name="flow_define_id")
    @org.hibernate.annotations.ForeignKey(name = "none")
    private List<WorkflowTerminal> terminal;

    @Column(name = "state")
    @ApiModelProperty(value = "状态(0:禁用,1:启用)")
    private Integer state;

    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @Column(name = "form_key")
    @ApiModelProperty(value = "流转表单")
    private String formKey;

    @Column(name = "print_template")
    @ApiModelProperty(value = "打印模板(id或url)")
    private String printTemplate;

    @Column(name = "customize_backend_api")
    @ApiModelProperty(value = "自定义后端api")
    private String customizeBackendApi;

    @Column(name = "backlog_title_rule")
    @ApiModelProperty(value = "待办标题规则")
    private String backlogTitleRule;

    @Column(name = "backlog_extension_rule")
    @ApiModelProperty(value = "待办扩展属性规则")
    private String backlogExtensionRule;

    @Column(name = "deal_with_type")
    @ApiModelProperty(value = "办理时限类型(0:工作日,1:自然日)")
    private Integer dealWithType;

    @Column(name = "deal_with_time")
    @ApiModelProperty(value = "办理时限")
    private Integer dealWithTime;

    @Column(name = "process_manager")
    @ApiModelProperty(value = "流程管理人")
    private String processManager;

    @Column(name = "process_start")
    @ApiModelProperty(value = "流程启动人")
    private String processStart;

    @Column(name = "process_consult")
    @ApiModelProperty(value = "流程查阅人")
    private String processConsult;
}
