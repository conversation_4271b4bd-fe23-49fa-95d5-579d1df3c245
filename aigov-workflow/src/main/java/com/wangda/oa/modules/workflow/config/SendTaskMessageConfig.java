package com.wangda.oa.modules.workflow.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 是否发送消息
 */

@Data
@Component
@ConfigurationProperties(prefix = "flowable")
public class SendTaskMessageConfig {

    /**
     * 政务钉钉
     */
    private Boolean sendZwdd = Boolean.FALSE;

    /**
     * 统一待办
     */
    private Boolean sendBacklog = Boolean.FALSE;

    /**
     * 之江实验室
     */
    private Boolean sendZhejianglab = Boolean.FALSE;

    /**
     * 干部之家
     */
    private Boolean sendGbzjoa = Boolean.FALSE;

}
