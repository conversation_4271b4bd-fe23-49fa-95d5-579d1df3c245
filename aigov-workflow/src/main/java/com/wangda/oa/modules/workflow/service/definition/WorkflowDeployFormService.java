package com.wangda.oa.modules.workflow.service.definition;


import com.wangda.oa.modules.workflow.domain.definition.WorkflowDeployForm;
import com.wangda.oa.modules.workflow.domain.workflow.vo.DeployFormVo;

import java.util.List;

/**
 * 流程实例关联表单Service接口
 * <AUTHOR>
 * @date 2021-04-03
 */
public interface WorkflowDeployFormService
{
    /**
     * 查询流程实例关联表单
     *
     * @param id 流程实例关联表单ID
     * @return 流程实例关联表单
     */
    public WorkflowDeployForm selectWorkflowDeployFormById(Long id);

    /**
     * 查询流程实例关联表单列表
     *
     * @param workflowDeployForm 流程实例关联表单
     * @return 流程实例关联表单集合
     */
    public List<WorkflowDeployForm> selectWorkflowDeployFormList(WorkflowDeployForm workflowDeployForm);

    /**
     * 新增流程实例关联表单
     *
     * @param workflowDeployForm 流程实例关联表单
     * @return 结果
     */
    public int insertWorkflowDeployForm(WorkflowDeployForm workflowDeployForm);

    /**
     * 修改流程实例关联表单
     *
     * @param workflowDeployForm 流程实例关联表单
     * @return 结果
     */
    public int updateWorkflowDeployForm(WorkflowDeployForm workflowDeployForm);

    /**
     * 批量删除流程实例关联表单
     *
     * @param ids 需要删除的流程实例关联表单ID
     * @return 结果
     */
    public int deleteWorkflowDeployFormByIds(Long[] ids);

    /**
     * 删除流程实例关联表单信息
     *
     * @param id 流程实例关联表单ID
     * @return 结果
     */
    public void deleteWorkflowDeployFormById(Long id);

    /**
     * 查询流程挂着的表单
     * @param deployId
     * @return
     */
    DeployFormVo selectWorkflowDeployFormByDeployId(String deployId);

    /**
     * 保存部署表单数据
     * @param resources
     */
    void saveDeployForm(WorkflowDeployForm resources);

    /**
     * Description: 修改表单数据
     * @param resources
     * @return: void
     * @Date: 2021/7/7 11:12
     * @Author: maogy
     * @throws:
     */
    void updateDeployForm(WorkflowDeployForm resources);
}
