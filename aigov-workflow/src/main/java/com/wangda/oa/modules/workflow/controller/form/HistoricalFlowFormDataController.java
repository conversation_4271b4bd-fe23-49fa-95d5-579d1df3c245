package com.wangda.oa.modules.workflow.controller.form;

import com.wangda.boot.platform.utils.PageUtil;
import com.wangda.oa.modules.workflow.domain.form.HistoricalFlowFormData;
import com.wangda.oa.modules.workflow.domain.form.mapstruct.HistoricalFlowFormDataMapper;
import com.wangda.oa.modules.workflow.service.form.HistoricalFlowFormDataService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 历史业务表单数据
 * <AUTHOR>
 * @date 2022/04/01 14:52
 */
@Slf4j
@RequestMapping("/api/historical/formdata")
@RestController
@CrossOrigin
@Validated
public class HistoricalFlowFormDataController {

    @Resource
    private HistoricalFlowFormDataService historicalFlowFormDataService;

    @Resource
    private HistoricalFlowFormDataMapper historicalFlowFormDataMapper;

    @ApiOperation(value = "保存业务表单历史版本数据")
    @PostMapping
    public ResponseEntity save(@RequestBody HistoricalFlowFormData flowFormData) {
        HistoricalFlowFormData result = historicalFlowFormDataService.save(flowFormData);
        return ResponseEntity.ok(result);
    }

    @ApiOperation(value = "根据流程实例id获取表单历史版本数据")
    @GetMapping(value = "/queryHistoricalFormData")
    public ResponseEntity queryHistoricalFormData(@RequestParam("procInstId") String procInstId, Pageable pageable) {
        Page pageList = historicalFlowFormDataService.queryAllByProcInstId(procInstId, pageable);
        return ResponseEntity.ok(PageUtil.toPage(historicalFlowFormDataMapper.toDto(pageList.getContent()), pageList.getTotalElements()));
    }

    @ApiOperation(value = "根据ID获取业务表单历史数据")
    @GetMapping("/queryById/{id}")
    public ResponseEntity queryById(@PathVariable("id") Long id) {
        HistoricalFlowFormData historicalFlowFormData = historicalFlowFormDataService.getHistoricalFormData(id);
        return ResponseEntity.ok(historicalFlowFormData);
    }

    @ApiOperation(value = "根据流程实例id删除该流程所有表单历史记录")
    @DeleteMapping(value = "/deleteAllFormDataByProcInstId")
    public ResponseEntity deleteAllFormDataByProcInstId(@RequestParam("procInstId") String procInstId) {
        historicalFlowFormDataService.deleteAllFormDataByProcInstId(procInstId);
        return ResponseEntity.ok("删除成功");
    }


    @ApiOperation(value = "删除表单历史记录")
    @DeleteMapping("/deleteById/{id}")
    public ResponseEntity deleteById(@PathVariable("id") Long id){
        historicalFlowFormDataService.deleteById(id);
        return ResponseEntity.ok("删除成功");
    }
}
