package com.wangda.oa.modules.workflow.controller.workflow;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.platform.utils.PageUtil;
import com.wangda.oa.modules.workflow.domain.workflow.vo.FlowRemarkVo;
import com.wangda.oa.modules.workflow.domain.workflow.vo.FlowTaskVo;
import com.wangda.oa.modules.workflow.domain.workflow.vo.ProcessQueryVo;
import com.wangda.oa.modules.workflow.domain.workflow.vo.TaskUserReadVo;
import com.wangda.oa.modules.workflow.dto.workflow.FlowProcessInfoDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskAssigneeDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskVariablesDto;
import com.wangda.oa.modules.workflow.factory.AfterTransactionOpt;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.modules.workflow.utils.FlowDiagramUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.*;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "流程实例接口")
@RestController
@CrossOrigin
@RequestMapping("/api/bpm/instance")
public class FlowInstanceController {

    // 流程实例服务
    @Autowired
    private RuntimeService runtimeService;

    // 流程节点任务服务
    @Autowired
    private TaskService taskService;

    // 流程节点任务服务
    @Autowired
    private FlowInstanceService flowInstanceService;

    // 部署服务
    @Autowired
    private RepositoryService repositoryService;

    // 流程引擎
    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private FlowDiagramUtils flowDiagramUtils;

    @ApiOperation(value = "我的流程(根据角色获取)", response = FlowTaskDto.class)
    @GetMapping(value = "/myProcess")
    public ResponseEntity<Object> myProcess(@ApiParam(value = "流程查询参数") ProcessQueryVo query) {
        Page pageList =  flowInstanceService.myProcess(query);
        return ResponseEntity.ok(PageUtil.toPage(pageList.getRecords(), pageList.getTotal()));
    }

    @ApiOperation(value = "查询流程列表", response = FlowTaskDto.class)
    @PostMapping(value = "/queryProcess")
    public ResponseEntity<Object> queryProcess(@ApiParam(value = "流程查询参数", required = true) @RequestBody ProcessQueryVo query) {
        Page pageList =  flowInstanceService.queryProcess(query);
        return ResponseEntity.ok(PageUtil.toPage(pageList.getRecords(), pageList.getTotal()));
    }

    @ApiOperation(value = "删除流程实例")
    @DeleteMapping(value = "/delete")
    public ResultJson delete(@ApiParam(value = "流程实例ID", required = true) @RequestParam String instanceIds,
                             @ApiParam(value = "删除原因") @RequestParam(required = false) String deleteReason) {
        flowInstanceService.delete(instanceIds, deleteReason);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "流程历史流转记录", response = FlowTaskVariablesDto.class)
    @GetMapping(value = "/flowRecord")
    public ResultJson flowRecord(String procInsId) {
        return flowInstanceService.flowRecord(procInsId);
    }

    @ApiOperation(value = "撤回流程", response = ResultJson.class)
    @PostMapping(value = "/revokeProcess")
    public ResultJson revokeProcess(@RequestBody FlowTaskVo flowTaskVo) {
        return flowInstanceService.revokeProcess(flowTaskVo);
    }

    @ApiOperation(value = "保存已阅", response = ResultJson.class)
    @PostMapping(value = "/saveHasRead")
    public ResultJson saveHasRead(@RequestBody TaskUserReadVo taskUserReadVo) {
        flowInstanceService.saveHasRead(taskUserReadVo);
        return ResultJson.generateResult("保存已阅成功");
    }

    @ApiOperation(value = "获取恢复流程所有可选择的节点")
    @GetMapping(value = "/reactivateTaskList/{procInstanceId}")
    public ResultJson findReactivateTaskList(@PathVariable(value = "procInstanceId") String procInstanceId) {
        return flowInstanceService.findReactivateTaskList(procInstanceId);
    }

    @ApiOperation(value = "重新恢复流程", response = ResultJson.class)
    @PostMapping(value = "/reactivateProcess")
    public ResultJson reactivateProcess(@RequestBody FlowTaskVo flowTaskVo) throws Exception {
        return flowInstanceService.reactivateProcess(flowTaskVo);
    }

    @ApiOperation(value = "获取流程任务节点处理人")
    @GetMapping(value = "/getTaskAssignees/{procInstanceId}")
    public ResultJson getTaskAssignees(@PathVariable(value = "procInstanceId") String procInstanceId) {
        return flowInstanceService.getTaskAssignees(procInstanceId);
    }

    @ApiOperation(value = "同步流程表单意见")
    @GetMapping(value = "/syncFormRemark/{procInstanceId}")
    public ResultJson syncFormRemark(@PathVariable(value = "procInstanceId") @NotBlank String procInstanceId, Boolean isAll) {
        return flowInstanceService.syncFormRemark(procInstanceId, isAll);
    }

    /**
     * 生成流程图
     *
     * @param processId 任务ID
     */
    @RequestMapping("/diagram/{processId}")
    public void genProcessDiagramImg(HttpServletResponse response, @PathVariable("processId") String processId) {
        InputStream inputStream =  flowInstanceService.diagram(processId);
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(inputStream);
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
                if(inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 生成流程图
     *
     * @param processId 任务ID
     */
    @RequestMapping(value = "/processDiagram")
    public void genProcessDiagram(HttpServletResponse httpServletResponse, String processId) throws Exception {
        ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processId).singleResult();

        //流程走完的不显示图
        if (pi == null) {
            return;
        }
        Task task = taskService.createTaskQuery().processInstanceId(pi.getId()).singleResult();
        //使用流程实例ID，查询正在执行的执行对象表，返回流程实例对象
        String InstanceId = task.getProcessInstanceId();
        List<Execution> executions = runtimeService
                .createExecutionQuery()
                .processInstanceId(InstanceId)
                .list();

        //得到正在执行的Activity的Id
        List<String> activityIds = new ArrayList<>();
        List<String> flows = new ArrayList<>();
        for (Execution exe : executions) {
            List<String> ids = runtimeService.getActiveActivityIds(exe.getId());
            activityIds.addAll(ids);
        }

        //获取流程图
        BpmnModel bpmnModel = repositoryService.getBpmnModel(pi.getProcessDefinitionId());
        ProcessEngineConfiguration engconf = processEngine.getProcessEngineConfiguration();
        ProcessDiagramGenerator diagramGenerator = engconf.getProcessDiagramGenerator();
        InputStream in = diagramGenerator.generateDiagram(bpmnModel, "png", activityIds, flows, engconf.getActivityFontName(), engconf.getLabelFontName(), engconf.getAnnotationFontName(), engconf.getClassLoader(), 1.0, true);
        OutputStream out = null;
        byte[] buf = new byte[1024];
        int legth = 0;
        try {
            out = httpServletResponse.getOutputStream();
            while ((legth = in.read(buf)) != -1) {
                out.write(buf, 0, legth);
            }
        } finally {
            if (in != null) {
                in.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

    @GetMapping("/diagram/model-json/{processInstanceId}/{processDefinitionId}")
    public ResponseEntity genProcessDiagramModel( @PathVariable String processInstanceId, @PathVariable String processDefinitionId){
        ObjectNode historyProcessInstanceModelJSON = flowDiagramUtils.getHistoryProcessInstanceModelJSON(processInstanceId, processDefinitionId);

        JSONObject jsonObject = JSONUtil.parseObj(historyProcessInstanceModelJSON.toString());
        return ResponseEntity.ok(jsonObject);
    }

    @ApiOperation(value = "根据流程实例id查询其他参数")
    @GetMapping("/queryParamsByProcInsId")
    public ResponseEntity queryParamsByProcInsId(@RequestParam("procInsId") String procInsId) {
        Map result = flowInstanceService.queryParamsByProcInsId(procInsId);
        return ResponseEntity.ok(result);
    }

    @ApiOperation(value = "根据流程实例id查询流程信息")
    @GetMapping("/getProcessInfoByProcInsId")
    public ResponseEntity getProcessInfoByProcInsId(@RequestParam("procInsId") String procInsId) {
        FlowProcessInfoDto flowProcessInfoDto = flowInstanceService.getProcessInfoByProcInsId(procInsId);
        return ResponseEntity.ok(flowProcessInfoDto);
    }

    @ApiOperation(value = "根据流程实例id查询任务信息列表")
    @GetMapping("/getTaskInfoByProcInsId")
    public ResponseEntity getTaskInfoByProcInsId(@RequestParam("procInsId") String procInsId) {
        List<FlowTaskAssigneeDto> flowTaskAssigneeDtos = flowInstanceService.getTaskInfoByProcInsId(procInsId);
        return ResponseEntity.ok(flowTaskAssigneeDtos);
    }

    @ApiOperation(value = "终止流程")
    @PostMapping("/cancelProcess")
    public ResultJson cancelProcess(@RequestBody FlowTaskVo flowTaskVo) {
        return flowInstanceService.cancelProcess(flowTaskVo);
    }

    @ApiOperation(value = "意见排序")
    @PostMapping("/sortFormRemark")
    public ResultJson sortFormRemark(@RequestBody FlowRemarkVo flowRemarkVo) {
        return ResultJson.generateResult(ResultCodeEnum.SUCCESS.getMessageCN(), flowInstanceService.sortFormRemark(flowRemarkVo));
    }

    @ApiOperation(value = "查看事务后线程")
    @GetMapping("/showAfterTransOpt")
    @PreAuthorize("@el.check('admin')")
    public ResultJson showAfterTransOpt() {
        return ResultJson.generateResult(ResultCodeEnum.SUCCESS.getMessageCN(), AfterTransactionOpt.getAfterTransactionOpts());
    }
}
