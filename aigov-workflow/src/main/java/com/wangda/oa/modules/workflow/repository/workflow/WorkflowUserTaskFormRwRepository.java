package com.wangda.oa.modules.workflow.repository.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskFormRw;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午8:30
 */
public interface WorkflowUserTaskFormRwRepository extends JpaRepository<WorkflowUserTaskFormRw,Long>, JpaSpecificationExecutor<WorkflowUserTaskFormRw> {

    /**
     * 根据用户任务定义ID删除数据
     * @param taskDefinitionId
     */
    @Transactional
    void deleteByTaskDefinitionId(Long taskDefinitionId);
}
