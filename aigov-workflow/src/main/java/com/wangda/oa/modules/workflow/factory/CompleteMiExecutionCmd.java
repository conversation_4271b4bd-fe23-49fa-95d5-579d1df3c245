package com.wangda.oa.modules.workflow.factory;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

/**
 * flowable 完成多实例
 * <AUTHOR>
 * @date 2021-10-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class CompleteMiExecutionCmd implements Command<Void> {

    private String executionId;
    private MultiInstanceActivityBehavior multiInstanceActivityBehavior;

    public CompleteMiExecutionCmd(String executionId, MultiInstanceActivityBehavior multiInstanceActivityBehavior) {
        this.executionId = executionId;
        this.multiInstanceActivityBehavior = multiInstanceActivityBehavior;
    }

    @Override
    public Void execute(CommandContext commandContext) {
        ExecutionEntity executionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).findById(executionId);
        executionEntity.inactivate();

        // 结束活动实例
        CommandContextUtil.getActivityInstanceEntityManager(commandContext).recordActivityEnd(executionEntity, null);
        return null;
    }
}
