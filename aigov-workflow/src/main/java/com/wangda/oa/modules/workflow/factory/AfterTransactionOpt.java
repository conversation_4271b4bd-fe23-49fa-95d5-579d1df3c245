package com.wangda.oa.modules.workflow.factory;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25
 * @description 事务提交后操作
 */
@Slf4j
@Component
public class AfterTransactionOpt implements Executor {

    @Resource
    private AfterTransactionService afterTransactionService;

    @Getter
    private static final ThreadLocal<Map<String, LinkedBlockingQueue<Runnable>>> afterTransactionOpts = new NamedThreadLocal<>("发消息后置操作thread-local");

    @Override
    public void execute(Runnable runnable) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            //manager的synchronizations容器是set,可以随便注册,不会重复
            TransactionSynchronizationManager.registerSynchronization(afterTransactionService);
            Map<String,  LinkedBlockingQueue<Runnable>> stack = this.getIfNesses();
            try {
                stack.get(this.getTransactionName()).put(runnable);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            afterTransactionOpts.set(stack);
        }else{
            log.error("事务后置操作*必须*在一个活跃的事务中");
        }
    }

    /**
     * 获取当前线程执行数据栈
     * @return
     */
    public Map<String, LinkedBlockingQueue<Runnable>> getIfNesses() {
        Map<String, LinkedBlockingQueue<Runnable>> stack = afterTransactionOpts.get();
        //如果没有
        if(stack==null){
            stack = new HashMap<>();
        }
        if(stack.get(this.getTransactionName())==null){
            LinkedBlockingQueue<Runnable> runnables = new LinkedBlockingQueue<>(1000);
            stack.put(this.getTransactionName(),runnables);
        }
        return stack;
    }

    /**
     * 获取当前事务名称
     * @return
     */
    public static String getTransactionName() {
        String transName = TransactionSynchronizationManager.getCurrentTransactionName();
        if(StringUtils.isEmpty(transName)) {
            transName =  "defaultTrans";
        }
        return transName;
    }
}
