package com.wangda.oa.modules.workflow.factory;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * flowable 条件表达式执行结果
 * <AUTHOR>
 * @date 2021-08-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class ExpressionExecuteCmd implements Command<Boolean> {

    /**
     * 执行
     */
    private String executionId;
    private String conditionExpression;


    public ExpressionExecuteCmd(String executionId, String conditionExpression) {
        this.executionId = executionId;
        this.conditionExpression = conditionExpression;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {

        Expression expression = CommandContextUtil.getProcessEngineConfiguration(commandContext).getExpressionManager().createExpression(conditionExpression);

        ExecutionEntity executionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).findById(executionId);
        if(Objects.isNull(executionEntity)) {
            executionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).create();
        }

        Object booleanObject = expression.getValue(executionEntity);
        return this.getBoolean(booleanObject);
    }

    protected Boolean getBoolean(Object booleanObject) {
        if (booleanObject instanceof Boolean) {
            return (Boolean) booleanObject;
        }
        if (booleanObject instanceof String) {
            if ("true".equalsIgnoreCase((String) booleanObject)) {
                return Boolean.TRUE;
            }
            if ("false".equalsIgnoreCase((String) booleanObject)) {
                return Boolean.FALSE;
            }
        }
        return null;
    }
}
