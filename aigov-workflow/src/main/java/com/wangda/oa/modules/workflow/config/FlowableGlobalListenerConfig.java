package com.wangda.oa.modules.workflow.config;

import com.wangda.oa.modules.workflow.listener.FlowableGlobalProcessCompleteListener;
import com.wangda.oa.modules.workflow.listener.FlowableGlobalTaskCancelListener;
import com.wangda.oa.modules.workflow.listener.FlowableGlobalTaskCompleteListener;
import com.wangda.oa.modules.workflow.listener.FlowableGlobalTaskCreateListener;
import lombok.RequiredArgsConstructor;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.common.engine.api.delegate.event.FlowableEventDispatcher;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/16
 * @description 流程全局监听配置
 */
@Configuration
@RequiredArgsConstructor
public class FlowableGlobalListenerConfig implements ApplicationListener<ContextRefreshedEvent> {

    private final SpringProcessEngineConfiguration configuration;

    private final FlowableGlobalTaskCompleteListener flowableGlobalTaskCompleteListener;
    private final FlowableGlobalTaskCreateListener flowableGlobalTaskCreateListener;
    private final FlowableGlobalTaskCancelListener flowableGlobalTaskCancelListener;
    private final FlowableGlobalProcessCompleteListener flowableGlobalProcessCompleteListener;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        FlowableEventDispatcher dispatcher = configuration.getEventDispatcher();

        // 任务创建全局监听-待办消息发送
        dispatcher.addEventListener(flowableGlobalTaskCreateListener, FlowableEngineEventType.TASK_CREATED);
        dispatcher.addEventListener(flowableGlobalTaskCompleteListener, FlowableEngineEventType.TASK_COMPLETED);
        dispatcher.addEventListener(flowableGlobalTaskCancelListener, FlowableEngineEventType.ACTIVITY_CANCELLED);

        // 流程完成
        dispatcher.addEventListener(flowableGlobalProcessCompleteListener, FlowableEngineEventType.PROCESS_COMPLETED);
    }
}
