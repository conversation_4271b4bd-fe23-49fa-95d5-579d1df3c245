package com.wangda.oa.modules.workflow.factory;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.persistence.entity.HistoricActivityInstanceEntity;
import org.flowable.engine.impl.persistence.entity.HistoricActivityInstanceEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

/**
 * 创建历史活动
 * <AUTHOR>
 * @date 2021-04-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class CreateHistoricActivityInstanceCmd implements Command<Void> {

    /**
     * 用于创建活动
     */
    private HistoricActivityInstanceEntity historicActivityInstanceEntity;

    public CreateHistoricActivityInstanceCmd(HistoricActivityInstanceEntity historicActivityInstanceEntity) {
        this.historicActivityInstanceEntity = historicActivityInstanceEntity;
    }

    @Override
    public Void execute(CommandContext commandContext) {

        HistoricActivityInstanceEntityManager historicActivityInstanceEntityManager = CommandContextUtil.getHistoricActivityInstanceEntityManager(commandContext);
        historicActivityInstanceEntityManager.insert(historicActivityInstanceEntity);
        return null;
    }
}
