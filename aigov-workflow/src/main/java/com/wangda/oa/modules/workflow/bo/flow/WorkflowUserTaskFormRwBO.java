package com.wangda.oa.modules.workflow.bo.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/4/16 上午9:25
 */
@Data
public class WorkflowUserTaskFormRwBO {
    @ApiModelProperty(value = "唯一主键")
    private Long id;

    @ApiModelProperty(value = "用户任务定义ID")
    private Long taskDefinitionId;

    @ApiModelProperty(value = "流程定义id")
    private String flowDefineId;

    @ApiModelProperty(value = "环节Key")
    private String linkKey;

    @ApiModelProperty(value = "表单模板id")
    private Long templateId;

    @ApiModelProperty(value = "子表单标识")
    private String subFormId;

    @ApiModelProperty(value = "表单字段名")
    private String formFieldIdentifier;

    @ApiModelProperty(value = "读写隐状态(R:读,W:写,H:隐藏)")
    private String readState;
}
