package com.wangda.oa.modules.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/15 上午9:26
 */
@Data
@ApiModel(description="待办列表实体")
public class BacklogListDto {
    @ApiModelProperty(value = "待办id")
    private Long id;
    @ApiModelProperty(value = "外部业务id(同个任务相同)")
    private String bizId;
    @ApiModelProperty(value = "待办标题")
    private String title;
    @ApiModelProperty(value = "来源系统appId")
    private String appId;
    @ApiModelProperty(value = "模块名称")
    private String moduleName;
    @ApiModelProperty(value = "业务扩展属性")
    private String extensionJson;
    @ApiModelProperty(value = "跳转详情url")
    private String url;
    @ApiModelProperty(value = "pc跳转url")
    private String pcUrl;
    @ApiModelProperty(value = "徽标")
    private String logo;
    @ApiModelProperty(value = "搁置-非已收藏且状态为待办有效(0:未搁置,1:已搁置)")
    private Integer shelve;
    @ApiModelProperty(value = "搁置截止时间-非已收藏且状态为待办有效")
    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
    private Date shelveAbortTime;
    @ApiModelProperty(value = "办理时限-非已收藏且状态为待办有效")
    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
    private Date abortTime;
    @ApiModelProperty(value = "急件-非已收藏且状态为待办有效(0:非急件,1:急件)")
    private Integer urgent;
    @ApiModelProperty(value = "最后阅读时间")
    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
    private Date endReadTime;
    @ApiModelProperty(value = "待办创建时间")
    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
    private Date createDate;
    @ApiModelProperty(value = "当前办理人")
    private String currentHandler;
    @ApiModelProperty(value = "办理状态(待办、已办)")
    private String handleStatus;
    @ApiModelProperty(value = "临期逾期状态(1、2)")
    private Integer expiredStatus;

}
