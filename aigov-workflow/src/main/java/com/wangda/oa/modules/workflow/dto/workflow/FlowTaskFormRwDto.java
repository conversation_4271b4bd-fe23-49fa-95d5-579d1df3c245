package com.wangda.oa.modules.workflow.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-05-03
 */
@Data
@Builder
@ApiModel("工作流任务相关-表单读写状态")
public class FlowTaskFormRwDto implements Serializable {

    @ApiModelProperty("表单默认状态")
    private String defaultFormStatus;

    @ApiModelProperty("表单内容读写隐状态")
    private String formWrite;

}
