package com.wangda.oa.modules.workflow.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.zwdd.task.TaskCreateBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationLinkBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.config.SendTaskMessageConfig;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.modules.workflow.enums.workflow.TaskRemindWayEnum;
import com.wangda.oa.modules.workflow.factory.AfterTransactionOpt;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.service.IGbzjTaskService;
import com.wangda.oa.modules.workflow.service.ILabTaskService;
import com.wangda.oa.modules.workflow.service.ISysAppService;
import com.wangda.oa.modules.workflow.service.IZwddService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEntityEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.event.impl.FlowableEntityEventImpl;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description 任务创建监听
 */
@Slf4j
@Component
public class FlowableGlobalTaskCreateListener implements FlowableEventListener {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RestUrlComponent restUrlComponent;

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private UserService userService;

    @Autowired
    private SendTaskMessageConfig sendTaskMessageConfig;

    @Autowired
    private AfterTransactionOpt afterTransactionOpt;

    // zwdd
    @Resource
    private ZwddProperties zwddProperties;
    @Autowired(required = false)
    private IZwddService zwddService;
    @Autowired(required = false)
    private ISysAppService sysAppService;

    @Autowired(required = false)
    private ILabTaskService labTaskService;
    @Autowired(required = false)
    private IGbzjTaskService gbzjTaskService;

    @Override
    public void onEvent(FlowableEvent flowableEvent) {
        FlowableEntityEvent event = null;
        if(flowableEvent instanceof FlowableEntityEventImpl) {
            event = (FlowableEntityEventImpl) flowableEvent;
        }else if(flowableEvent instanceof FlowableEngineEntityEvent) {
            event = (org.flowable.common.engine.impl.event.FlowableEntityEventImpl) flowableEvent;
        }
        TaskEntity taskEntity = (TaskEntity) event.getEntity();

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(taskEntity.getProcessInstanceId())
                .includeProcessVariables()
                .singleResult();
        Map<String, Object> variables = processInstance.getProcessVariables();

        if(StringUtils.isEmpty(taskEntity.getAssignee()) || Objects.isNull(variables.get(ProcessConstants.BPM_FORM_DATA))) {
            log.info("任务创建监听，处理人没有：" + taskEntity.getName());
            return;
        }

        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(taskEntity.getProcessDefinitionId());
        String subject = "有一条" + processDefinition.getName() + "待您处理";

        String bpmUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/page?taskId=" + taskEntity.getId() + "&processInstanceId=" + taskEntity.getProcessInstanceId();
        String pcUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/webPage?processInstanceId=" + taskEntity.getProcessInstanceId() + "&taskId=" + taskEntity.getId();

        //新增oa待办
        try {
            Object formTitle = variables.get(ProcessConstants.BPM_FORM_TITLE);
            if(Objects.nonNull(formTitle)) {
                subject = formTitle.toString();
            }

            // 发送统一待办
            if(sendTaskMessageConfig.getSendBacklog()) {
                List backlogUserList = new ArrayList();
                UserDto userDto = userService.findById(Long.parseLong(processInstance.getStartUserId()));
                BacklogUserDto backlogUserDto = BacklogUserDto.builder().createDate(Instant.now().getEpochSecond()).userId(taskEntity.getAssignee()).build();
                if(Objects.nonNull(taskEntity.getDueDate())) {
                    long abortTime = taskEntity.getDueDate().getTime() / 1000;
                    backlogUserDto.setAbortTime(abortTime);
                }
                backlogUserList.add(backlogUserDto);

                // 表单数据
                String formData = JSON.toJSONString(variables.get(ProcessConstants.BPM_FORM_DATA));
                flowTaskService.sendBacklogList(subject, formData, backlogUserList, processInstance, userDto.getUsername(), bpmUrl, pcUrl, null);
            }

            // 发送之江实验室
            if(sendTaskMessageConfig.getSendZhejianglab()) {
                //实验室创建流程对应的待办任务
                String taskResult = labTaskService.createBpmTodoTask(Long.parseLong(taskEntity.getProcessInstanceId()), Long.parseLong(taskEntity.getId()), taskEntity.getAssignee());
                JSONObject jsonObject = new JSONObject(taskResult);
                if(jsonObject.getInt("code") == 400) {
                    //创建流程实例，如果已经当前流程实例id已经被创建，则返回创建失败
                    labTaskService.createBpmInstance(Long.parseLong(taskEntity.getProcessInstanceId()), subject);
                    taskResult = labTaskService.createBpmTodoTask(Long.parseLong(taskEntity.getProcessInstanceId()), Long.parseLong(taskEntity.getId()), taskEntity.getAssignee());
                }
                log.info("实验室创建任务结果：" + taskResult);
            }
        }catch(Exception e) {
            log.error("FlowableGlobalTaskCreateListener oa待办发送失败", e.getMessage());
            e.printStackTrace();
        }

        // 设置任务类别
        if(StringUtils.isBlank(taskEntity.getCategory())) {
            Deployment deploy = repositoryService.createDeploymentQuery().deploymentId(processDefinition.getDeploymentId()).singleResult();
            if(Objects.nonNull(deploy) && StringUtils.isNotBlank(deploy.getCategory())) {
                taskEntity.setCategory(deploy.getCategory());
            }
        }

        SysUserPlatform sysUserPlatform = null;
        if(sendTaskMessageConfig.getSendZwdd() || sendTaskMessageConfig.getSendGbzjoa()) {
            sysUserPlatform = sysAppService.getByUserNameAndType(taskEntity.getAssignee(), zwddProperties.getType());
            if(Objects.isNull(sysUserPlatform)) {
                ResultJson resultJson = sysAppService.getUid(taskEntity.getAssignee());
                if(Objects.nonNull(resultJson) && resultJson.isSuccess()) {
                    sysUserPlatform = new SysUserPlatform();
                    sysUserPlatform.setPlatformUserId(resultJson.getData().toString());
                }else {
                    sysUserPlatform = sysAppService.getByUserNameAndType("admin", zwddProperties.getType());
                }
            }
            if(Objects.isNull(sysUserPlatform) || StringUtils.isEmpty(sysUserPlatform.getPlatformUserId())) {
                log.error("FlowableGlobalTaskCreateListener:" + taskEntity.getAssignee() + ",该用户没有绑定浙政钉编号");
                return;
            }
        }

        // 发送干部之家
        if(sendTaskMessageConfig.getSendGbzjoa()) {
            try {
                gbzjTaskService.pushOaTask(taskEntity.getProcessInstanceId(), subject, bpmUrl, sysUserPlatform.getPlatformUserId(), "一般", DateUtil.format(new Date(), "yyyyMMddHHmmss"), taskEntity.getId());
            }catch(Exception e) {
                log.error("发送干部之家任务创建错误：procInstId:" + taskEntity.getProcessInstanceId() + " platformUserId:" + sysUserPlatform.getPlatformUserId());
                e.printStackTrace();
            }
        }

        // 发送政务钉钉
        if(sendTaskMessageConfig.getSendZwdd()) {
            // 流程发起人
            SysUserPlatform creatorUserPlatform = sysAppService.getByUserId(processInstance.getStartUserId(), zwddProperties.getType());
            if(Objects.isNull(creatorUserPlatform)) {
                UserDto startUserDto = userService.findById(Long.parseLong(processInstance.getStartUserId()));
                ResultJson resultJson = sysAppService.getUid(startUserDto.getUsername());
                if(Objects.nonNull(resultJson) && resultJson.isSuccess()) {
                    creatorUserPlatform = new SysUserPlatform();
                    creatorUserPlatform.setPlatformUserName(startUserDto.getNickName());
                    creatorUserPlatform.setPlatformUserId(resultJson.getData().toString());
                }
            }
            if(Objects.isNull(creatorUserPlatform) || StringUtils.isEmpty(creatorUserPlatform.getPlatformUserId())) {
                log.error("FlowableGlobalTaskCreateListener:浙政钉待办通知创建错误，流程发起人还未绑定浙政钉id，procInstId:" + taskEntity.getProcessInstanceId() + " startUserId:" + processInstance.getStartUserId());
                creatorUserPlatform = sysUserPlatform;
            }

            TaskCreateBO taskCreateBO = TaskCreateBO.builder()
//                .assigneeInfo("{\"imgId\":\"\",\"name\":\""+taskEntity.getAssignee()+"\"}")
                    .subject(subject)
                    .creatorId(creatorUserPlatform.getPlatformUserId())
                    .createDate(processInstance.getStartTime())
                    .bizTaskId(taskEntity.getId())
                    .url(bpmUrl)
                    .mobileUrl(bpmUrl)
                    .assigneeId(sysUserPlatform.getPlatformUserId())
                    .packageUuid(taskEntity.getProcessInstanceId())
                    .build();

            // 发送待办
            afterTransactionOpt.execute(new Runnable() {
                @Override
                public void run() {
                    zwddService.createTask(taskCreateBO);
                }
            });

            // 获取节点消息提醒方式
            BpmnModel bpmnModel = repositoryService.getBpmnModel(taskEntity.getProcessDefinitionId());
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(taskEntity.getTaskDefinitionKey());
            String taskRemindWays = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMINDWAY);

            // 发送浙政钉通知
            if(StringUtils.isNotBlank(taskRemindWays) && taskRemindWays.contains(TaskRemindWayEnum.ZWDD.getValue())) {

                String notificationSubject = new StringBuilder(creatorUserPlatform.getPlatformUserName())
                        .append(DateUtil.format(processInstance.getStartTime(), "yyyy.MM.dd"))
                        .append("提交的")
                        .append(processDefinition.getName())
                        .append("待您处理")
                        .toString();

                WorkNotificationLinkBO workNotificationLinkBO = WorkNotificationLinkBO.builder()
                        .title(processDefinition.getName())
                        .messageUrl(bpmUrl)
                        .picUrl("@lALOACZwe2Rk")
                        .text(notificationSubject)
                        .build();
                WorkNotificationBO workNotificationBO = WorkNotificationBO.builder()
                        .receiverIds(sysUserPlatform.getPlatformUserId())
                        .bizMsgId(taskEntity.getId())
                        .type(1)
                        .linkBO(workNotificationLinkBO)
                        .build();

                // 发送通知
                afterTransactionOpt.execute(new Runnable() {
                    @Override
                    public void run() {
                        zwddService.workNotification(workNotificationBO);
                    }
                });
            }
        }

        // 发送短信通知
//        if(StringUtils.isNotBlank(taskRemindWays) && taskRemindWays.contains(TaskRemindWayEnum.SMS.getValue())) {
//            UserDto userDto = userService.findByName(taskEntity.getAssignee());
//            if(Objects.isNull(userDto) || StringUtils.isBlank(userDto.getPhone())) {
//                return;
//            }
//
//            MasUserBO masUserBO = new MasUserBO();
//            masUserBO.setPhone(userDto.getPhone());
//            masUserBO.setNickName(userDto.getNickName());
//            masUserBO.setId(userDto.getId().toString());
//            List<MasUserBO> userBOList = new ArrayList<>();
//            userBOList.add(masUserBO);
//
//            MasSendContentBO contentBO = new MasSendContentBO();
//            contentBO.setUserBOList(userBOList);
//            contentBO.setContent(notificationSubject);
//            contentBO.setSendDate(new Date());
//            contentBO.setTiming(0);
//            masBusinessService.sendSMContent(contentBO, 0, 0);
//        }
    }

    @Override
    public boolean isFailOnException() {
        return true;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }

}
