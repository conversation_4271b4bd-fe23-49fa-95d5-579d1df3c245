package com.wangda.oa.modules.workflow.controller.form;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wangda.boot.annotation.ResultBody;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.modules.workflow.bo.FormFieldBO;
import com.wangda.oa.modules.workflow.bo.QueryFormBO;
import com.wangda.oa.modules.workflow.bo.SubmitFormBO;
import com.wangda.oa.modules.workflow.bo.TemplateListBO;
import com.wangda.oa.modules.workflow.config.SendTaskMessageConfig;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowDeployForm;
import com.wangda.oa.modules.workflow.domain.form.HistoricalFlowFormData;
import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.modules.workflow.dto.TemplateListDto;
import com.wangda.oa.modules.workflow.factory.IFlowFormHandleFactory;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.repository.workflow.ProcessArchiveRepository;
import com.wangda.oa.modules.workflow.service.definition.WorkflowDeployFormService;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.form.HistoricalFlowFormDataService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.utils.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.Process;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.Instant;
import java.util.*;

/**
 * 表单模板
 *
 * <AUTHOR>
 * @date 2020/12/22 14:52
 */
@Slf4j
@RequestMapping("/api/form")
@RestController
@CrossOrigin
@Validated
public class FormTemplateController {

    @Resource
    private FormTemplateService formTemplateService;

    @Resource
    private WorkflowDeployFormService workflowDeployFormService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private HistoryService historyService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private IFlowFormHandleFactory flowFormHandleFactory;

    @Resource
    private RestUrlComponent restUrlComponent;

    @Resource
    private SendTaskMessageConfig sendTaskMessageConfig;

    @Resource
    private FlowTaskService flowTaskService;

    @Resource
    private ProcessArchiveRepository processArchiveRepository;

    @Resource
    private HistoricalFlowFormDataService historicalFlowFormDataService;

    @Resource
    private WdFormTemplateRepository wdFormTemplateRepository;

    @RequestMapping(value = "/getFormData", method = RequestMethod.GET)
    public ResponseEntity getFormData(QueryFormBO queryFormBO) throws Exception {
        ResponseEntity responseEntity = formTemplateService.getFormData(queryFormBO);
        return responseEntity;
    }

    /**
     * 普通表单数据提交
     * @param submitFormBO
     * @return ResponseEntity
     */
    @PostMapping(value = "/submitForm")
    public ResponseEntity submitForm(@Validated @RequestBody SubmitFormBO submitFormBO) throws Exception {

        ResponseInfo responseInfo = formTemplateService.submitForm(submitFormBO);
        if(responseInfo.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
            return ResponseEntity.ok("保存成功");
        }
        return ResponseEntity.status(responseInfo.getCode()).body(responseInfo.getMessage());
    }

    /**
     * 流程数据提交 fullClazz 全路径 com.wangda.oa.modules.workflow.domain.form.Form01
     * @param submitFormBO
     * @return
     */
    @RequestMapping(value = "/formSubmit", method = RequestMethod.POST)
    @Transactional(rollbackOn = Exception.class)
    public ResponseInfo formSubmit(@Validated @RequestBody SubmitFormBO submitFormBO) throws Exception {

        // 预处理formData
        JSONObject formDataObj = JSONObject.parseObject(submitFormBO.getFormDataJson());
        if(!StringUtils.isEmpty(submitFormBO.getProcessInstanceId())) {
            // 处理1.7号晚上发版后，解决暂存意见为保存引起的这两天办件问题，后续删除代码
            String[] ignoreProperties = {"zsdw", "csdw"};
            for (String key: ignoreProperties) {
                Object obj = formDataObj.get(key);
                if(Objects.nonNull(obj) && obj instanceof JSONObject) {
                    JSONObject jsonObject = (JSONObject)obj;
                    jsonObject.remove("id");
                }
            }

            formDataObj.put("bpmInstanceId", submitFormBO.getProcessInstanceId());
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(submitFormBO.getProcessInstanceId())
                .singleResult();
        if(StringUtils.isEmpty(formDataObj.getString("bpmProcessKey"))) {
            formDataObj.put("bpmProcessKey", processInstance.getProcessDefinitionKey());
        }

        ResponseInfo result = formTemplateService.formSubmit(submitFormBO.getFormTemplateId(), formDataObj.toJSONString(), submitFormBO.getTaskId());
        if(result.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
            // 更新流程变量
            JSONObject jsonObject = (JSONObject)result.getData();
            BeanUtil.copyProperties(jsonObject, formDataObj, CopyOptions.create().ignoreNullValue().ignoreError());
            result = new ResponseInfo("操作成功");
        }
        runtimeService.setVariable(submitFormBO.getProcessInstanceId(), ProcessConstants.BPM_FORM_DATA, formDataObj);

        // 如果是保存草稿，则发送统一待办
        if (sendTaskMessageConfig.getSendBacklog() && StringUtils.isEmpty(processInstance.getBusinessStatus())) {
            // bpm定义模型
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
            Process process = bpmnModel.getProcess(null);

            String titleRule = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_BACKLOG_SUBJECT_RULE);
            String subject = flowFormHandleFactory.handleSubjectRule(formDataObj, titleRule);

            String bpmUrl = restUrlComponent.getServerUrl()+"/api/bpm/handle/page?processInstanceId="+processInstance.getProcessInstanceId();
            String pcUrl = restUrlComponent.getServerUrl()+"/api/bpm/handle/webPage?processInstanceId="+processInstance.getProcessInstanceId();

            List backlogUserList = new ArrayList();
            String currentUsername = SecurityUtils.getCurrentUsername();
            BacklogUserDto backlogUserDto = BacklogUserDto.builder().createDate(Instant.now().getEpochSecond()).userId(currentUsername).build();
            backlogUserList.add(backlogUserDto);

            flowTaskService.sendBacklogList(subject, JSON.toJSONString(formDataObj), backlogUserList, processInstance, currentUsername, bpmUrl, pcUrl, null);
        }

        return result;
    }

    /**
     * 表单数据修改
     * @param submitFormBO
     * @return
     */
    @RequestMapping(value = "/updateFormData", method = RequestMethod.POST)
    @Transactional(rollbackOn = Exception.class)
    public ResponseInfo updateFormData(@Validated @RequestBody SubmitFormBO submitFormBO) throws Exception {
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(submitFormBO.getProcessInstanceId())
                .includeProcessVariables()
                .singleResult();
        Map variables = processInstance.getProcessVariables();
        Object formData = variables.get(ProcessConstants.BPM_FORM_DATA);

        // 保存历史版本
        HistoricalFlowFormData historicalFlowFormData = new HistoricalFlowFormData();
        historicalFlowFormData.setProcInstId(submitFormBO.getProcessInstanceId());
        historicalFlowFormData.setFormData(JSON.toJSONString(formData));
        historicalFlowFormData.setOperator(SecurityUtils.getCurrentUsername());
        historicalFlowFormDataService.save(historicalFlowFormData);

        // 预处理formData
        JSONObject formDataObj = JSONObject.parseObject(submitFormBO.getFormDataJson());
        if(!StringUtils.isEmpty(submitFormBO.getProcessInstanceId())) {
            formDataObj.put("bpmInstanceId", submitFormBO.getProcessInstanceId());
        }
        if(StringUtils.isEmpty(formDataObj.getString("bpmProcessKey"))) {
            formDataObj.put("bpmProcessKey", processInstance.getProcessDefinitionKey());
        }
        ResponseInfo result = formTemplateService.formSubmit(submitFormBO.getFormTemplateId(), formDataObj.toJSONString(), submitFormBO.getTaskId());
        if(result.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
            // 更新流程变量
            JSONObject jsonObject = (JSONObject)result.getData();
            BeanUtil.copyProperties(jsonObject, formDataObj, CopyOptions.create().ignoreNullValue().ignoreError());
            result = new ResponseInfo("操作成功");
        }

        // 业务流程缓存处理
        flowFormHandleFactory.handleAddVariables(submitFormBO.getTaskId(), processInstance.getProcessDefinitionKey(), variables, formDataObj);

        if(Objects.isNull(processInstance.getEndTime())) {
            runtimeService.setVariable(submitFormBO.getProcessInstanceId(), ProcessConstants.BPM_FORM_DATA, formDataObj);
        }else {
            processArchiveRepository.updateHiActGeBytearrayByProcInstId(submitFormBO.getProcessInstanceId(), formDataObj);
        }
        return result;
    }

    @RequestMapping(value = "/getWritableFormData", method = RequestMethod.GET)
    public ResponseEntity getWritableFormData(@RequestParam("procInstId") String procInstId) {
        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInstId)
                .includeProcessVariables()
                .singleResult();
        Map variables = processInstance.getProcessVariables();

        // 获取流程扩展
        Map<String, String> formRwhMap = new HashMap<>();
        formRwhMap.put(ProcessConstants.BPM_RWH_DEFAULT, "W");

        // PC端服务端处理读写状态
        WdFormTemplate template = wdFormTemplateRepository.getOne(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()));
        JSONObject formObject = JSONObject.parseObject(template.getFormJson());
        // 处理流转表单读写属性
        formTemplateService.formRwHandle(formObject.getJSONArray("list"), formRwhMap);
        return ResponseEntity.ok(formObject);
    }

    @RequestMapping(value = "/updateFormField", method = RequestMethod.POST)
    @Transactional(rollbackOn = Exception.class)
    public ResponseInfo updateFormField(@Validated @RequestBody FormFieldBO formFieldBO) throws Exception {

        ResponseInfo result = formTemplateService.updateFormFieldData(formFieldBO.getFormTemplateId(), formFieldBO.getProcessInstanceId(), formFieldBO.getFieldName(), formFieldBO.getFormFieldData(), false);

        // 判断流程是否已结束
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(formFieldBO.getProcessInstanceId()).includeProcessVariables().singleResult();
        if(Objects.isNull(historicProcessInstance.getEndTime())) {
            // 更新流程变量
            JSONObject formDataObj = (JSONObject) runtimeService.getVariable(formFieldBO.getProcessInstanceId(), ProcessConstants.BPM_FORM_DATA);
            formDataObj.put(formFieldBO.getFieldName(), formFieldBO.getFormFieldData());
            runtimeService.setVariable(formFieldBO.getProcessInstanceId(), ProcessConstants.BPM_FORM_DATA, formDataObj);
        }else {
            Map variables = historicProcessInstance.getProcessVariables();
            JSONObject processFormData = (JSONObject)variables.get(ProcessConstants.BPM_FORM_DATA);

            //处理业务表意见
            processFormData.put(formFieldBO.getFieldName(), formFieldBO.getFormFieldData());

            //更新历史流程缓存
            processArchiveRepository.updateHiActGeBytearrayByProcInstId(historicProcessInstance.getId(), processFormData);
        }

        if(result.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            result = new ResponseInfo(ResultCodeEnum.SUCCESS.getCode(),"修改成功");
        }
        return result;
    }

    /**
     * 保存模板与修改表单模板
     *
     * @return
     */
    @ApiOperation(value = "保存模板与修改模板", notes = "保存模板与修改模板")
    @PostMapping
    @ResultBody
    public ResponseInfo save(@RequestBody WdFormTemplate template) {
        return formTemplateService.save(template);
    }

    /**
     * 根据ID查询模板
     *
     * @return
     */
    @ApiOperation(value = "根据ID查询表单模板", notes = "根据ID查询表单模板")
    @GetMapping("/{id}")
    @ResultBody
    public WdFormTemplate queryById(@PathVariable("id") Long id) {
        log.info("根据ID查询表单模板入参:" + id);
        return formTemplateService.queryById(id);
    }

    /**
     * 查询所有表单
     *
     * @return
     */
    @ApiOperation(value = "查询所有表单", notes = "查询所有表单")
    @GetMapping(value = "/queryAll")
    public TemplateListDto queryAll(TemplateListBO bo) {
        return formTemplateService.queryAll(bo);
    }

    @ApiOperation(value = "挂载表单")
    @PostMapping("/addDeployForm")
    public ResultJson addDeployForm(@Validated @RequestBody WorkflowDeployForm workflowDeployForm) {
        workflowDeployFormService.saveDeployForm(workflowDeployForm);
        return ResultJson.generateResult("挂载成功");
    }
    @ApiOperation(value = "修改表单")
    @PostMapping("/updateDeployForm")
    public ResultJson updateDeployForm(@Validated @RequestBody WorkflowDeployForm workflowDeployForm){
        workflowDeployFormService.updateDeployForm(workflowDeployForm);
        return ResultJson.generateResult("修改成功");
    }

    @ApiOperation(value = "删除表单")
    @DeleteMapping("/deleteForm")
    public boolean deleteFrom(Long id){
        return  formTemplateService.deleteFrom(id);
    }
}
