package com.wangda.oa.modules.workflow.domain.workflow.vo;

import com.wangda.boot.platform.base.Pagination;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lee
 * @projectName : flowable
 * @description: 任务查询VO
 * @date : 2021/05/13
 */
@Data
@NoArgsConstructor
public class TaskQueryVo extends Pagination {

    /**
     * 用户名
     */
    private String username;

    /**
     * 时间范围
     */
    private List<String> timeRange;

    /**
     * 流程名
     */
    private String name;

    /**
     * 流程类别
     */
    private String category;

    /**
     * 状态（1:已办未完结,2:已办已完结）
     */
    private Integer status;

    /**
     * 流程定义key
     */
    private String procDefKey;
}
