package com.wangda.oa.modules.workflow.domain.workflow;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;

/**
 * 流程-用户任务定义扩展表
 *
 * <AUTHOR>
 * @date 2021/4/15下午7:47
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_workflow_user_task_extension",uniqueConstraints=@UniqueConstraint(columnNames={"flow_define_id", "link_Key"}))
public class WorkflowUserTaskExtension extends BaseDomain {

    @Column(name = "flow_define_id")
    @ApiModelProperty(value = "流程部署id")
    private String flowDefineId;

    @Column(name = "link_key")
    @ApiModelProperty(value = "环节Key")
    private String linkKey;

    @Column(name = "name")
    @ApiModelProperty(value = "名称")
    private String name;

    @Column(name = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Column(name = "form_read")
    @ApiModelProperty(value = "表单默认读写隐状态(R:读,W:写,H:隐藏)")
    private String formRead;

    @Column(name = "opinion_binding_rule")
    @ApiModelProperty(value = "意见绑定规则")
    private String opinionBindingRule;

    @Column(name = "prompt")
    @ApiModelProperty(value = "提示语")
    private String prompt;

    @ApiModelProperty(value = "PC表单样式")
    private String formSkin;

    @ApiModelProperty(value = "移动端表单样式")
    private String mobileFormSkin;

    @ApiModelProperty(value = "用户任务操作按钮定义集合")
    @OneToMany(targetEntity = WorkflowUserTaskButtons.class)
    @JoinColumn(name="task_definition_id")
    @org.hibernate.annotations.ForeignKey(name = "none")
    @OrderBy("sort ASC")
    private List<WorkflowUserTaskButtons> buttons;

    @ApiModelProperty(value = "用户任务表单读写定义集合")
    @OneToMany(targetEntity = WorkflowUserTaskFormRw.class)
    @JoinColumn(name="task_definition_id")
    @org.hibernate.annotations.ForeignKey(name = "none")
    private List<WorkflowUserTaskFormRw> readWrite;

    @ApiModelProperty(value = "流程名称")
    @Transient
    private String flowDefineName;
}
