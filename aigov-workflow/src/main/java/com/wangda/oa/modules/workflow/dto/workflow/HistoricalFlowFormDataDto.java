package com.wangda.oa.modules.workflow.dto.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Lob;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/12 22:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("表单历史版本数据")
public class HistoricalFlowFormDataDto implements Serializable {

    @NotBlank
    @ApiModelProperty(value = "流程实例id")
    private String procInstId;

    @NotNull
    @ApiModelProperty(value = "业务表单数据JSON")
    @Lob
    private String formData;

    @ApiModelProperty(value = "操作人")
    private SimpleUserDto userDto;

    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date createDate;
}
