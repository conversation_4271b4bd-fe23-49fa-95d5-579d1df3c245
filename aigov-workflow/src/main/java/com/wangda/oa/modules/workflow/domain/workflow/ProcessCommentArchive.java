package com.wangda.oa.modules.workflow.domain.workflow;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/8
 * @description 流程意见归档
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class ProcessCommentArchive extends BaseDomain {

    @ApiModelProperty(value = "类别", notes = "TaskCommentTypeEnum的属性名")
    private String type;

    @ApiModelProperty(value = "意见内容")
    @Column(length = 512)
    private String comment;

    @ApiModelProperty(value = "意见名称")
    private String name;

    @ApiModelProperty(value = "处理用户名")
    private String username;

    @ApiModelProperty(value = "处理人部门名称")
    private String deptName;

    @ApiModelProperty(value = "处理时间")
    private Date time;

    @ApiModelProperty(value = "处理人任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "流程实例名称")
    private String procInstanceName;

    @ApiModelProperty(value = "流程实例ID")
    private String procInstanceId;

}
