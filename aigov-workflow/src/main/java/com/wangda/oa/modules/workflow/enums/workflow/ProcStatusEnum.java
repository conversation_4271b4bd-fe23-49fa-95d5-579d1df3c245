package com.wangda.oa.modules.workflow.enums.workflow;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR> leec
 * @title: : ProcStatusEnum
 * @description: 流程状态
 * @date : 2021/05/11
 */
@Getter
@AllArgsConstructor
@ToString
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@JSONType(serializeEnumAsJavaBean = true)
public enum ProcStatusEnum {
    INPROGRESS("INPROGRESS", "进行中"),
    TG("TG", "已通过"),
    JJ("JJ", "已拒绝"),
    CX("CX", "已撤销"),
    //同步的数据状态
    END("END", "已通过");

    //名称
    private String value;
    private String name;

    public static ProcStatusEnum getProcStatusEnumByValue(String value) {
        for (ProcStatusEnum e : ProcStatusEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}
