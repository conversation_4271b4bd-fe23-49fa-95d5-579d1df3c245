package com.wangda.oa.modules.workflow.listener;

import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowCustomExtensionService;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.flowable.engine.impl.el.FixedValue;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/20
 * @description
 */
@Slf4j
@Component
@Setter
@Getter
public class SendMessageExecutionHandler implements JavaDelegate, ApplicationContextAware {

    private static final long serialVersionUID = -5140234938739863473L;

    private static ApplicationContext applicationContext;

    /**
     * 抄送的用户的表达式 如${userIds}
     */
    private Expression userList;

    /**
     * 是否发送到统一待办
     */
    private FixedValue sendToBacklog;

    @Override
    public void setApplicationContext(ApplicationContext arg0) throws BeansException {
        applicationContext = arg0;
    }

    /**
     * receiverIds @ApiModelProperty(value = "接收人用户ID(accountId)， 多个人时使用半角逗号分隔，与organizationCodes都为空时不发送，最大长度列表跟organizationCodes加起来不大于1000")
     * bizMsgId @ApiModelProperty(value = "业务消息id，自定义，有去重功能 调用者的业务数据ID，同样的ID调用多次会提示\"重复\"错误")
     * msg @ApiModelProperty(value = "必填,json对象 必须 {\"msgtype\":\"text\",\"text\":{\"content\":\"消息内容\"}} 消息内容，目前支持：文本消息：text, 链接消息：link, OA消息：oa, 卡片消息：action_card。最长不超过2048个字节\n",required = true)
     * @param execution
     */
    @Override
    public void execute(DelegateExecution execution) {
        String usernames = (String) this.userList.getValue(execution);
        if(StringUtils.isNotEmpty(usernames)) {
            WorkflowCustomExtensionService workflowCustomExtensionService = (WorkflowCustomExtensionService) applicationContext.getBean("workflowCustomExtensionServiceImpl");

            // 设置抄送人
            execution.setVariable(ProcessConstants.BPM_BPM_COPYTO_PREFIX + execution.getCurrentActivityId(), usernames);
            List<String> usernameList = Arrays.asList(usernames.split(ElAdminConstant.SEPARATOR_COMMA));

            String processInstanceId = execution.getProcessInstanceId();

            boolean toBacklog = false;
            if(Objects.nonNull(sendToBacklog)) {
                Object sendToBacklogValue = sendToBacklog.getValue(execution);
                toBacklog = Boolean.parseBoolean(sendToBacklogValue.toString());
            }
            workflowCustomExtensionService.sendCopyToMessage(processInstanceId, usernameList, toBacklog);
        }
    }
}
