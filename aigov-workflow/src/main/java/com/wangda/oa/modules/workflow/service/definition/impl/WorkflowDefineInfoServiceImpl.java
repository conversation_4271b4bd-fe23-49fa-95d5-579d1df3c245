package com.wangda.oa.modules.workflow.service.definition.impl;

import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.workflow.bo.flow.InfoBO;
import com.wangda.oa.modules.workflow.bo.flow.ListBO;
import com.wangda.oa.modules.workflow.bo.flow.WorkflowDefineInfoBO;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowClassify;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowDefineInfo;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowTerminal;
import com.wangda.oa.modules.workflow.dto.workflow.WorkflowDefineInfoListDto;
import com.wangda.oa.modules.workflow.repository.definition.WorkflowClassifyRepository;
import com.wangda.oa.modules.workflow.repository.definition.WorkflowDefineInfoRepository;
import com.wangda.oa.modules.workflow.repository.definition.WorkflowTerminalRepository;
import com.wangda.oa.modules.workflow.service.definition.WorkflowDefineInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/13 下午8:09
 */
@Service
public class WorkflowDefineInfoServiceImpl implements WorkflowDefineInfoService {

    @Resource
    private WorkflowDefineInfoRepository workflowDefineInfoRepository;

    @Resource
    private WorkflowClassifyRepository workflowClassifyRepository;

    @Resource
    private WorkflowTerminalRepository workflowTerminalRepository;

    @Override
    @Transactional(rollbackFor = Exception.class,readOnly = false)
    public Long addOrUpdate(WorkflowDefineInfoBO bo) {
        WorkflowDefineInfo defineInfo;
        if(bo.getId()!=null){
            workflowTerminalRepository.deleteByFlowDefineId(bo.getId());
            defineInfo=workflowDefineInfoRepository.findById(Long.valueOf(bo.getId())).orElse(null);
            if(defineInfo==null){
                throw new BadRequestException("数据不存在");
            }
        }else{
            defineInfo=new WorkflowDefineInfo();
            List<WorkflowDefineInfo> list=workflowDefineInfoRepository.findByProcessDefinitionKeyAndLatestVersion(bo.getProcessDefinitionKey(),1);
            for (WorkflowDefineInfo workflowDefineInfo:list) {
                //把原来流程标识为最新版本的修改为否
                workflowDefineInfo.setLatestVersion(0);
                workflowDefineInfoRepository.save(workflowDefineInfo);
            }
            //新增记录版本号累加
            int number=workflowDefineInfoRepository.countByProcessDefinitionKey(bo.getProcessDefinitionKey());
            defineInfo.setVersionNumber(number+1);
        }
        BeanUtils.copyProperties(bo,defineInfo);
        defineInfo.setLatestVersion(1);
        WorkflowClassify classify=workflowClassifyRepository.findById(bo.getClassifyId()).orElse(new WorkflowClassify());
        defineInfo.setClassify(classify);
        workflowDefineInfoRepository.save(defineInfo);
        //保存流程终端表
        for (String name:bo.getTerminals()) {
            WorkflowTerminal workflowTerminal=new WorkflowTerminal();
            workflowTerminal.setName(name);
            workflowTerminal.setFlowDefineId(String.valueOf(defineInfo.getId()));
            workflowTerminalRepository.save(workflowTerminal);
        }
        return defineInfo.getId();
    }

    @Override
    public WorkflowDefineInfoListDto getList(ListBO bo) {
        WorkflowDefineInfoListDto dto=new WorkflowDefineInfoListDto();
        Pageable pageable= PageRequest.of(bo.getPage(),bo.getSize(), Sort.by("createDate").descending());
        WorkflowDefineInfo workflowDefineInfo=new WorkflowDefineInfo();
        workflowDefineInfo.setLatestVersion(1);
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("latestVersion", ExampleMatcher.GenericPropertyMatchers.exact());
        Example<WorkflowDefineInfo> example = Example.of(workflowDefineInfo ,matcher);
        Page<WorkflowDefineInfo> page=workflowDefineInfoRepository.findAll(example,pageable);
        dto.setContent(page.getContent());
        dto.setTotalElements(page.getTotalElements());
        return dto;
    }

    @Override
    public WorkflowDefineInfo getById(InfoBO bo) {
        return workflowDefineInfoRepository.findById(bo.getId()).orElse(new WorkflowDefineInfo());
    }

    @Override
    public List<WorkflowClassify> getClassifyList() {
        return workflowClassifyRepository.findAll();
    }

    @Override
    public Boolean delete(WorkflowDefineInfoBO bo) {
        workflowTerminalRepository.deleteByFlowDefineId(bo.getId());
        workflowDefineInfoRepository.deleteById(Long.valueOf(bo.getId()));
        return true;
    }
}
