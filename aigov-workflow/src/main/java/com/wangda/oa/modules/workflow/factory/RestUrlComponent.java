package com.wangda.oa.modules.workflow.factory;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Data
 * @description 调用其他服务地址配置
 */

@Component
@ConfigurationProperties(prefix = "url")
@Getter
@Setter
public class RestUrlComponent{

    /**
     * 待办服务地址
     */
    private String backlogServiceUrl;

    /**
     * 统一待办应用ID
     */
    private String backlogAppId = "OA";

    /**
     * 服务地址
     */
    private String serverUrl;

    private Integer port;

    /**
     * 老oa服务地址
     */
    private String oldOaServerUrl;

    /**
     * 实验室双中台对接地址
     */
    private String zhejianglabUrl;

}
