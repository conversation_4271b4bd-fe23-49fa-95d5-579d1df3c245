package com.wangda.oa.modules.workflow.service.workflow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserRead;
import com.wangda.oa.modules.workflow.domain.workflow.vo.*;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.modules.workflow.dto.HistoricProcessBO;
import com.wangda.oa.modules.workflow.dto.workflow.*;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.data.domain.Pageable;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-04-03 14:42
 */
public interface FlowTaskService {

    /**
     * 审批任务
     * @param task 请求实体参数
     */
    void completeTask(FlowTaskVo task) throws Exception;

    /**
     * 驳回任务
     * @param flowTaskVo
     */
    void taskReject(FlowTaskVo flowTaskVo);

    /**
     * 退回任务
     * @param flowTaskVo 请求实体参数
     */
    void taskReturn(FlowTaskVo flowTaskVo);

    /**
     * 退回首环节
     * @param flowTaskVo 请求实体参数
     */
    void taskReturnFirst(FlowTaskVo flowTaskVo);

    /**
     * 退回上一环节
     * @param flowTaskVo 请求实体参数
     */
    void taskReturnLast(FlowTaskVo flowTaskVo);

    /**
     * 获取所有可回退的节点
     * @param flowTaskVo
     * @return
     */
    ResultJson findReturnTaskList(FlowTaskVo flowTaskVo);

    /**
     * 获取特送所有可选择的节点
     * @param taskId
     * @return
     */
    ResultJson findExpressTaskList(String taskId);

    /**
     * 删除任务
     * @param flowTaskVo 请求实体参数
     */
    void deleteTask(FlowTaskVo flowTaskVo);

    /**
     * 认领/签收任务
     * @param flowTaskVo 请求实体参数
     */
    void claim(FlowTaskVo flowTaskVo);

    /**
     * 取消认领/签收任务
     * @param flowTaskVo 请求实体参数
     */
    void unClaim(FlowTaskVo flowTaskVo);

    /**
     * 委派任务(特送转办任务)
     * @param flowTaskVo 请求实体参数
     */
    void delegateTask(FlowTaskVo flowTaskVo);

    /**
     * 我的流程(包括我发起和我处理的)
     * @param query
     * @return
     */
    Page myProcessList(ProcessQueryVo query);

    /**
     * 取消申请
     * @param flowTaskVo
     * @return
     */
    ResultJson stopProcess(FlowTaskVo flowTaskVo);

    /**
     * 收回流程
     * @param flowTaskVo
     * @return
     */
    ResultJson revokeProcess(FlowTaskVo flowTaskVo);


    /**
     * 我的代办任务列表
     * @param query 查询参数
     * @return
     */
    ResultJson todoList(TaskQueryVo query) throws Exception;

    /**
     * 查询待办任务列表
     * @param query 参数
     * @return
     */
    Page<FlowTaskDto> getApplyingTasks(TaskQueryVo query) throws Exception;

    /**
     * 已办任务列表(根据类别)
     * @param query 查询条件
     * @return
     */
    ResultJson finishedList(TaskQueryVo query) throws ParseException;

    /**
     * 已办任务列表
     * @param pageNum    当前页码
     * @param pageSize   每页条数
     * @param procDefKey 流程实例key
     * @param criteria   查询条件
     * @return
     */
    ResultJson finishedList(Integer pageNum, Integer pageSize, String procDefKey, SearchDtoCriteria criteria);

    /**
     * 已办任务列表
     * @param pageNum     当前页码
     * @param pageSize    每页条数
     * @param procDefKeys 流程实例key
     * @param criteria    查询条件
     * @return
     */
    ResultJson finishedList(Integer pageNum, Integer pageSize, List<String> procDefKeys, SearchDtoCriteria criteria);

    /**
     * 获取流程变量
     * @param taskId
     * @return
     */
    ResultJson processVariables(String taskId);

    /**
     * 发送PC待办
     * @param title                 标题
     * @param formData              扩展字段
     * @param backlogUserList       待办人员集合
     * @param processInstance       流程
     * @param username              发起人用户名称
     * @param url                   跳转的url
     * @param alreadyUserListBOList 已办人集合
     * @return
     */
    Boolean sendBacklogList(String title, String formData, List<BacklogUserDto> backlogUserList, ProcessInstance processInstance, String username, String url
            , String pcUrl, List<AlreadyUserListBO> alreadyUserListBOList);

    /**
     * 发送PC待办
     * @param title
     * @param formData
     * @param backlogUserList
     * @param procInstId
     * @param username
     * @param url
     * @param pcUrl
     * @param alreadyUserListBOList
     * @return Boolean
     */
    Boolean sendBacklogList(String title, String formData, List<BacklogUserDto> backlogUserList, String procInstId, String username, String url
            , String pcUrl, List<AlreadyUserListBO> alreadyUserListBOList);

    /**
     * 任务加签
     * @param addSignTaskVo 参数
     * @return ResultJson
     */
    ResultJson addSignTask(AddSignTaskVo addSignTaskVo);

    /**
     * 任务减签
     * @param reduceSignTaskVo 参数
     * @return ResultJson
     */
    ResultJson reduceSignTask(ReduceSignTaskVo reduceSignTaskVo);

    /**
     * 任务加减签
     * @param signTaskVo 参数
     * @return ResultJson
     */
    ResultJson signTask(SignTaskVo signTaskVo);

    /**
     * 锁定任务
     * @param flowTaskVo
     */
    boolean taskLock(FlowTaskVo flowTaskVo);

    /**
     * 解锁任务
     * @param flowTaskVo
     */
    boolean taskUnLock(FlowTaskVo flowTaskVo);

    /**
     * 根据条件历史实例列表查询
     * @param bo
     * @param pageable
     * @return
     */
    Map<String, Object> findHistoricProcessList(HistoricProcessBO bo, Pageable pageable);

    /**
     * 查询行政服务列表
     * @param bo
     * @param pageable
     * @return
     */
    Map<String, Object> queryAdministrative(HistoricProcessBO bo, Pageable pageable);

    /**
     * 保存待阅
     * @param taskUserReadVo
     */
    void saveToBeRead(TaskUserReadVo taskUserReadVo);

    /**
     * 待阅列表
     * @param taskToBeReadQueryCriteria
     */
    org.springframework.data.domain.Page<BpmTaskUserRead> toBeReadList(TaskToBeReadQueryCriteria taskToBeReadQueryCriteria, Pageable pageable);

    /**
     * 已阅列表
     * @param taskHasReadQueryCriteria
     */
    org.springframework.data.domain.Page<BpmTaskUserRead> hasReadList(TaskHasReadQueryCriteria taskHasReadQueryCriteria, Pageable pageable);

    /**
     * 获取当前节点默认处理人
     * @param bpmnModel
     * @param taskKey
     * @param startUser
     * @param executionId
     * @return FlowNextDto
     */
    FlowNextDto currentUserTaskHandle(BpmnModel bpmnModel, String taskKey, UserDto startUser, String executionId);

    /**
     * 任务分发
     * @param task
     * @param currentFlowNode
     * @param assignee
     */
    void distributeNewTask(Task task, FlowNode currentFlowNode, String assignee);

    /**
     * 是否要传后续处理人
     * @param bpmnModel
     * @param taskKey
     * @param userTaskList
     * @param executionId
     * @return boolean
     */
    boolean hasNextTaskFlowNode(BpmnModel bpmnModel, String taskKey, List<FlowElement> userTaskList, String executionId);

    /**
     * 任务是否可批量处理
     * @param procInstanceId
     * @return
     */
    boolean checkBatchHandleTask(String procInstanceId);

    /**
     * 批量处理任务
     * @param procInstanceIds
     * @param remark          意见
     */
    void batchCompleteTask(List<String> procInstanceIds, String remark) throws Exception;

    /**
     * 批量处理任务
     * @param procInstanceIds
     * @param remark          意见
     * @param taskKey         后续节点Key
     */
    void batchCompleteTask(List<String> procInstanceIds, String remark, String taskKey) throws Exception;
}
