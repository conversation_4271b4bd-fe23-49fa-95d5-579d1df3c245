package com.wangda.oa.modules.workflow.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 预签后续任务节点
 * <AUTHOR>
 * @date 2021/5/12 22:59
 */
@Data
@Builder
@ApiModel("预签后续任务节点")
public class FlowNextPreSignTaskDto implements Serializable {

    private static final long serialVersionUID = -8802104551385088520L;

    @ApiModelProperty("任务key")
    private String taskDefKey;

    @ApiModelProperty("用户名")
    private String userNames;

}
