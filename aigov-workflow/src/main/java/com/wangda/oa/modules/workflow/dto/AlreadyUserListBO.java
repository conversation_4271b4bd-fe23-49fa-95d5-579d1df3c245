package com.wangda.oa.modules.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/10 上午10:19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlreadyUserListBO {
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "办理时间，时间戳，秒级(若调用更新时当前人无改变则传入时间和新增传入时间保持一致，该用户则不作处理)")
    private Long transactionTime;
}
