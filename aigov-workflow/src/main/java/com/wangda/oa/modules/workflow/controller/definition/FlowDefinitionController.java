package com.wangda.oa.modules.workflow.controller.definition;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.platform.utils.PageUtil;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserQueryCriteria;
import com.wangda.oa.modules.workflow.dto.workflow.FlowProcDefDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowSaveXmlVo;
import com.wangda.oa.modules.workflow.service.workflow.FlowDefinitionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

/**
 * 工作流程定义
 * <AUTHOR>
 * @date 2021-04-03
 */
@Slf4j
@Api(tags = "流程定义")
@RestController
@RequestMapping("/api/flowable/definition")
@CrossOrigin
public class FlowDefinitionController {

    @Autowired
    private FlowDefinitionService flowDefinitionService;

    @Autowired
    private UserService userService;

    @GetMapping(value = "/list")
    @ApiOperation(value = "流程定义列表", response = FlowProcDefDto.class)
    public ResponseEntity<Object> list(@ApiParam(value = "当前页码", required = true) @RequestParam Integer page,
                               @ApiParam(value = "每页条数", required = true) @RequestParam Integer size, @RequestParam(required = false) String name) {
        Page pageList = flowDefinitionService.list(page, size, name);
        return ResponseEntity.ok(PageUtil.toPage(pageList.getRecords(), pageList.getTotal()));
    }

    @ApiOperation(value = "导入流程文件", notes = "上传bpmn20的xml文件")
    @PostMapping("/import")
    public ResultJson importFile(@RequestParam(required = false) String name,
                                 @RequestParam(required = false) String category,
                                 MultipartFile file) {
        InputStream in = null;
        try {
            in = file.getInputStream();
            flowDefinitionService.importFile(name, category, in);
        } catch (Exception e) {
            log.error("导入失败:", e);
            return ResultJson.generateResult(e.getMessage(), ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                log.error("关闭输入流出错", e);
            }
        }

        return ResultJson.generateResult("导入或修改成功");
    }

    @ApiOperation(value = "读取xml文件")
    @GetMapping("/readXml/{deployId}")
    public ResultJson readXml(@ApiParam(value = "流程定义id") @PathVariable(value = "deployId") String deployId) {
        try {
            return flowDefinitionService.readXml(deployId);
        } catch (Exception e) {
            return ResultJson.generateResult("加载xml文件异常，请检查该流程是否存在", ResultCodeEnum.SYSTEM_ERROR.getCode());
        }

    }

    @ApiOperation(value = "读取xml文件")
    @GetMapping("/readXmlByDefKey/{procDefKey}")
    public ResultJson readXmlByDefKey(@ApiParam(value = "流程定义key") @PathVariable(value = "procDefKey") String procDefKey) {
        try {
            return flowDefinitionService.readXmlByDefKey(procDefKey);
        } catch (Exception e) {
            return ResultJson.generateResult("加载xml文件异常，请检查该流程是否存在", ResultCodeEnum.SYSTEM_ERROR.getCode());
        }

    }

    @ApiOperation(value = "读取图片文件")
    @GetMapping("/readImage/{deployId}")
    public void readImage(@ApiParam(value = "流程定义id") @PathVariable(value = "deployId") String deployId, HttpServletResponse response) {
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(flowDefinitionService.readImage(deployId));
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    @ApiOperation(value = "保存流程设计器内的xml文件")
    @PostMapping("/save")
    public ResultJson save(@RequestBody FlowSaveXmlVo vo) {
        try {
            flowDefinitionService.updateFile(vo);
        } catch (Exception e) {
            log.error("导入失败:", e);
            return ResultJson.generateResult(e.getMessage());
        }
        return ResultJson.generateResult("导入成功");
    }

    @ApiOperation(value = "根据流程定义id启动流程实例")
    @PostMapping("/start/{procDefId}")
    public ResultJson start(@ApiParam(value = "流程定义id") @PathVariable(value = "procDefId") String procDefId,
                            @ApiParam(value = "变量集合,json对象") @RequestBody Map<String, Object> variables) {
        return flowDefinitionService.startProcessInstanceById(procDefId, variables);
    }

    @ApiOperation(value = "根据流程定义key启动流程实例")
    @PostMapping("/startProcess")
    public ResultJson startByKey(@RequestParam String procDefKey, Long startUserId) {
        return flowDefinitionService.startProcessInstanceByKey(procDefKey, startUserId);
    }

    @ApiOperation(value = "激活或挂起流程定义")
    @PutMapping(value = "/updateState")
    public ResultJson updateState(@ApiParam(value = "1:激活,2:挂起", required = true) @RequestParam Integer state,
                                  @ApiParam(value = "流程部署ID", required = true) @RequestParam String deployId) {
        flowDefinitionService.updateState(state, deployId);
        String msg = "挂起成功";
        if(state == 1) {
            msg = "激活成功";
        }
        return ResultJson.generateResult(msg);
    }

    @ApiOperation(value = "删除流程部署")
    @DeleteMapping(value = "/delete")
    public ResultJson delete(@ApiParam(value = "流程部署ID", required = true) @RequestParam String deployId) {
        flowDefinitionService.delete(deployId);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "指定流程办理人员列表")
    @GetMapping("/userList")
    public ResultJson userList(UserQueryCriteria criteria, Pageable pageable) {
        Object list = userService.queryAll(criteria, pageable);
        return ResultJson.generateResult(list);
    }

    @ApiOperation(value = "获取流程定义各Key")
    @GetMapping(value = "/findProcessKeys")
    public ResultJson findProcessKeys(@NotNull String deployId) {
        return ResultJson.generateResult(flowDefinitionService.findProcessKeys(deployId));
    }

}
