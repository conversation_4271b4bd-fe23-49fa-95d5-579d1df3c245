package com.wangda.oa.modules.workflow.dto.workflow;

import com.wangda.oa.modules.workflow.domain.definition.WorkflowDefineInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/13 下午8:40
 */
@Data
public class WorkflowDefineInfoListDto {
    @ApiModelProperty(value = "内容")
    private List<WorkflowDefineInfo> content;
    @ApiModelProperty(value = "总数")
    private Long totalElements;
}
