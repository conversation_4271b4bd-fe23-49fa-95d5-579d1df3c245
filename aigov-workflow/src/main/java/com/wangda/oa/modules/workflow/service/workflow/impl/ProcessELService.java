package com.wangda.oa.modules.workflow.service.workflow.impl;

import com.google.common.collect.Lists;
import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.enums.PositionEnum;
import com.wangda.oa.modules.extension.service.SysDeptUserPositionService;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.DeptDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.dto.workflow.FlowNextDto;
import com.wangda.oa.modules.workflow.factory.CheckMiExecutionConditionCmd;
import com.wangda.oa.modules.workflow.factory.ExpressionExecuteCmd;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.*;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/6
 * @description
 */
@Service
@Slf4j
public class ProcessELService {

    @Autowired
    private SysDeptUserPositionService sysDeptUserPositionService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private ManagementService managementService;

    @Autowired
    private DeptService deptService;

    @Autowired
    private UserService userService;

    /**
     * 获取当前节点用户
     * @param assignee
     * @param taskDefKey
     * @return
     */
    public String getAssignee(String assignee, String taskDefKey) {
        String user = null;
        if (StringUtils.isBlank(assignee)) {
            return null;
        }
        String[] userArr = assignee.split(",");
        for (int i = 0; i < userArr.length; i++) {
            String[] arr = userArr[i].split("\\$");
            if (arr.length == 2) {
                if (taskDefKey.equals(arr[1])) {
                    user = arr[0];
                    break;
                }
            }else {
                user = userArr[i];
                break;
            }
        }
        return StringUtils.isNotEmpty(user) ? user : null;
    }

    /**
     * 解析获取用户列表
     * @param wfAssignee
     * @param taskDefKey
     * @return List
     */
    public List<String> getResolveUsers(String wfAssignee, String taskDefKey) {
        List list = new ArrayList();
        if(StringUtils.isBlank(wfAssignee)) {
            return list;
        }
        String[] userArr = wfAssignee.split(",");
        for (int i = 0; i < userArr.length; i++) {
            String[] arr = userArr[i].split("\\$");
            if (arr.length == 2) {
                if (taskDefKey.equals(arr[1])) {
                    list.add(arr[0]);
                }
            }else{
                list.add(userArr[i]);
            }
        }
        return list;
    }

    /**
     * 用户String转List
     * @param userList
     * @return List
     */
    public List convertMultipleUser(String userList) {
        List list = new ArrayList();
        String[] userArr = userList.split(",");
        for (int i = 0; i < userArr.length; i++) {
            list.add(userArr[i]);
        }
        return list;
    }

    public Boolean isSelectedFlow(String wfSequenceFlow, String currentFlowId) {
        if (StringUtils.isEmpty(wfSequenceFlow)) {
            return false;
        }

        return ("," + wfSequenceFlow + ",").indexOf("," + currentFlowId + ",") > -1;
    }

    /**
     * 当前人是否是负责人
     * @return
     */
    public boolean currentUserIsHead() {
        SysDeptUserPosition sysDeptUserPosition = sysDeptUserPositionService.findUserByUserId(PositionEnum.HEAD.getValue(), SecurityUtils.getCurrentUserId());
        if(Objects.nonNull(sysDeptUserPosition)) {
            return true;
        }
        return false;
    }

    /**
     * 当前人是不是普通人员
     * @return
     */
    public boolean currentUserIsNormal() {
        SysDeptUserPosition sysDeptUserPosition = sysDeptUserPositionService.findUserByUserIdAndTypeIn(SecurityUtils.getCurrentUserId(), Lists.newArrayList(PositionEnum.HEAD.getValue(), PositionEnum.LEADERSHIP.getValue(), PositionEnum.HALL_LEADERSHIP.getValue()));
        if(Objects.isNull(sysDeptUserPosition)) {
            return true;
        }
        return false;
    }

    /**
     * 当前人是不是分管领导
     * @return
     */
    public boolean currentUserIsLeadership() {
        SysDeptUserPosition sysDeptUserPosition = sysDeptUserPositionService.findUserByUserId(PositionEnum.LEADERSHIP.getValue(), SecurityUtils.getCurrentUserId());
        if(Objects.nonNull(sysDeptUserPosition)) {
            return true;
        }
        return false;
    }

    /**
     * 当前人是不是厅领导
     * @return
     */
    public boolean currentUserIsHallLeadership() {
        SysDeptUserPosition sysDeptUserPosition = sysDeptUserPositionService.findUserByUserId(PositionEnum.HALL_LEADERSHIP.getValue(), SecurityUtils.getCurrentUserId());
        if(Objects.nonNull(sysDeptUserPosition)) {
            return true;
        }
        return false;
    }

    /**
     * 当前意见是否已阅
     * （已阅）（已阅。）(已阅！)或者意见是空的，直接结束
     * @return
     */
    public boolean currentRemarkIsHaveReadOrNull(DelegateExecution execution) {
        Object remark = execution.getVariable(ProcessConstants.BPM_BPM_APPROVALREMARK);
        if(Objects.isNull(remark) || "".equals(remark)) {
            return true;
        }

        String regex = "^已阅(.?)";
        Pattern pattern = Pattern.compile(regex);
        if(pattern.matcher(remark.toString().trim()).matches()) {
            return true;
        }
        return false;
    }

    /**
     * 节点是否不存在处理人
     * @return
     */
    public boolean nonUserInNode(DelegateExecution execution, String taskKey) {
        String taskAssignees = (String)execution.getVariable(ProcessConstants.BPM_TASK_ASSIGNEE);
        if(StringUtils.isBlank(taskAssignees)) {
            // 处理需要获取节点处理人的情况
            BpmnModel bpmnModel = repositoryService.getBpmnModel(execution.getProcessDefinitionId());
            FlowNextDto flowNextDto = flowTaskService.currentUserTaskHandle(bpmnModel, taskKey, null, execution.getId());
            if(Objects.isNull(flowNextDto) || StringUtils.isBlank(flowNextDto.taskAssigneeFormatToString())) {
                return true;
            }
            taskAssignees = flowNextDto.taskAssigneeFormatToString();
        }

        List<String> taskAssigneeList = Arrays.asList(taskAssignees.split(ElAdminConstant.SEPARATOR_COMMA));
        for (int i=0; i<taskAssigneeList.size(); i++) {
            String[] assignees = taskAssigneeList.get(i).split("\\$");
            if((assignees.length == 2 && assignees[1].equals(taskKey)) || assignees.length == 1) {
                List<Task> taskList = taskService.createTaskQuery().processInstanceId(execution.getProcessInstanceId()).taskDefinitionKey(taskKey).taskAssignee(assignees[0]).list();
                if (!CollectionUtils.isEmpty(taskList)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 抄送
     * @param usernames
     */
    public String copyTo(String usernames) {
        if(StringUtils.isNotEmpty(usernames)) {
            return usernames;
        }
        return null;
    }

    /**
     * 执行脚本
     * @param script
     */
    public void runScript(ExecutionEntity execution, String script) {
//        ScriptEngine se = new ScriptEngine(1);
//        se.setNeedCheck(false);
//        try {
//            se.importPackage("cc.pubone.framework");
//            se.importPackage("cc.pubone.framework.data");
//            se.importPackage("cc.pubone.framework.orm");
//            se.importPackage("cc.pubone.framework.utility");
//            se.importPackage("cc.pubone.bpm.service");
//            se.importPackage("cc.pubone.bpm.script");
//            se.setVar("execution", execution);
//            se.compileFunction("runScript", script);
//            se.executeFunction("runScript");
//        } catch (EvalException e) {
//            LogUtil.getLogger().error("表达式有误：\n" + script + "\n" + e.getMessage());
//        }
    }

    public boolean evalRouterExpr(DelegateExecution execution, boolean expression, String taskKey) {
        if (!expression) {
            return false;
        } else if (StringUtils.isEmpty(taskKey)) {
            return true;
        } else {
            String taskDefKey = (String)execution.getVariable(ProcessConstants.BPM_TASK_DEF_KEY);
            if(Objects.isNull(taskDefKey)) {
                return true;
            }
            String[] taskDefKeys = taskDefKey.split(ElAdminConstant.SEPARATOR_COMMA);
            if(Arrays.stream(taskDefKeys).anyMatch(taskKey::contains)) {
                return true;
            }
            return false;
        }
    }

    public boolean evalBooleanExpr(DelegateExecution execution, String expr) {
        if(StringUtils.isNotEmpty(expr) && StringUtils.isNotEmpty(execution.getId()) && FlowableUtils.isExpressionRule(expr)) {
            Boolean result = managementService.executeCommand(new ExpressionExecuteCmd(execution.getId(), expr));
            return result;
        }
        return false;
    }

    /**
     * 判断是否为流程最后一个执行人
     */
    public Boolean isLastAssignee(ExecutionEntity execution) {
        String wfProcInstID = execution.getProcessInstanceId();
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(wfProcInstID).list();
        if (!CollectionUtils.isEmpty(taskList) && taskList.size() == 1) {
            return true;
        }
        log.info("判断当前是否为流程最后执行人,当前还有[" + taskList.size() + "]位执行人");
        return false;
    }

    /**
     * 判断是否为流程节点最后一个执行人
     */
    public Boolean isLastAssignee(BpmnModel bpmnModel, Task task) {
        // 当前审批节点
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());
        List<SequenceFlow> outFlows = flowNode.getOutgoingFlows();
        boolean nextIsGateway = false;
        for (SequenceFlow sequenceFlow : outFlows) {
            // 下一个审批节点
            FlowElement targetFlow = sequenceFlow.getTargetFlowElement();
            if(targetFlow instanceof Gateway) {
                nextIsGateway = true;
                break;
            }
        }

        // 如果该节点后续节点为网关的话，查询是否有所有其他流程节点
        TaskQuery taskQuery = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId());
        if(!nextIsGateway) {
            taskQuery.taskDefinitionKey(task.getTaskDefinitionKey());
        }
        List<Task> taskList = taskQuery.list();
        if (!CollectionUtils.isEmpty(taskList) && taskList.size() == 1) {
            return true;
        }

        // 如果是会签，判断完成条件
        if(flowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
            MultiInstanceActivityBehavior multiInstanceActivityBehavior = (MultiInstanceActivityBehavior)flowNode.getBehavior();
            Boolean isCompletion = this.completionConditionSatisfied(task.getExecutionId(), multiInstanceActivityBehavior);
            if(isCompletion) {
                return true;
            }
        }else {
            // 不是会签节点（分发节点）可以选人
            return true;
        }

        log.info("判断当前是否为流程最后执行人,当前还有[" + taskList.size() + "]位执行人");
        return false;
    }

    /**
     * 会签判断是否符合完成条件
     * @param executionId
     * @param multiInstanceActivityBehavior
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean completionConditionSatisfied(String executionId, MultiInstanceActivityBehavior multiInstanceActivityBehavior) {
        Boolean isCompletion = false;
        try {
            isCompletion = managementService.executeCommand(new CheckMiExecutionConditionCmd(executionId, multiInstanceActivityBehavior));
        }catch (Exception e) {
            throw new CustomException("办理提交冲突，请重新提交");
        }
        return isCompletion;
    }

    /**
     * 当前人的部门匹配
     * @return
     */
    public boolean currentUserBelongToDept(String deptName) {
        UserDto userDto = userService.findByName(SecurityUtils.getCurrentUsername());
        DeptDto deptDto = deptService.findById(userDto.getDept().getId());
        List<DeptDto> deptDtoList = deptService.findCurrentAndSuperDept(deptDto, new ArrayList<>());
        if(CollectionUtils.isEmpty(deptDtoList)) {
            return false;
        }
        List<String> deptNames = deptDtoList.stream().map(DeptDto::getName).collect(Collectors.toList());
        return deptNames.contains(deptName);
    }
}
