package com.wangda.oa.modules.workflow.dto;

import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/18 下午8:25
 */
@Data
public class TemplateListDto {
    @ApiModelProperty(value = "内容")
    private List<WdFormTemplate> content;
    @ApiModelProperty(value = "总数")
    private Long totalElements;
}
