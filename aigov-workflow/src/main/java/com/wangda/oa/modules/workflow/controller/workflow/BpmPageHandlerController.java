package com.wangda.oa.modules.workflow.controller.workflow;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.HttpUtil;
import com.google.common.collect.Maps;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.ShortLink;
import com.wangda.oa.modules.extension.service.ShortLinkService;
import com.wangda.oa.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.*;

/**
 * <AUTHOR>
 */
@Api(value = "BpmPageHandlerController", description = "流程页面处理")
@Controller
@CrossOrigin
@Slf4j
@RequestMapping("/api/bpm/handle")
@Validated
@Transactional(readOnly = true)
public class BpmPageHandlerController {

    // 任务服务
    @Autowired
    protected TaskService taskService;

    @Autowired
    private ZwddProperties zwddProperties;

    @Autowired
    private ShortLinkService shortLinkService;

    @ApiOperation(value = "处理PC页面", httpMethod = "GET")
    @AnonymousGetMapping("/webPage")
    public void handleWebPage(@NotBlank String processInstanceId, String taskId, HttpServletResponse response) throws Exception {

        processInstanceId = this.convertProcInstId(processInstanceId);

        // 跳转页
        String handleUrl = zwddProperties.getWebUrl() + "/#/handle?procInsId=" + processInstanceId + "&taskId="+taskId;
        if(StringUtils.isEmpty(taskId)) {
            handleUrl = zwddProperties.getWebUrl() + "/#/handle?procInsId=" + processInstanceId;
        }
        response.sendRedirect(handleUrl);

    }

    @ApiOperation(value = "PC处理页面路由", httpMethod = "GET")
    @GetMapping("/webPageRouter")
    public ResponseEntity webPageRouter(@NotBlank String processInstanceId, String taskId) {

        processInstanceId = this.convertProcInstId(processInstanceId);
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).taskAssignee(SecurityUtils.getCurrentUsername()).active().list();

        Map<String, Object> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(taskList)) {
            taskId = taskList.get(0).getId();
            Map<String, String> query = new HashMap<>();
            query.put("taskId", taskId);
            result.put("path", "/handle");
            result.put("query", query);
        } else {
            Map<String, String> query = new HashMap<>();
            query.put("procInsId", processInstanceId);
            result.put("path", "/handle");
            result.put("query", query);
        }

        log.info("webPageRouter流程跳转参数taskId:" + taskId + " processInstanceId:" + processInstanceId + " userName:" + SecurityUtils.getCurrentUsername());
        return ResponseEntity.ok(result);
    }

    @ApiOperation(value = "处理页面", httpMethod = "GET")
    @AnonymousGetMapping("/page")
    public void handlePage(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> params = Maps.newHashMap();
        Enumeration<String> em = request.getParameterNames();
        while (em.hasMoreElements()) {
            String name = em.nextElement();
            String value = request.getParameter(name);
            params.put(name, value);
        }

        if(Objects.nonNull(params.get("processInstanceId"))) {
            params.replace("processInstanceId", this.convertProcInstId(params.get("processInstanceId").toString()));
        }

        String handleUrl = HttpUtil.urlWithForm(zwddProperties.getAppUrl() + "/#/bpmJumpPage", params, CharsetUtil.CHARSET_UTF_8, true);
        log.info("page处理页面跳转参数: " + handleUrl);
        response.sendRedirect(handleUrl);
    }

    @ApiOperation(value = "处理页面路由", httpMethod = "GET")
    @GetMapping("/pageRouter")
    public ResponseEntity pageRouter(@NotBlank String processInstanceId, String taskId, String userName) {

        String username = userName;
        try {
            if (StringUtils.isBlank(username)) {
                username = SecurityUtils.getCurrentUsername();
            }
        }catch (Exception e) {
            log.error("pageRouter token错误", e.getMessage());
        }

        processInstanceId = this.convertProcInstId(processInstanceId);
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).taskAssignee(username).active().list();

        Map<String, Object> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(taskList)) {
            taskId = taskList.get(0).getId();
            Map<String, String> query = new HashMap<>();
            query.put("taskId", taskId);
            result.put("path", "/bpm/handle");
            result.put("query", query);
        } else {
            Map<String, String> query = new HashMap<>();
            query.put("processInstanceId", processInstanceId);
            result.put("path", "/bpmRead/browse");
            result.put("query", query);
        }

        log.info("pageRouter流程跳转参数taskId:" + taskId + " processInstanceId:" + processInstanceId + " userName:" + username);
        return ResponseEntity.ok(result);
    }

    // 流程重新恢复后，旧流程实例id转换新的
    private String convertProcInstId(String procInstId) {
        ShortLink shortLink = shortLinkService.get(procInstId);
        if(Objects.nonNull(shortLink)) {
            procInstId = shortLink.getLongUrl();
        }
        return procInstId;
    }

}
