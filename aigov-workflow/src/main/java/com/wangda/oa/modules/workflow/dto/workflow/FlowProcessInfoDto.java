package com.wangda.oa.modules.workflow.dto.workflow;

import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-04-03
 * 流程信息
 */
@Data
@Builder
@ApiModel("工作流任务相关-处理单结构")
public class FlowProcessInfoDto implements Serializable {

    private static final long serialVersionUID = 78824676723477013L;

    @ApiModelProperty("流程标题")
    private String title;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("当前环节")
    private String taskNames;

    @ApiModelProperty("当前办理人")
    private String taskAssignees;

    @ApiModelProperty("当前抄送人")
    private String taskCopyToUsers;

    @ApiModelProperty("流程实例id")
    private String processInstanceId;

    @ApiModelProperty("流程定义id")
    private String procDefId;

    @ApiModelProperty("流程部署id")
    private String procDeployId;

    @ApiModelProperty("流程定义key")
    private String procDefKey;

    @ApiModelProperty("流程状态")
    private ProcStatusEnum procStatus;

}
