package com.wangda.oa.modules.workflow.repository.definition;

import com.wangda.oa.modules.workflow.domain.definition.WorkflowDeployForm;
import com.wangda.oa.modules.workflow.domain.workflow.vo.DeployFormVo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/04/14 15:10
 */
@Repository
public interface WorkflowDeployFormRepository extends JpaRepository<WorkflowDeployForm,Long>, JpaSpecificationExecutor<WorkflowDeployForm> {

    @Query(value="SELECT new com.wangda.oa.modules.workflow.domain.workflow.vo.DeployFormVo(df.id, wt.id, wt.title, wt.formJson, df.form<PERSON>kin, df.mobileFormSkin, df.mobileFormId, wt2.formJson) FROM WorkflowDeployForm df " +
            "LEFT JOIN WdFormTemplate wt ON df.formId = wt.id " +
            "LEFT JOIN WdFormTemplate wt2 ON df.mobileFormId = wt2.id " +
            "WHERE df.deployId = :deployId")
    DeployFormVo findByDeployId(String deployId);

    List<WorkflowDeployForm> findByFormId(Long id);
}
