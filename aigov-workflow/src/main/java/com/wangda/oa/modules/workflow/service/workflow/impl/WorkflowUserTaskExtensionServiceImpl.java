package com.wangda.oa.modules.workflow.service.workflow.impl;

import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.modules.workflow.bo.flow.*;
import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskButtons;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskFormRw;
import com.wangda.oa.modules.workflow.dto.workflow.FlowProcDefDto;
import com.wangda.oa.modules.workflow.dto.workflow.UserTaskListDto;
import com.wangda.oa.modules.workflow.mapper.WorkflowUserTaskExtensionMapper;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.repository.workflow.WorkflowUserTaskButtonsRepository;
import com.wangda.oa.modules.workflow.repository.workflow.WorkflowUserTaskExtensionRepository;
import com.wangda.oa.modules.workflow.repository.workflow.WorkflowUserTaskFormRwRepository;
import com.wangda.oa.modules.workflow.service.workflow.FlowDefinitionService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowUserTaskExtensionService;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午8:34
 */
@Service
public class WorkflowUserTaskExtensionServiceImpl implements WorkflowUserTaskExtensionService {

    @Resource
    private WorkflowUserTaskExtensionRepository workflowUserTaskExtensionRepository;

    @Resource
    private WorkflowUserTaskButtonsRepository workflowUserTaskButtonsRepository;

    @Resource
    private WorkflowUserTaskFormRwRepository workflowUserTaskFormRwRepository;

    @Resource
    private WdFormTemplateRepository wdFormTemplateRepository;

    @Resource
    private WorkflowUserTaskExtensionMapper workflowUserTaskExtensionMapper;

    @Autowired
    private FlowDefinitionService flowDefinitionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdateExtension(WorkflowUserTaskExtensionBO bo) {
        WorkflowUserTaskExtension workflowUserTaskExtension=new WorkflowUserTaskExtension();
        int number;
        if(bo.getId()!=null){
            workflowUserTaskExtension=workflowUserTaskExtensionRepository.findById(bo.getId()).orElse(null);
            if(workflowUserTaskExtension==null){
                throw new BadRequestException("数据不存在");
            }
            number=workflowUserTaskExtensionRepository.countBySortAndIdNot(bo.getSort(),bo.getId());
        }else{
            number=workflowUserTaskExtensionRepository.countBySort(bo.getSort());
        }
        if(number>0){
            //序号存在，则原先大于等于该序号的都累加
            workflowUserTaskExtensionMapper.updateSortBySort(bo.getSort());
        }
        BeanUtils.copyProperties(bo,workflowUserTaskExtension);
        workflowUserTaskExtensionRepository.save(workflowUserTaskExtension);
        return workflowUserTaskExtension.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdateButton(WorkflowUserTaskButtonBO bo) {
        WorkflowUserTaskButtons workflowUserTaskButtons=new WorkflowUserTaskButtons();
        int number;
        if(bo.getId()!=null){
            workflowUserTaskButtons=workflowUserTaskButtonsRepository.findById(bo.getId()).orElse(null);
            if(workflowUserTaskButtons==null){
                throw new BadRequestException("数据不存在");
            }
            number=workflowUserTaskButtonsRepository.countBySortAndIdNot(bo.getSort(),bo.getId());
        }else{
            number=workflowUserTaskButtonsRepository.countBySort(bo.getSort());
        }
        if(number>0){
            //序号存在，则原先大于等于该序号的都累加
            workflowUserTaskExtensionMapper.updateButtonSortBySort(bo.getSort());
        }
        BeanUtils.copyProperties(bo,workflowUserTaskButtons);
        workflowUserTaskButtonsRepository.save(workflowUserTaskButtons);
        return workflowUserTaskButtons.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdateFormRw(WorkflowUserTaskFormRwBO bo) {
        WorkflowUserTaskFormRw workflowUserTaskFormRw=new WorkflowUserTaskFormRw();
        if(bo.getId()!=null){
            workflowUserTaskFormRw=workflowUserTaskFormRwRepository.findById(bo.getId()).orElse(null);
            if(workflowUserTaskFormRw==null){
                throw new BadRequestException("数据不存在");
            }
        }
        if(bo.getTemplateId()!=null){
            WdFormTemplate wdFormTemplate=wdFormTemplateRepository.findById(bo.getTemplateId()).orElse(null);
            if(wdFormTemplate==null){
                throw new BadRequestException("模板不存在");
            }
            workflowUserTaskFormRw.setTemplate(wdFormTemplate);
        }
        BeanUtils.copyProperties(bo,workflowUserTaskFormRw);
        workflowUserTaskFormRwRepository.save(workflowUserTaskFormRw);
        return workflowUserTaskFormRw.getId();
    }

    @Override
    public UserTaskListDto getList(ListBO bo) {
        UserTaskListDto dto = new UserTaskListDto();
        Pageable pageable = PageRequest.of(bo.getPage(),bo.getSize(), Sort.by("sort"));
        Page<WorkflowUserTaskExtension> page = null;
        if(StringUtils.isNotEmpty(bo.getFlowDefineId())) {
            page = workflowUserTaskExtensionRepository.findAllByFlowDefineId(bo.getFlowDefineId(), pageable);
        }else {
            page = workflowUserTaskExtensionRepository.findAll(pageable);
        }

        // 转换流程标识
        List<WorkflowUserTaskExtension> workflowUserTaskExtensionList = page.getContent();
        if(!CollectionUtils.isEmpty(workflowUserTaskExtensionList)) {

            List<FlowProcDefDto> procDefDtoList = flowDefinitionService.list(SystemConstant.NUMBER_ZERO, SystemConstant.NUMBER_MAX_HUNDRED, null).getRecords();
            Map<String, String> procDefMap = new HashMap<>();
            procDefDtoList.forEach(flowProcDefDto -> {
                procDefMap.put(flowProcDefDto.getDeploymentId(), flowProcDefDto.getName() + ElAdminConstant.SEPARATOR_COLON + flowProcDefDto.getVersion());
            });

            workflowUserTaskExtensionList.forEach(workflowUserTaskExtension -> {
                workflowUserTaskExtension.setFlowDefineName(procDefMap.get(workflowUserTaskExtension.getFlowDefineId()));
            });
        }

        dto.setContent(workflowUserTaskExtensionList);
        dto.setTotalElements(page.getTotalElements());
        return dto;
    }

    @Override
    public Boolean deleteExtension(InfoBO bo) {
        workflowUserTaskButtonsRepository.deleteByTaskDefinitionId(bo.getId());
        workflowUserTaskFormRwRepository.deleteByTaskDefinitionId(bo.getId());
        workflowUserTaskExtensionRepository.deleteById(bo.getId());
        return true;
    }

    @Override
    public Boolean deleteFormRw(InfoBO bo) {
        workflowUserTaskFormRwRepository.deleteById(bo.getId());
        return true;
    }

    @Override
    public Boolean deleteButton(InfoBO bo) {
        workflowUserTaskButtonsRepository.deleteById(bo.getId());
        return true;
    }

    @Override
    public WorkflowUserTaskExtension getInfoByKey(GetInfoByKeyBO bo) {
        List<WorkflowUserTaskExtension> list = workflowUserTaskExtensionRepository.findByFlowDefineIdAndLinkKey(bo.getFlowDefineId(), bo.getLinkKey());
        if(list==null || list.size()<1){
            return null;
        }
        return list.get(0);
    }
}
