package com.wangda.oa.modules.workflow.service.workflow.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/6
 * @description
 */
@Service
@Slf4j
public class Printer {

    public String printMessage() {
        log.info("流程结束");
        return "办结";
    }

    public String out(String msg) {
        log.info("流程:" + msg);
        return msg;
    }
}
