package com.wangda.oa.modules.workflow.utils;

import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.workflow.vo.TaskAssigneeVo;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskApplicantDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskUserDto;
import com.wangda.oa.utils.ElAdminConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 流程引擎工具类封装
 * @author: linjinp
 * @create: 2019-12-24 13:51
 **/
public class FlowableUtils {

    public static final Logger log = LogManager.getLogger(FlowableUtils.class);

    // 表达式规则
    public static final Pattern EXPRESSION_PATTERN = Pattern.compile("\\$\\{(.*?)}");

    /**
     * 根据节点，获取入口连线
     * @param source
     * @return
     */
    public static List<SequenceFlow> getElementIncomingFlows(FlowElement source) {
        List<SequenceFlow> sequenceFlows = null;
        if (source instanceof FlowNode) {
            sequenceFlows = ((FlowNode) source).getIncomingFlows();
        } else if (source instanceof Gateway) {
            sequenceFlows = ((Gateway) source).getIncomingFlows();
        } else if (source instanceof SubProcess) {
            sequenceFlows = ((SubProcess) source).getIncomingFlows();
        } else if (source instanceof StartEvent) {
            sequenceFlows = ((StartEvent) source).getIncomingFlows();
        } else if (source instanceof EndEvent) {
            sequenceFlows = ((EndEvent) source).getIncomingFlows();
        }
        return sequenceFlows;
    }

    /**
     * 根据节点，获取出口连线
     * @param source
     * @return
     */
    public static List<SequenceFlow> getElementOutgoingFlows(FlowElement source) {
        List<SequenceFlow> sequenceFlows = null;
        if (source instanceof FlowNode) {
            sequenceFlows = ((FlowNode) source).getOutgoingFlows();
        } else if (source instanceof Gateway) {
            sequenceFlows = ((Gateway) source).getOutgoingFlows();
        } else if (source instanceof SubProcess) {
            sequenceFlows = ((SubProcess) source).getOutgoingFlows();
        } else if (source instanceof StartEvent) {
            sequenceFlows = ((StartEvent) source).getOutgoingFlows();
        } else if (source instanceof EndEvent) {
            sequenceFlows = ((EndEvent) source).getOutgoingFlows();
        }
        return sequenceFlows;
    }

    /**
     * 获取全部节点列表，包含子流程节点
     * @param flowElements
     * @param allElements
     * @return
     */
    public static Collection<FlowElement> getAllElements(Collection<FlowElement> flowElements, Collection<FlowElement> allElements) {
        allElements = allElements == null ? new ArrayList<>() : allElements;

        for (FlowElement flowElement : flowElements) {
            allElements.add(flowElement);
            if (flowElement instanceof SubProcess) {
                // 继续深入子流程，进一步获取子流程
                allElements = FlowableUtils.getAllElements(((SubProcess) flowElement).getFlowElements(), allElements);
            }
        }
        return allElements;
    }

    /**
     * 获取全部用户节点列表，包含子流程用户节点
     * @param flowElements
     * @param allElements
     * @return
     */
    public static Collection<UserTask> getAllUserTaskElements(Collection<FlowElement> flowElements, Collection<UserTask> allElements) {
        allElements = allElements == null ? new ArrayList<>() : allElements;

        for (FlowElement flowElement : flowElements) {
            if(flowElement instanceof UserTask) {
                allElements.add((UserTask)flowElement);
            }
            if (flowElement instanceof SubProcess) {
                // 继续深入子流程，进一步获取子流程
                allElements = FlowableUtils.getAllUserTaskElements(((SubProcess) flowElement).getFlowElements(), allElements);
            }
        }
        return allElements;
    }

    /**
     * 获取流程启动节点
     * @param flowElements
     * @return
     */
    public static FlowElement findStartFlowElement(Collection<FlowElement> flowElements) {
        Collection<FlowElement> allElements = getAllElements(flowElements, null);
        for (FlowElement flowElement : allElements) {
            // 启动节点
            if (flowElement instanceof StartEvent) {
                return flowElement;
            }
        }
        return null;
    }

    /**
     * 根据正在运行的节点迭代获取后续所有节点
     * @param source
     * @param hasSequenceFlow
     * @param flowElementList
     * @return
     */
    public static List<FlowElement> iteratorFindOutgoingFlowElements(FlowElement source, Set<String> hasSequenceFlow, List<FlowElement> flowElementList) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        flowElementList = flowElementList == null ? new ArrayList<>() : flowElementList;

        // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
        if (source instanceof EndEvent && source.getSubProcess() != null) {
            flowElementList = iteratorFindOutgoingFlowElements(source.getSubProcess(), hasSequenceFlow, flowElementList);
        }

        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow: sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                flowElementList.add(sequenceFlow);

                FlowElement targetFlowElement = sequenceFlow.getTargetFlowElement();
                // 如果为用户任务类型，且任务节点的 Key 正在运行的任务中存在，添加
                if (targetFlowElement instanceof UserTask || targetFlowElement instanceof ServiceTask || targetFlowElement instanceof Gateway || targetFlowElement instanceof EndEvent) {
                    flowElementList.add(targetFlowElement);
                }else if (targetFlowElement instanceof SubProcess) {
                    // 如果节点为子流程节点情况，则从节点中的第一个节点开始获取
                    List<FlowElement> childUserTaskList = iteratorFindOutgoingFlowElements((FlowElement) (((SubProcess) targetFlowElement).getFlowElements().toArray()[0]), hasSequenceFlow, null);
                    // 如果找到节点，则说明该线路找到节点，不继续向下找，反之继续
                    if (childUserTaskList != null && childUserTaskList.size() > 0) {
                        flowElementList.addAll(childUserTaskList);
                        continue;
                    }
                }
                // 继续迭代
                flowElementList = iteratorFindOutgoingFlowElements(targetFlowElement, hasSequenceFlow, flowElementList);
            }
        }
        return flowElementList;
    }

    /**
     * 根据正在运行的任务节点，获取后续用户节点
     * @param source 起始节点
     * @param hasSequenceFlow 已经经过的连线的 ID，用于判断线路是否重复
     * @param userTaskList 后续用户任务列表
     * @return
     */
    public static List<UserTask> findOutgoingUserTasks(FlowElement source, Set<String> hasSequenceFlow, List<UserTask> userTaskList) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        userTaskList = userTaskList == null ? new ArrayList<>() : userTaskList;

        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementOutgoingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow: sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());

                FlowElement targetElement = sequenceFlow.getTargetFlowElement();
                if (targetElement instanceof UserTask) {
                    // 如果为用户任务类型添加
                    userTaskList.add((UserTask)targetElement);
                } else if (targetElement instanceof Gateway) {
                    // 下个节点为网关，继续查找
                    userTaskList = findOutgoingUserTasks(targetElement, hasSequenceFlow, userTaskList);
                } else if(targetElement instanceof SubProcess) {
                    // 如果节点为子流程节点情况，则从节点中的第一个节点开始获取
                    List<UserTask> childUserTaskList = findOutgoingUserTasks((FlowElement) (((SubProcess) targetElement).getFlowElements().toArray()[0]), hasSequenceFlow, null);
                    // 如果找到节点，则说明该线路找到节点，不继续向下找，反之继续
                    if (!CollectionUtils.isEmpty(childUserTaskList)) {
                        userTaskList.addAll(childUserTaskList);
                    }
                } else if (targetElement instanceof EndEvent && targetElement.getParentContainer() != null) {
                    // 当前为子流程结束节点，获取父流程后续节点
                    FlowElementsContainer parentContainer = targetElement.getParentContainer();
                    if (parentContainer instanceof SubProcess) {
                        userTaskList = findOutgoingUserTasks((FlowElement)parentContainer, hasSequenceFlow, userTaskList);
                    }
                }
            }
        }
        return userTaskList;
    }

    /**
     * 获取该流程节点和后续所有用户节点关系
     * @param process 流程定义
     * @return
     */
    public static Map<String, List<UserTask>> handleElementsTaskAfterRelation(Process process) {
        Map<String, List<UserTask>> userTaskMap = new HashMap<>();

        // 获取用户节点元素
        Collection<UserTask> userTaskElements = getAllUserTaskElements(process.getFlowElements(), null);
        if (!CollectionUtils.isEmpty(userTaskElements)) {
            for (UserTask userTaskElement: userTaskElements) {
                if (!userTaskMap.containsKey(userTaskElement.getId())) {
                    userTaskMap.put(userTaskElement.getId(), findOutgoingUserTasks(userTaskElement, null, null));
                }
            }
        }
        return userTaskMap;
    }

    /**
     * 迭代从后向前扫描，判断目标节点相对于当前节点是否是串行
     * 不存在直接回退到子流程中的情况，但存在从子流程出去到父流程情况
     * @param source 起始节点
     * @param isSequential 是否串行
     * @param hasSequenceFlow 已经经过的连线的 ID，用于判断线路是否重复
     * @param targetKsy 目标节点
     * @return
     */
    public static Boolean iteratorCheckSequentialReferTarget(FlowElement source, String targetKsy, Set<String> hasSequenceFlow, Boolean isSequential) {
        isSequential = isSequential == null ? true : isSequential;
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;

        // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
        if (source instanceof StartEvent && source.getSubProcess() != null) {
            isSequential = iteratorCheckSequentialReferTarget(source.getSubProcess(), targetKsy, hasSequenceFlow, isSequential);
        }

        // 根据类型，获取入口连线
        List<SequenceFlow> sequenceFlows = getElementIncomingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow: sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 如果目标节点已被判断为并行，后面都不需要执行，直接返回
                if (isSequential == false) {
                    break;
                }
                // 这条线路存在目标节点，这条线路完成，进入下个线路
                if (targetKsy.equals(sequenceFlow.getSourceFlowElement().getId())) {
                    isSequential = true;
                    break;
                }
                if (sequenceFlow.getSourceFlowElement() instanceof StartEvent) {
                    isSequential = false;
                    break;
                }
                // 否则就继续迭代
                isSequential = iteratorCheckSequentialReferTarget(sequenceFlow.getSourceFlowElement(), targetKsy, hasSequenceFlow, isSequential);
            }
        }
        return isSequential;
    }

    /**
     * 从后向前寻路，获取到达节点的所有路线
     * 不存在直接回退到子流程，但是存在回退到父级流程的情况
     * @param source 起始节点
     * @param passRoads 已经经过的点集合
     * @param roads 路线
     * @return
     */
    public static List<List<UserTask>> findRoad(FlowElement source, List<UserTask> passRoads, Set<String> hasSequenceFlow, List<List<UserTask>> roads) {
        passRoads = passRoads == null ? new ArrayList<>() : passRoads;
        roads = roads == null ? new ArrayList<>() : roads;
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;

        // 如果该节点为开始节点，且存在上级子节点，则顺着上级子节点继续迭代
        if (source instanceof StartEvent && source.getSubProcess() != null) {
            roads = findRoad(source.getSubProcess(), passRoads, hasSequenceFlow, roads);
        }

        // 根据类型，获取入口连线
        List<SequenceFlow> sequenceFlows = getElementIncomingFlows(source);

        if (sequenceFlows != null && sequenceFlows.size() != 0) {
            for (SequenceFlow sequenceFlow: sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());
                // 添加经过路线
                if (sequenceFlow.getSourceFlowElement() instanceof UserTask) {
                    passRoads.add((UserTask) sequenceFlow.getSourceFlowElement());
                }
                // 继续迭代
                roads = findRoad(sequenceFlow.getSourceFlowElement(), passRoads, hasSequenceFlow, roads);
            }
        } else {
            // 添加路线
            roads.add(passRoads);
        }
        return roads;
    }

    /**
     * 从后向前寻路，获取到达节点的直接路线
     * 不存在直接回退到子流程，但是存在回退到父级流程的情况
     * @param source 起始节点
     * @param hasSequenceFlow 已经经过的点集合
     * @param sequenceFlowList 路线
     * @return
     */
    public static List<SequenceFlow> findIncomingSequenceFlows(FlowElement source, Set<String> hasSequenceFlow, List<SequenceFlow> sequenceFlowList) {
        hasSequenceFlow = hasSequenceFlow == null ? new HashSet<>() : hasSequenceFlow;
        sequenceFlowList = sequenceFlowList == null ? new ArrayList<>() : sequenceFlowList;

        // 根据类型，获取出口连线
        List<SequenceFlow> sequenceFlows = getElementIncomingFlows(source);
        if (sequenceFlows != null) {
            // 循环找到目标元素
            for (SequenceFlow sequenceFlow: sequenceFlows) {
                // 如果发现连线重复，说明循环了，跳过这个循环
                if (hasSequenceFlow.contains(sequenceFlow.getId())) {
                    continue;
                }
                // 添加已经走过的连线
                hasSequenceFlow.add(sequenceFlow.getId());

                FlowElement sourceElement = sequenceFlow.getSourceFlowElement();
                if (sourceElement instanceof UserTask) {
                    // 如果为用户任务类型添加
                    sequenceFlowList.add(sequenceFlow);
                } else if (sourceElement instanceof Gateway) {
                    // 下个节点为网关，继续查找
                    sequenceFlowList = findIncomingSequenceFlows(sourceElement, hasSequenceFlow, sequenceFlowList);
                } else if(sourceElement instanceof SubProcess) {
                    // 如果节点为子流程节点情况，则从节点中的最后一个节点开始获取
                    Collection subFlowElements = ((SubProcess) sourceElement).getFlowElements();
                    List<SequenceFlow> childSequenceFlowList = findIncomingSequenceFlows((FlowElement) (subFlowElements.toArray()[subFlowElements.size()-1]), hasSequenceFlow, null);
                    // 如果找到节点，则说明该线路找到节点，不继续向下找，反之继续
                    if (!CollectionUtils.isEmpty(childSequenceFlowList)) {
                        sequenceFlowList.addAll(childSequenceFlowList);
                    }
                } else if (sourceElement instanceof StartEvent && sourceElement.getParentContainer() != null) {
                    // 当前为子流程结束节点，获取父流程后续节点
                    FlowElementsContainer parentContainer = sourceElement.getParentContainer();
                    if (parentContainer instanceof SubProcess) {
                        sequenceFlowList = findIncomingSequenceFlows((FlowElement)parentContainer, hasSequenceFlow, sequenceFlowList);
                    }
                }
            }
        }
        return sequenceFlowList;
    }

    /**
     * 解析标题规则 ${}
     * @param formDataObj
     * @param subjectRule
     * @return String
     */
    public static String parseSubjectRule(JSONObject formDataObj, String subjectRule) {
        //懒匹配${}
        Matcher matcher;
        //自旋进行最小匹配，直到无法匹配
        while((matcher = EXPRESSION_PATTERN.matcher(subjectRule)).find()) {
            String key = matcher.group().replaceAll("\\$\\{|}", "");
            //替换匹配内容
            if(StringUtils.isNotEmpty(formDataObj.getString(key))) {
                subjectRule = subjectRule.replace(matcher.group(), formDataObj.getString(key));
            }else {
                subjectRule = subjectRule.replace(matcher.group(), "");
            }
        }
        return subjectRule;
    }

    /**
     * 是否是自定义表达式 ${}
     * @param expression
     * @return String
     */
    public static boolean isExpressionRule(String expression) {
        //懒匹配${}
        if(EXPRESSION_PATTERN.matcher(expression).matches()) {
            return true;
        }
        return false;
    }

    /**
     * 设置处理人信息
     * @param flowTask
     * @param sysUser
     */
    public static void setAssigneeUserInfo(FlowTaskDto flowTask, UserDto sysUser) {
        if(Objects.isNull(sysUser)) {
            log.error("设置处理人信息错误，系统找不到处理人");
            return;
        }
        FlowTaskUserDto flowTaskUserDto;
        if(Objects.nonNull(flowTask.getAssigneeInfo())) {
            flowTaskUserDto = flowTask.getAssigneeInfo();
        }else {
            flowTaskUserDto = new FlowTaskUserDto();
            flowTask.setAssigneeInfo(flowTaskUserDto);
        }
        flowTaskUserDto.setAssigneeId(sysUser.getId());
        flowTaskUserDto.setAssigneeUsername(sysUser.getUsername());
        flowTaskUserDto.setAssigneeName(sysUser.getNickName());
        flowTaskUserDto.setDeptName(sysUser.getDept().getName());
    }

    /**
     * 设置启动人信息
     * @param flowTask
     * @param startUser
     */
    public static void setStartUserInfo(FlowTaskDto flowTask, UserDto startUser) {
        if(Objects.isNull(startUser)) {
            log.error("设置启动人信息错误，系统找不到启动人");
            return;
        }
        FlowTaskApplicantDto flowTaskApplicantDto;
        if(Objects.nonNull(flowTask.getApplicantInfo())) {
            flowTaskApplicantDto = flowTask.getApplicantInfo();
        }else {
            flowTaskApplicantDto = new FlowTaskApplicantDto();
            flowTask.setApplicantInfo(flowTaskApplicantDto);
        }
        flowTaskApplicantDto.setStartUserId(startUser.getId());
        flowTaskApplicantDto.setStartUsername(startUser.getUsername());
        flowTaskApplicantDto.setStartNickName(startUser.getNickName());
        flowTaskApplicantDto.setStartDeptName(startUser.getDept().getName());
        flowTaskApplicantDto.setStartTime(flowTask.getCreateTime());
    }

    /**
     * 流程完成时间处理
     * @param ms
     * @return
     */
    public static String getDate(long ms) {
        long day = ms / (24 * 60 * 60 * 1000);
        long hour = (ms / (60 * 60 * 1000) - day * 24);
        long minute = ((ms / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long second = (ms / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60);

        if (day > 0) {
            return day + "天" + hour + "小时" + minute + "分钟";
        }
        if (hour > 0) {
            return hour + "小时" + minute + "分钟";
        }
        if (minute > 0) {
            return minute + "分钟";
        }
        if (second > 0) {
            return second + "秒";
        } else {
            return 0 + "秒";
        }
    }

    /**
     * 查找出现次数
     * @param sourceText 源字符串
     * @param appearText 查找字符串
     * @return 次数
     */
    public static int countAppear(String sourceText, String appearText) {
        int count = 0;
        if(Objects.isNull(sourceText) || Objects.isNull(appearText)) {
            return count;
        }
        Pattern p = Pattern.compile(appearText);
        Matcher m = p.matcher(sourceText);
        while (m.find()) {
            count++;
        }
        return count;
    }

    /**
     * 格式化预签数据
     * @param preSignAssigneeVar
     * @return
     */
    public static Map<String, String> formatPreSignAssignee(String preSignAssigneeVar) {
        Map<String, String> result = new HashMap<>();
        if(StringUtils.isBlank(preSignAssigneeVar)) {
            return result;
        }
        String[] preSignArr = preSignAssigneeVar.split("#");
        for (int i = 0; i < preSignArr.length; i++) {
            String[] arr = preSignArr[i].split(":");
            if (arr.length == 2) {
                result.put(arr[0], arr[1]);
            }else {
                result.put(arr[0], null);
            }
        }
        return result;
    }

    // 后续节点处理人
    public static List<String> parseNextTaskAssignee(String taskAssignees) {
        List<String> assigneeList = new ArrayList<>();
        if(StringUtils.isNotBlank(taskAssignees)) {
            String[] assigneesArray = taskAssignees.split(ElAdminConstant.SEPARATOR_COMMA);
            for (int i=0; i<assigneesArray.length; i++) {
                String[] taskAssignee = assigneesArray[i].split("\\$");
                assigneeList.add(taskAssignee[0]);
            }
        }
        return assigneeList;
    }

    // 后续节点处理人
    public static List<TaskAssigneeVo> parseNextTaskAssigneeVo(String taskAssignees) {
        List<TaskAssigneeVo> assigneeList = new ArrayList<>();
        if(StringUtils.isNotEmpty(taskAssignees)) {
            String[] assigneesArray = taskAssignees.split(ElAdminConstant.SEPARATOR_COMMA);
            for (int i=0; i<assigneesArray.length; i++) {
                String[] taskAssignee = assigneesArray[i].split("\\$");
                TaskAssigneeVo taskAssigneeVo = new TaskAssigneeVo();
                taskAssigneeVo.setTaskKey(taskAssignee[1]);
                taskAssigneeVo.setUsername(taskAssignee[0]);
                assigneeList.add(taskAssigneeVo);
            }
        }
        return assigneeList;
    }

    // 解析后续节点key
    public static List<String> parseNextTaskNode(String taskAssignees) {
        List<String> nodeKeyList = new ArrayList<>();
        if(StringUtils.isNotEmpty(taskAssignees)) {
            String[] preSignArr = taskAssignees.split(ElAdminConstant.SEPARATOR_COMMA);
            for (int i = 0; i < preSignArr.length; i++) {
                String[] arr = preSignArr[i].split("\\$");
                if (arr.length == 2) {
                    nodeKeyList.add(arr[1]);
                }
            }
        }
        return nodeKeyList;
    }

    // 后续节点处理人格式化
    public static String taskAssigneeFormatToString(List<TaskAssigneeVo> taskAssignees) {
        StringBuilder assignee = new StringBuilder();
        if(!CollectionUtils.isEmpty(taskAssignees)) {
            taskAssignees.forEach(taskAssigneeVo -> {
                assignee.append(taskAssigneeVo.getUsername()).append(ElAdminConstant.SEPARATOR_DOLLAR)
                        .append(taskAssigneeVo.getTaskKey()).append(ElAdminConstant.SEPARATOR_COMMA);
            });
        }
        if(assignee.length() == 0) {
            return assignee.toString();
        }
        return assignee.substring(0, assignee.length()-1);
    }

    /**
     * 验证是否是合法json字符串
     * @param jsonString
     * @return
     */
    public static boolean isJSONValid(String jsonString) {
        try {
            JSONObject.parse(jsonString);
        }catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 获取所有意见上表单字段
     * @param process 主流程
     * @return
     */
    public static List<String> findAllRemarkToFormField(Process process) {
        List<String> remarkToFormFields = new ArrayList<>();
        if(Objects.isNull(process)) {
            return remarkToFormFields;
        }

        Collection<UserTask> userTasks = getAllUserTaskElements(process.getFlowElements(), null);
        for (UserTask userTask: userTasks) {
            String remarkToFormField = userTask.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
            if(StringUtils.isNotEmpty(remarkToFormField)) {
                remarkToFormFields.add(remarkToFormField);
            }
        }
        return remarkToFormFields;
    }

    /**
     * 是否有多实例节点
     * @param bpmnModel
     * @param taskNodes
     * @return
     */
    public static boolean hasMultiInstanceNode(BpmnModel bpmnModel, String taskNodes) {
        if(StringUtils.isEmpty(taskNodes) || Objects.isNull(bpmnModel)) {
            return false;
        }
        String[] taskKeys = taskNodes.split(ElAdminConstant.SEPARATOR_COMMA);
        for(int i=0; i< taskKeys.length; i++) {
            FlowNode currentFlowNode = (FlowNode)bpmnModel.getFlowElement(taskKeys[i]);
            if(Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
                return true;
            }
        }
        return false;
    }
}
