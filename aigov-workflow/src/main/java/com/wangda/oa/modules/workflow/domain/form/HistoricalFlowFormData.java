package com.wangda.oa.modules.workflow.domain.form;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/1
 * @description 表单数据历史版本
 */
@Data
@Entity
public class HistoricalFlowFormData extends BaseDomain {

    @NotBlank
    @ApiModelProperty(value = "流程实例id")
    private String procInstId;

    @NotNull
    @ApiModelProperty(value = "业务表单数据JSON")
    @Lob
    private String formData;

    @ApiModelProperty(value = "操作人名称")
    private String operator;
}
