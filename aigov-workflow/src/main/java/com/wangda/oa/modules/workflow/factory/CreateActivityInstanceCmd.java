package com.wangda.oa.modules.workflow.factory;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntity;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

/**
 * 创建活动
 * <AUTHOR>
 * @date 2021-04-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class CreateActivityInstanceCmd implements Command<Void> {

    /**
     * 用于创建活动
     */
    private ActivityInstanceEntity activityInstanceEntity;

    public CreateActivityInstanceCmd(ActivityInstanceEntity activityInstanceEntity) {
        this.activityInstanceEntity = activityInstanceEntity;
    }

    @Override
    public Void execute(CommandContext commandContext) {

        ActivityInstanceEntityManager activityInstanceEntityManager = CommandContextUtil.getActivityInstanceEntityManager(commandContext);
        activityInstanceEntityManager.insert(activityInstanceEntity);
        return null;
    }
}
