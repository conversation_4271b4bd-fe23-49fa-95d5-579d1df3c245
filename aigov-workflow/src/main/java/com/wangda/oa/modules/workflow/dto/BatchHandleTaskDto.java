package com.wangda.oa.modules.workflow.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: leec
 * @create: 2022-02-18
 * @description: 流程任务批量处理返回
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchHandleTaskDto {
    /**
     * 流程实例ID
     */
    private String procInstanceId;

    /**
     * 是否可批量处理
     */
    private Boolean validated;
}
