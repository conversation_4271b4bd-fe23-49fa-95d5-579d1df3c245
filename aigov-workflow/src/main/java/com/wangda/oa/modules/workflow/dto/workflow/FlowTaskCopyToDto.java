package com.wangda.oa.modules.workflow.dto.workflow;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>工作流任务<p>
 *
 * <AUTHOR>
 * @date 2021-04-03
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流任务抄送相关-返回参数")
public class FlowTaskCopyToDto implements Serializable {

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务Key")
    private String taskDefKey;

    @ApiModelProperty("任务抄送人")
    private List<FlowTaskUserDto> taskUserDtos;

    @ApiModelProperty("流程实例ID")
    private String procInsId;

    @ApiModelProperty("任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    @ApiModelProperty("任务完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date finishTime;

}
