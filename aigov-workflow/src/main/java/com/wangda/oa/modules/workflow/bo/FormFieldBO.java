package com.wangda.oa.modules.workflow.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新表单字段
 * <AUTHOR>
 * @date 2022/01/23 9:05
 */
@Data
public class FormFieldBO {

    @ApiModelProperty("表单模板ID")
    @NotNull
    private Long formTemplateId;

    @ApiModelProperty("表单流程实例ID")
    @NotBlank
    private String processInstanceId;

    @ApiModelProperty("表单字段")
    @NotBlank
    private String fieldName;

    @ApiModelProperty("表单字段数据")
    @NotNull
    private Object formFieldData;
}
