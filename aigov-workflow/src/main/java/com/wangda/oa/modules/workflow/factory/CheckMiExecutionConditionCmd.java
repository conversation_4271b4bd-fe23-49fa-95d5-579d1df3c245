package com.wangda.oa.modules.workflow.factory;

import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * flowable 检测多实例完成条件
 * <AUTHOR>
 * @date 2021-10-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class CheckMiExecutionConditionCmd implements Command<Boolean> {

    private String executionId;
    private MultiInstanceActivityBehavior multiInstanceActivityBehavior;

    public CheckMiExecutionConditionCmd(String executionId, MultiInstanceActivityBehavior multiInstanceActivityBehavior) {
        this.executionId = executionId;
        this.multiInstanceActivityBehavior = multiInstanceActivityBehavior;
    }

    @Override
    public Boolean execute(CommandContext commandContext) {
        ExecutionEntity executionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).findById(executionId);
        Integer nrOfCompletedInstances = multiInstanceActivityBehavior.getLoopVariable(executionEntity, ProcessConstants.NUMBER_OF_COMPLETED_INSTANCES);
        int nrOfInstances = multiInstanceActivityBehavior.getLoopVariable(executionEntity, ProcessConstants.NUMBER_OF_INSTANCES);
        if(Objects.isNull(nrOfCompletedInstances)) {
            nrOfCompletedInstances = 0;
        }

        DelegateExecution miRootExecution = this.getMultiInstanceRootExecution(executionEntity);
        miRootExecution.setTransientVariableLocal(ProcessConstants.NUMBER_OF_COMPLETED_INSTANCES, nrOfCompletedInstances+1);

        Boolean result = multiInstanceActivityBehavior.completionConditionSatisfied(executionEntity);

        // 计算完后还原
        miRootExecution.removeTransientVariableLocal(ProcessConstants.NUMBER_OF_COMPLETED_INSTANCES);

        if(nrOfCompletedInstances + 1 >= nrOfInstances || result) {
            return true;
        }
        return false;
    }

    protected DelegateExecution getMultiInstanceRootExecution(DelegateExecution executionEntity) {
        DelegateExecution multiInstanceRootExecution = null;
        DelegateExecution currentExecution = executionEntity;
        while (currentExecution != null && multiInstanceRootExecution == null && currentExecution.getParent() != null) {
            if (currentExecution.isMultiInstanceRoot()) {
                multiInstanceRootExecution = currentExecution;
            } else {
                currentExecution = currentExecution.getParent();
            }
        }
        return multiInstanceRootExecution;
    }
}
