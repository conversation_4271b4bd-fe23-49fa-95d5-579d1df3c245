package com.wangda.oa.modules.workflow.service.workflow;

import com.alibaba.fastjson.JSONObject;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.workflow.constant.BpmAuthorityConstant;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskVariablesDto;
import com.wangda.oa.utils.SecurityUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.security.core.GrantedAuthority;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/28
 * @description 各业务须实现该接口
 */
public interface WorkflowCustomExtensionService {

    /**
     * 根据定义key获取图标
     * @param procDefKey
     * @return
     */
    String getWorkTypeIconByProcDefKey(String procDefKey);

    /**
     * 根据定义key获取名称
     * @param value
     * @return
     */
    String getWorkTypeNameByValue(String value);

    /**
     * 根据业务id（流程实例id）删除待办
     * @param bizId
     */
    Boolean deleteBackLog(String bizId);

    /**
     * 发送待办
     * @param backlogDto
     * @return
     */
    Boolean pushToBacklog(BacklogDto backlogDto);

    /**
     * 发送待办已读
     * @param bizId
     * @param username
     * @return
     */
    Boolean readBacklog(String bizId, String username);

    /**
     * 是否是抄送人员
     * @param bizId
     * @param username
     * @return Boolean
     */
    default Boolean checkCopyTo(String bizId, String username) {
        return false;
    }

    /**
     * 根据类别返回流程定义列表
     * @param type
     * @return
     */
    List<String> getProcessDefinitionKeyList(String type);


    /**
     * 获取用户流程权限类别
     * @return List
     */
    List<String> getBpmAdminKeys();

    /**
     * 是否是流程管理员
     * @return
     */
    default Boolean isBpmAdmin() {
        List<String> elPermissions = SecurityUtils.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        if(Arrays.stream(new String[]{ BpmAuthorityConstant.AUTHORITY_BPM_ADMIN, BpmAuthorityConstant.AUTHORITY_ADMIN }).anyMatch(elPermissions::contains)) {
            return true;
        }
        return false;
    }

    /**
     * 更新待阅任务
     * @param bpmInstanceId
     * @param readId
     * @param flag
     * @return
     */
    String updateBpmReadTask(Long bpmInstanceId, Long readId, int flag);

    /**
     * 发送抄送消息
     * @param procInstanceId
     * @param userList
     * @return
     */
    void sendCopyToMessage(String procInstanceId, List<String> userList);

    /**
     * 发送抄送消息
     * @param procInstanceId
     * @param userList
     * @param sendToBacklog 是否发送到统一待办
     * @return
     */
    default void sendCopyToMessage(String procInstanceId, List<String> userList, boolean sendToBacklog) {}

    /**
     * 校验业务表单
     * @param procInstanceId
     * @param procDefKey
     * @param formDataObj
     * @return
     */
    default ResultJson validateFormData(String procInstanceId, String procDefKey, JSONObject formDataObj) {
        return ResultJson.generateResult();
    }

    /**
     * 校验业务流程表单
     * @param procInstanceId
     * @param procDefKey
     * @param currentTask 当前任务
     * @param nextTaskNodes 后续节点
     * @param formDataObj
     * @return
     */
    default ResultJson validateFormData(String procInstanceId, String procDefKey, Task currentTask, String nextTaskNodes, JSONObject formDataObj) {
        return this.validateFormData(procInstanceId, procDefKey, formDataObj);
    }

    /**
     * 批量办理自定义业务表单逻辑判断
     * @param procInstanceId
     * @param procDefKey
     * @param currentTask
     * @return
     */
    default boolean checkBatchHandleTaskFormData(String procInstanceId, String procDefKey, Task currentTask) {
        return true;
    }

    /**
     * 设置默认选中节点
     * @param flowTaskVariablesDto 任务变量
     * @return void
     */
   default void handleFlowTaskData(FlowTaskVariablesDto flowTaskVariablesDto) {
   }

    /**
     * 获取后续节点预签人员
     * @param processInstance 流程实例
     * @param taskDefKey 任务key
     * @param bpmnModel 流程定义模型
     * @return String
    */
    default String getNextTaskPreSignAssignee(ProcessInstance processInstance, String taskDefKey, BpmnModel bpmnModel) {
        return null;
    }

    /**
     * 撤销流程自定义业务数据处理
     * @param procInstId
     * @return boolean
     */
    default boolean handleStopProcess(String procInstId) {
        return true;
    }

    /**
     * 自定义业务扩展人员获取
     * @param procDefKey 流程定义key
     * @param taskDefKey 任务key
     * @param procInstId 流程实例Id
     * @return List<SimpleUserDto>
     */
    default List<SimpleUserDto> handleCustomBizUser(String procDefKey, String taskDefKey, String procInstId) {
        return null;
    }

    /**
     * 修改统一待办业务ID
     * @param procInstId
     * @param newProcInstId
     * @return boolean
     */
    default boolean updateBacklogBizId(String procInstId, String newProcInstId) {
        return true;
    }
}
