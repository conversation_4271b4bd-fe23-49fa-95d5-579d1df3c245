package com.wangda.oa.modules.workflow.domain.workflow.mapstruct;

import com.wangda.oa.base.BaseMapper;
import com.wangda.oa.modules.workflow.domain.workflow.ProcessCommentArchive;
import com.wangda.oa.modules.workflow.dto.workflow.ProcessCommentArchiveDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
* <AUTHOR>
* @date 2021-08-06
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProcessCommentArchiveMapper extends BaseMapper<ProcessCommentArchiveDto, ProcessCommentArchive> {

}
