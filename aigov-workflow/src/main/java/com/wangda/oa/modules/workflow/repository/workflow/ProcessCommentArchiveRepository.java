package com.wangda.oa.modules.workflow.repository.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.ProcessCommentArchive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4 上午9:29
 */
@Repository
public interface ProcessCommentArchiveRepository extends JpaRepository<ProcessCommentArchive, Long>, JpaSpecificationExecutor<ProcessCommentArchive> {

    List<ProcessCommentArchive> findByProcInstanceIdOrderByTime(String procInstanceId);

    /**
     * 根据实例id删除
     * @param procInstanceId
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByProcInstanceId(String procInstanceId);

    /**
     * 根据实例id删除
     * @param procInstanceId
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByProcInstanceIdIn(List<String> procInstanceId);
}
