package com.wangda.oa.modules.workflow.listener;

import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.ElAdminConstant;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.task.api.Task;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description 分发处理（指派事件）
 */
@Slf4j
@Component
public class TaskParallelDistListener implements TaskListener {

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private TaskService taskService;

    private static TaskParallelDistListener taskParallelDistListener;

    // 解决监听器中Bean获取不到问题
    @PostConstruct
    public void init() {
        taskParallelDistListener = this;
        taskParallelDistListener.flowTaskService = this.flowTaskService;
        taskParallelDistListener.repositoryService = this.repositoryService;
        taskParallelDistListener.taskService = this.taskService;
    }

    @Override
    public void notify(DelegateTask delegateTask) {

        BpmnModel bpmnModel = taskParallelDistListener.repositoryService.getBpmnModel(delegateTask.getProcessDefinitionId());
        Task myTask = taskParallelDistListener.taskService.createTaskQuery().taskId(delegateTask.getId()).singleResult();
        String taskAssignees = (String)delegateTask.getVariable(ProcessConstants.BPM_TASK_ASSIGNEE);

        // 判断提交的节点中是否有多人的单节点
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(myTask.getTaskDefinitionKey());
        if(FlowableUtils.countAppear(taskAssignees, myTask.getTaskDefinitionKey()) > 1 && !(flowNode.getBehavior() instanceof MultiInstanceActivityBehavior)) {
            List<String> taskAssigneeList = Arrays.asList(taskAssignees.split(ElAdminConstant.SEPARATOR_COMMA)).stream().filter(t -> t.contains(myTask.getTaskDefinitionKey())).collect(Collectors.toList());
            for (int i=1; i<taskAssigneeList.size(); i++) {
                String[] assignees = taskAssigneeList.get(i).split("\\$");
                // 过滤节点已有该处理人，不创建
                List<Task> taskList = taskParallelDistListener.taskService.createTaskQuery().processInstanceId(delegateTask.getProcessInstanceId()).taskDefinitionKey(delegateTask.getTaskDefinitionKey()).taskAssignee(assignees[0]).list();
                if (!CollectionUtils.isEmpty(taskList)) {
                    continue;
                }
                taskParallelDistListener.flowTaskService.distributeNewTask(myTask, flowNode, assignees[0]);
            }
        }

        log.info("taskParallelDistListener", delegateTask);
    }
}
