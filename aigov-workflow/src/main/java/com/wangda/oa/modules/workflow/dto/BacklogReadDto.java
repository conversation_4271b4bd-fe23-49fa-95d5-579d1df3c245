package com.wangda.oa.modules.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/12/11 上午11:00
 */
@Data
@ApiModel(description="阅读")
public class BacklogReadDto {
    @ApiModelProperty(value = "外部业务id(同个任务相同)")
    @NotBlank(message = "外部业务id不能为空")
    private String bizId;
    @ApiModelProperty(value = "用户名")
    private String userId;
}
