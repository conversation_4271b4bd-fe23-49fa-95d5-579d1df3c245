package com.wangda.oa.modules.workflow.domain.workflow;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * 流程-用户任务操作按钮定义表
 *
 * <AUTHOR>
 * @date 2021/4/15下午7:47
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_workflow_user_task_buttons",uniqueConstraints=@UniqueConstraint(columnNames={"flow_define_id", "link_Key", "button_key"}))
public class WorkflowUserTaskButtons extends BaseDomain {

    @Column(name = "task_definition_id")
    @ApiModelProperty(value = "用户任务定义ID")
    private Long taskDefinitionId;

    @Column(name = "flow_define_id")
    @ApiModelProperty(value = "流程部署id")
    private String flowDefineId;

    @Column(name = "link_key")
    @ApiModelProperty(value = "环节Key")
    private String linkKey;

    @Column(name = "button_key")
    @ApiModelProperty(value = "操作按钮Key")
    private String buttonKey;

    @Column(name = "button_icon")
    @ApiModelProperty(value = "按钮图标(索引)")
    private String buttonIcon;

    @Column(name = "button_name")
    @ApiModelProperty(value = "操作按钮名称")
    private String buttonName;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Column(name = "commonly")
    @ApiModelProperty(value = "是否常用按钮(false:否,true:是)")
    private Boolean commonly;

    @Column(name = "button_show_rule")
    @ApiModelProperty(value = "按钮显示规则")
    private String buttonShowRule;
}
