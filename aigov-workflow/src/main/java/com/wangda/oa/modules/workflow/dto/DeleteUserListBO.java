package com.wangda.oa.modules.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/10 上午10:22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteUserListBO {
    @ApiModelProperty(value = "用户id")
    private String userId;
}
