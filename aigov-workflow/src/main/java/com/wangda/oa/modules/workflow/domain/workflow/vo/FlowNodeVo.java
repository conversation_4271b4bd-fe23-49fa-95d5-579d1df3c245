package com.wangda.oa.modules.workflow.domain.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR> leec
 * @title: : FlowNodeVo
 * @description: 流程节点的Vo
 * @date : 2021/08/16
 */
@Data
@Builder
public class FlowNodeVo implements Serializable {

    @NotBlank
    @ApiModelProperty("任务Id")
    private String taskId;

    @ApiModelProperty("处理用户 username,node")
    private TaskAssigneeVo taskAssignee;

    @Tolerate
    FlowNodeVo(){}
}
