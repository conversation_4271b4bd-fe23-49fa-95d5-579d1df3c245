package com.wangda.oa.modules.workflow.factory;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

/**
 * flowable 创建执行
 * <AUTHOR>
 * @date 2021-10-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class CreateExecutionCmd implements Command<Void> {

    /**
     * 用于创建执行
     */
    private ExecutionEntity executionEntity;

    public CreateExecutionCmd(ExecutionEntity executionEntity) {
        this.executionEntity = executionEntity;
    }

    @Override
    public Void execute(CommandContext commandContext) {

        ExecutionEntityManager executionEntityManager = CommandContextUtil.getExecutionEntityManager(commandContext);
        executionEntityManager.insert(executionEntity);
        return null;
    }
}
