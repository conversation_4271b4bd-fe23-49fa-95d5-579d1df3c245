package com.wangda.oa.modules.workflow.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/28
 * @description 实验室接口
 */
public interface ILabTaskService {

    /**
     * 创建流程实例
     * @param bpmInstanceId
     * @param title
     */
    String createBpmInstance(Long bpmInstanceId, String title);

    /**
     * 更新流程实例
     * @param bpmInstanceId
     * @param flag
     * @param result
     * @return
     */
    String updateBpmInstance(Long bpmInstanceId, int flag, int result);

    /**
     * 创建待办任务
     * @param bpmInstanceId
     * @param taskId
     * @return
     */
    String createBpmTodoTask(Long bpmInstanceId, Long taskId, String toUserName);

    /**
     * 更新待办任务
     * @param bpmInstanceId
     * @param taskId
     * @param flag
     * @param result
     * @param opinion
     * @return
     */
    String updateBpmTodoTask(Long bpmInstanceId, Long taskId, int flag, int result, String opinion);

    /**
     * 创建待阅任务
     * @param bpmInstanceId
     * @return
     */
    String createBpmReadTask(Long bpmInstanceId);

    /**
     * 更新待阅任务
     * @param bpmInstanceId
     * @param readId
     * @param flag
     * @return
     */
    String updateBpmReadTask(Long bpmInstanceId, Long readId, int flag);

    /**
     * 创建催办
     * @param bpmInstanceId
     * @param taskIds
     * @param content
     * @return
     */
    String createBpmUrgeTask(Long bpmInstanceId, Long[] taskIds, String content);

    /**
     *
     * @param bpmInstanceId
     * @return
     */
    List<Map<String, String>> getUrgeList(Long bpmInstanceId);

    /**
     * 推送实验室发文
     * @param bpmInstanceId
     * @return
     */
    String pushLabDocument(Long bpmInstanceId, Long storageId);
}
