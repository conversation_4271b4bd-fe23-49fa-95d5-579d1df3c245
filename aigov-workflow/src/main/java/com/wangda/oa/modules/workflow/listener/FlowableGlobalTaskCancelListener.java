package com.wangda.oa.modules.workflow.listener;

import com.alibaba.fastjson.JSON;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.zwdd.task.TaskFinishBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationLinkBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkRevokeBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.config.SendTaskMessageConfig;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.enums.workflow.TaskCommentTypeEnum;
import com.wangda.oa.modules.workflow.enums.workflow.TaskRemindWayEnum;
import com.wangda.oa.modules.workflow.factory.AfterTransactionOpt;
import com.wangda.oa.modules.workflow.factory.FlowServiceFactory;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.service.IGbzjTaskService;
import com.wangda.oa.modules.workflow.service.ILabTaskService;
import com.wangda.oa.modules.workflow.service.ISysAppService;
import com.wangda.oa.modules.workflow.service.IZwddService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.event.impl.FlowableActivityCancelledEventImpl;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description 普通任务取消监听
 */
@Slf4j
@Component
public class FlowableGlobalTaskCancelListener extends FlowServiceFactory implements FlowableEventListener {

    @Autowired
    private RestUrlComponent restUrlComponent;

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private SendTaskMessageConfig sendTaskMessageConfig;

    @Autowired
    private AfterTransactionOpt afterTransactionOpt;

    @Autowired
    private UserService userService;

    // zwdd
    @Resource
    private ZwddProperties zwddProperties;
    @Autowired(required = false)
    private IZwddService zwddService;
    @Autowired(required = false)
    private ISysAppService sysAppService;

    @Autowired(required = false)
    private ILabTaskService labTaskService;
    @Autowired(required = false)
    private IGbzjTaskService gbzjTaskService;

    @Override
    public void onEvent(FlowableEvent flowableEvent) {
        FlowableActivityCancelledEventImpl event = (FlowableActivityCancelledEventImpl) flowableEvent;
        Task taskEntity = taskService.createTaskQuery().executionId(event.getExecutionId()).singleResult();
        if(Objects.isNull(taskEntity) || StringUtils.isEmpty(taskEntity.getAssignee())) {
            log.info("任务取消监听，任务或处理人没有" + event.getActivityId() + ";" + event.getExecutionId());
            return;
        }

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(taskEntity.getProcessInstanceId())
                .singleResult();

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(processInstance.getProcessDefinitionId()).singleResult();

        String subject = "您的" + processDefinition.getName() + taskEntity.getName() + "已处理";
        //完成oa待办
        String pcUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/webPage?processInstanceId=" + processInstance.getProcessInstanceId();

        String bpmUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/page?processInstanceId=" + processInstance.getProcessInstanceId();

        // 设置当前节点接收人
        Map<String, Object> variables = processInstance.getProcessVariables();

        try {
            // 发送统一待办
            if(sendTaskMessageConfig.getSendBacklog()) {
                List alreadyUserListBO = new ArrayList();
                UserDto userDto = userService.findById(Long.parseLong(processInstance.getStartUserId()));
                AlreadyUserListBO alreadyUserDto = AlreadyUserListBO.builder().transactionTime(Instant.now().getEpochSecond()).userId(taskEntity.getAssignee()).build();
                alreadyUserListBO.add(alreadyUserDto);

                // 表单数据
                String formData = JSON.toJSONString(variables.get(ProcessConstants.BPM_FORM_DATA));
                flowTaskService.sendBacklogList(null, formData, null, processInstance, userDto.getUsername(), bpmUrl, pcUrl, alreadyUserListBO);
            }

            // 发送之江实验室
            if(sendTaskMessageConfig.getSendZhejianglab()) {
                // 实验室取消
                String cancelResult = labTaskService.updateBpmTodoTask(Long.parseLong(taskEntity.getProcessInstanceId()), Long.parseLong(taskEntity.getId()), -1, 1, "");
                log.info("实验室取消任务结果：" + cancelResult);
            }
        }catch(Exception e) {
            log.error("取消完成oa待办失败", e.getMessage());
        }

        // 发送政务钉钉
        if(sendTaskMessageConfig.getSendZwdd()) {
            SysUserPlatform sysUserPlatform = sysAppService.getByUserNameAndType(taskEntity.getAssignee(), zwddProperties.getType());
            if(Objects.isNull(sysUserPlatform)) {
                ResultJson resultJson = sysAppService.getUid(taskEntity.getAssignee());
                if(resultJson.isSuccess()) {
                    sysUserPlatform = new SysUserPlatform();
                    sysUserPlatform.setPlatformUserId(resultJson.getData().toString());
                }else {
                    sysUserPlatform = sysAppService.getByUserNameAndType("admin", zwddProperties.getType());
                }
            }

            TaskFinishBO taskCreateBO = TaskFinishBO.builder()
                    .userId(sysUserPlatform.getPlatformUserId())
                    .bizTaskId(taskEntity.getId())
                    .build();

            // 取消待办
            ResultJson resultJson = zwddService.taskFinish(taskCreateBO);
            if(!resultJson.isSuccess()) {
                log.error("任务取消监听，发送取消待办失败:" + resultJson);
            }

            // 获取节点消息提醒方式
            BpmnModel bpmnModel = repositoryService.getBpmnModel(taskEntity.getProcessDefinitionId());
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(taskEntity.getTaskDefinitionKey());
            String taskRemindWays = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMINDWAY);
            // 发送浙政钉通知
            if(StringUtils.isNotBlank(taskRemindWays) && taskRemindWays.contains(TaskRemindWayEnum.ZWDD.getValue())) {
                // 取消已完成处理人工作通知
                WorkRevokeBO workRevokeBO = WorkRevokeBO.builder()
                        .bizMsgId(taskEntity.getId())
                        .build();
                afterTransactionOpt.execute(new Runnable() {
                    @Override
                    public void run() {
                        zwddService.revokeMessageInfo(workRevokeBO);
                    }
                });

                // 发送通知给发起人
                // 获取意见评论内容
                List<Comment> commentList = taskService.getTaskComments(taskEntity.getId(), TaskCommentTypeEnum.JJ.name());
                if(!CollectionUtils.isEmpty(commentList)) {

                    // 流程发起人
                    SysUserPlatform creatorUserPlatform = sysAppService.getByUserId(processInstance.getStartUserId(), zwddProperties.getType());

                    // 处理钉钉id
                    if(Objects.isNull(creatorUserPlatform)) {
                        log.error("浙政钉给发起人发送处理消息失败FlowableGlobalTaskCancelListener：找不到平台用户");
                    }else {
                        WorkNotificationLinkBO workNotificationLinkBO = WorkNotificationLinkBO.builder()
                                .title(processDefinition.getName())
                                .messageUrl(bpmUrl)
                                .picUrl("@lALOACZwe2Rk")
                                .text(subject)
                                .build();
                        WorkNotificationBO workNotificationBO = WorkNotificationBO.builder()
                                .receiverIds(creatorUserPlatform.getPlatformUserId())
                                .bizMsgId(taskEntity.getExecutionId())
                                .type(1)
                                .linkBO(workNotificationLinkBO)
                                .build();

                        // 发送通知
                        afterTransactionOpt.execute(new Runnable() {
                            @Override
                            public void run() {
                                zwddService.workNotification(workNotificationBO);
                            }
                        });
                    }
                }
            }
        }

        // 发送干部之家
        if(sendTaskMessageConfig.getSendGbzjoa()) {
            try {
                gbzjTaskService.completeOaTask(taskEntity.getProcessInstanceId(), taskEntity.getId());
            }catch(Exception e) {
                log.error("发送干部之家任务完成取消错误：procInstId:" + taskEntity.getProcessInstanceId() + " taskId:" + taskEntity.getId());
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean isFailOnException() {
        return true;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }
}
