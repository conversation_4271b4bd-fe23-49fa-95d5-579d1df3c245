package com.wangda.oa.modules.workflow.controller.workflow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.platform.utils.PageUtil;
import com.wangda.oa.modules.workflow.domain.workflow.vo.*;
import com.wangda.oa.modules.workflow.dto.BatchHandleTaskDto;
import com.wangda.oa.modules.workflow.dto.HistoricProcessBO;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>工作流任务管理<p>
 * <AUTHOR>
 * @date 2021-04-03
 */
@Slf4j
@Api(tags = "工作流流程任务管理")
@RestController
@RequestMapping("/api/flowable/task")
@CrossOrigin
public class FlowTaskController {

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RepositoryService repositoryService;

    @ApiOperation(value = "我的流程(包括我发起和我处理的)", response = FlowTaskDto.class)
    @GetMapping(value = "/myProcessList")
    public ResponseEntity<Object> myProcessList(@ApiParam(value = "流程查询参数") ProcessQueryVo query) {
        Page pageList = flowTaskService.myProcessList(query);
        return ResponseEntity.ok(PageUtil.toPage(pageList.getRecords(), pageList.getTotal()));
    }

    @ApiOperation(value = "收回流程", response = FlowTaskDto.class)
    @PostMapping(value = "/revokeProcess")
    public ResultJson revokeProcess(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.revokeProcess(flowTaskVo);
    }

    @ApiOperation(value = "我的待办列表", response = FlowTaskDto.class)
    @PostMapping(value = "/todoList")
    public ResultJson todoList(@ApiParam(value = "查询参数") @RequestBody TaskQueryVo query) throws Exception {
        return flowTaskService.todoList(query);
    }

    @ApiOperation(value = "查询全部待办列表", response = FlowTaskDto.class)
    @PostMapping(value = "/queryTodoList")
    public ResultJson queryTodoList(@ApiParam(value = "查询参数") @RequestBody TaskQueryVo query) throws Exception {
        Page<FlowTaskDto> page = flowTaskService.getApplyingTasks(query);
        return ResultJson.generateResult(page);
    }

    @ApiOperation(value = "获取已办任务", response = FlowTaskDto.class)
    @PostMapping(value = "/finishedList")
    public ResultJson finishedList(@ApiParam(value = "查询参数") @RequestBody TaskQueryVo query) throws Exception {
        return flowTaskService.finishedList(query);
    }

    @ApiOperation(value = "获取流程变量", response = FlowTaskDto.class)
    @GetMapping(value = "/processVariables/{taskId}")
    public ResultJson processVariables(@ApiParam(value = "流程任务Id") @PathVariable(value = "taskId") String taskId) {
        return flowTaskService.processVariables(taskId);
    }

    @ApiOperation(value = "提交审批任务")
    @PostMapping(value = "/complete")
    public ResultJson complete(@RequestBody FlowTaskVo flowTaskVo) throws Exception {
        flowTaskService.completeTask(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "拒绝任务")
    @PostMapping(value = "/reject")
    public ResultJson taskReject(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.taskReject(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "退回任务")
    @Deprecated
    @PostMapping(value = "/return")
    public ResultJson taskReturn(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.taskReturn(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "获取所有可回退的节点")
    @PostMapping(value = "/returnList")
    public ResultJson findReturnTaskList(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.findReturnTaskList(flowTaskVo);
    }

    @ApiOperation(value = "获取特送所有可选择的节点")
    @GetMapping(value = "/expressTaskList/{taskId}")
    public ResultJson findExpressTaskList(@ApiParam(value = "流程任务Id") @PathVariable(value = "taskId") String taskId) {
        return flowTaskService.findExpressTaskList(taskId);
    }

    @ApiOperation(value = "删除任务")
    @DeleteMapping(value = "/delete")
    public ResultJson delete(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.deleteTask(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "撤销流程")
    @PostMapping(value = "/stopProcess")
    public ResultJson stopProcess(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.stopProcess(flowTaskVo);
    }

    @ApiOperation(value = "认领/签收任务")
    @PostMapping(value = "/claim")
    public ResultJson claim(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.claim(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "取消认领/签收任务")
    @PostMapping(value = "/unClaim")
    public ResultJson unClaim(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.unClaim(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "委派任务（特送转办任务）")
    @PostMapping(value = "/delegate")
    public ResultJson delegate(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.delegateTask(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "回退首环节")
    @PostMapping(value = "/returnFirst")
    public ResultJson returnFirst(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.taskReturnFirst(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "回退上一环节")
    @PostMapping(value = "/returnLast")
    public ResultJson returnLast(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.taskReturnLast(flowTaskVo);
        return ResultJson.generateResult();
    }

    @ApiOperation(value = "加签")
    @PostMapping(value = "/addSignTask")
    public ResultJson addSignTask(@RequestBody AddSignTaskVo addSignTaskVo) {
        return flowTaskService.addSignTask(addSignTaskVo);
    }

    @ApiOperation(value = "减签")
    @PostMapping(value = "/reduceSignTask")
    public ResultJson reduceSignTask(@RequestBody ReduceSignTaskVo reduceSignTaskVo) {
        return flowTaskService.reduceSignTask(reduceSignTaskVo);
    }

    @ApiOperation(value = "加减签")
    @PostMapping(value = "/signTask")
    public ResultJson signTask(@RequestBody SignTaskVo signTaskVo) {
        return flowTaskService.signTask(signTaskVo);
    }

    @ApiOperation(value = "锁定任务")
    @PostMapping(value = "/taskLock")
    public ResultJson taskLock(@RequestBody FlowTaskVo flowTaskVo) {
        boolean result = flowTaskService.taskLock(flowTaskVo);
        if(result) {
            return ResultJson.generateResult("锁定任务成功");
        }else {
            return ResultJson.generateResult("锁定任务失败");
        }
    }

    @ApiOperation(value = "解锁任务")
    @PostMapping(value = "/taskUnLock")
    public ResultJson taskUnLock(@RequestBody FlowTaskVo flowTaskVo) {
        boolean result = flowTaskService.taskUnLock(flowTaskVo);
        if(result) {
            return ResultJson.generateResult("解锁任务成功");
        }else {
            return ResultJson.generateResult("解锁任务失败");
        }
    }

    @ApiOperation(value = "查询行政服务列表", response = FlowTaskDto.class)
    @GetMapping(value = "/queryAdministrative")
    public ResponseEntity<Object> queryAdministrative(HistoricProcessBO bo, Pageable pageable) {
        Map map = flowTaskService.queryAdministrative(bo, pageable);
        return ResponseEntity.ok(map);
    }

    @ApiOperation(value = "手动创建分发任务", response = ResultJson.class)
    @PostMapping(value = "/distributeNewTask")
    @PreAuthorize("@el.check('admin')")
    public ResultJson distributeNewTask(String procInstanceId, String taskDefKey, String assignee) {

        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstanceId).singleResult();
        if(Objects.isNull(historicProcessInstance)) {
            return ResultJson.generateResult("手动创建任务失败，流程未启动或已结束");
        }

        TaskEntityImpl newTask = (TaskEntityImpl) taskService.newTask();
        newTask.setProcessInstanceId(procInstanceId);
        newTask.setProcessDefinitionId(historicProcessInstance.getProcessDefinitionId());

        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

        // 判断提交的节点中是否有多人的单节点
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(taskDefKey);
        if(!(flowNode.getBehavior() instanceof MultiInstanceActivityBehavior)) {
            flowTaskService.distributeNewTask(newTask, flowNode, assignee);
        }
        return ResultJson.generateResult("手动创建任务成功");
    }

    @ApiOperation(value = "获取批量办理待办任务", response = ResultJson.class)
    @PostMapping(value = "/batchHandleTask")
    public ResultJson batchHandleTask(@RequestBody List<String> procInstanceIds) {
        if(CollectionUtils.isEmpty(procInstanceIds)) {
            return ResultJson.generateResult("参数不能为空", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }

        List<BatchHandleTaskDto> batchHandleTaskDtoList = new ArrayList<>();
        for(String procInstanceId : procInstanceIds) {
            boolean result = flowTaskService.checkBatchHandleTask(procInstanceId);
            BatchHandleTaskDto batchHandleTaskDto = BatchHandleTaskDto.builder()
                    .procInstanceId(procInstanceId)
                    .validated(result)
                    .build();
            batchHandleTaskDtoList.add(batchHandleTaskDto);
        }

        return ResultJson.generateResult(batchHandleTaskDtoList);
    }

    @ApiOperation(value = "批量提交审批任务")
    @PostMapping(value = "/batchComplete")
    public ResultJson batchComplete(@RequestBody List<String> procInstanceIds) throws Exception {
        if(CollectionUtils.isEmpty(procInstanceIds)) {
            return ResultJson.generateResult("参数不能为空", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }
        flowTaskService.batchCompleteTask(procInstanceIds, null);
        return ResultJson.generateResult();
    }
}
