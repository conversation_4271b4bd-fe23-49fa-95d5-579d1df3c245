package com.wangda.oa.modules.workflow.enums.workflow;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> leec
 * @description: 附件类型
 * @date : 2021/08/03
 */
@Getter
@AllArgsConstructor
public enum AttachmentTypeEnum {

    SXQM("手写签名"),
    YJFJ("意见附件");

    //名称
    private String name;

    /**
     * 通过type获取Msg
     * @param type
     * @return
     * @Description:
     */
    public static String getEnumInfoByType(String type) {
        for (AttachmentTypeEnum e : AttachmentTypeEnum.values()) {
            if (e.name().equals(type)) {
                return e.name;
            }
        }
        return null;
    }
}
