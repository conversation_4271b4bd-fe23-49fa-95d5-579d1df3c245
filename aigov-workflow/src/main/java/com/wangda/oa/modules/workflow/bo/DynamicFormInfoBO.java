package com.wangda.oa.modules.workflow.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/1/19 下午5:38
 */
@Data
public class DynamicFormInfoBO{

    @ApiModelProperty(value = "活动表单主键")
    @NotNull(message = "活动表单主键不能为空")
    private Long id;

    @ApiModelProperty(value = "模板id")
    @NotNull(message = "模板id不能为空")
    private Long templateId;
}
