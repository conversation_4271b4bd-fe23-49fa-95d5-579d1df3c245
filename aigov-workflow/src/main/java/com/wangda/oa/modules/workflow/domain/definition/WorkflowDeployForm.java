package com.wangda.oa.modules.workflow.domain.definition;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * 流程实例关联表单对象
 * <AUTHOR>
 * @date 2021-04-14
 */
@Entity
@Data
@Table(name = "t_workflow_deploy_form")
public class WorkflowDeployForm extends BaseDomain {

    @ApiModelProperty(value = "表单主键")
    @NotNull
    private Long formId;

    @ApiModelProperty(value = "流程部署主键")
    @NotNull
    @Column(unique=true)
    private String deployId;

    @ApiModelProperty(value = "默认流程样式")
    @NotNull
    @Column(name = "form_skin")
    private String formSkin;

    @ApiModelProperty(value = "移动端流程样式")
    @Column(name = "mobile_form_skin")
    private String mobileFormSkin;

    @ApiModelProperty(value = "移动端表单")
    private Long mobileFormId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("formId", getFormId())
            .append("deployId", getDeployId())
            .toString();
    }
}
