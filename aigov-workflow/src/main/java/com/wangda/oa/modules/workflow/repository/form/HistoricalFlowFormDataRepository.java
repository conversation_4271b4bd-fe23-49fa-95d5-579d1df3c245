package com.wangda.oa.modules.workflow.repository.form;

import com.wangda.oa.modules.workflow.domain.form.HistoricalFlowFormData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR>
 * @date 2022/04/01 15:10
 */
public interface HistoricalFlowFormDataRepository extends JpaRepository<HistoricalFlowFormData, Long>, JpaSpecificationExecutor<HistoricalFlowFormData> {

    Page findAllByProcInstIdOrderByCreateDateDesc(String procInstId, Pageable pageable);

    void deleteAllByProcInstId(String procInstId);
}
