package com.wangda.oa.modules.workflow.domain.definition;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 流程分类表
 *
 * <AUTHOR>
 * @date 2021/4/13下午7:02
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_workflow_classify")
public class WorkflowClassify extends BaseDomain {

    @Column(name = "name")
    @ApiModelProperty(value = "名称")
    private String name;

    @Column(name = "pid")
    @ApiModelProperty(value = "父类id")
    private Long pid;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;
}
