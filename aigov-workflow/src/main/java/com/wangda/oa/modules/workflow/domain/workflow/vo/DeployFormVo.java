package com.wangda.oa.modules.workflow.domain.workflow.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 流程表单对象
 * <AUTHOR>
 * @date 2021-03-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeployFormVo {
    private static final long serialVersionUID = 1L;

     /** 主键id */
    private Long id;

    /** 表单主键 */
    private Long formId;

    /** 表单名称 */
    private String formName;

    /** 表单内容 */
    private String formContent;

    /** 表单样式 */
    private String formSkin;

    /** 移动端表单样式 */
    private String mobileFormSkin;

    /**
     * 移动端表单id
     */
    private Long mobileFormId;

    private String mobileFormContent;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("formId", getFormId())
            .append("formName", getFormName())
            .append("formContent", getFormContent())
            .toString();
    }
}
