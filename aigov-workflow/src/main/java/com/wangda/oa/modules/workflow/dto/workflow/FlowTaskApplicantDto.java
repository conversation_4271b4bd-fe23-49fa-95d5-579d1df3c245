package com.wangda.oa.modules.workflow.dto.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-04-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流任务相关-申请人数据结构")
public class FlowTaskApplicantDto implements Serializable {

    @ApiModelProperty("申请人用户编号")
    private Long startUserId;

    @ApiModelProperty("申请人用户名")
    private String startUsername;

    @ApiModelProperty("申请人姓名")
    private String startNickName;

    @ApiModelProperty("申请人部门名称")
    private String startDeptName;

    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
    @ApiModelProperty("申请时间")
    private Date startTime;

}
