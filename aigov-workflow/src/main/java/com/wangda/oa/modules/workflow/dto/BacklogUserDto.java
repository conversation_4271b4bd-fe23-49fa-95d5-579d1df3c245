package com.wangda.oa.modules.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 待办用户
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BacklogUserDto implements Serializable {

    @ApiModelProperty(value = "办理时限，时间戳秒级")
    private Long abortTime;

    @ApiModelProperty(value = "是否为抄送人(0:不是,1:是)")
    private Integer cc;

    @ApiModelProperty(value = "任务创建日期")
    private Long createDate;

    @ApiModelProperty(value = "用户id")
    private String userId;

}
