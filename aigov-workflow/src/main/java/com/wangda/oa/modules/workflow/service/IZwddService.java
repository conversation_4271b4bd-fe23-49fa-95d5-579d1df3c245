package com.wangda.oa.modules.workflow.service;

import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.zwdd.task.TaskCreateBO;
import com.wangda.oa.modules.extension.bo.zwdd.task.TaskFinishBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkRevokeBO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/28
 * @description 政务钉钉接口
 */
public interface IZwddService {

    /**
     * 发送通知
     * @param workNotificationBO
     */
    ResultJson workNotification(WorkNotificationBO workNotificationBO);

    /**
     * 工作通知-工作通知取消
     * @param bo
     * @return
     */
    ResultJson revokeMessageInfo(WorkRevokeBO bo);

    /**
     * 创建待办接口v2
     * @param bo
     * @return
     */
    ResultJson createTask(TaskCreateBO bo);

    /**
     * 待办模块-完成待办接口
     * @param bo
     * @return
     */
    ResultJson taskFinish(TaskFinishBO bo);
}
