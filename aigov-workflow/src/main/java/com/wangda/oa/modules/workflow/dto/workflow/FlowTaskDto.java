package com.wangda.oa.modules.workflow.dto.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>工作流任务<p>
 *
 * <AUTHOR>
 * @date 2021-04-03
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流任务相关-返回参数")
public class FlowTaskDto implements Serializable {

    @ApiModelProperty("任务编号")
    private String taskId;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务Key")
    private String taskDefKey;

    @ApiModelProperty("经办人信息")
    private FlowTaskUserDto assigneeInfo;

    @ApiModelProperty("申请人信息")
    private FlowTaskApplicantDto applicantInfo;

    @ApiModelProperty("接收用户")
    private String candidate;

    @ApiModelProperty("流程类型")
    private String category;

    @ApiModelProperty("流程变量信息")
    private Object procVars;

    @ApiModelProperty("局部变量信息")
    private Object taskLocalVars;

    @ApiModelProperty("流程部署编号")
    private String deployId;

    @ApiModelProperty("流程定义ID")
    private String procDefId;

    @ApiModelProperty("流程key")
    private String procDefKey;

    @ApiModelProperty("流程定义名称")
    private String procDefName;

    @ApiModelProperty("流程定义内置使用版本")
    private int procDefVersion;

    @ApiModelProperty("流程实例ID")
    private String procInsId;

    @ApiModelProperty("任务耗时")
    private String duration;

    @ApiModelProperty("任务意见")
    private FlowCommentDto comment;

    @ApiModelProperty("手写签名")
    private String signature;

    @ApiModelProperty("任务创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("任务完成时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    @ApiModelProperty("阅读时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    @ApiModelProperty("流程状态")
    private ProcStatusEnum procStatus;

    @ApiModelProperty(name = "办理状态")
    private String bpmStatus;

    @ApiModelProperty(name = "标题")
    private String formTitle;
}
