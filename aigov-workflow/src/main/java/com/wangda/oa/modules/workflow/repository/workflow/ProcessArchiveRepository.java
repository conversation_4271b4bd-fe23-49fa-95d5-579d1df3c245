package com.wangda.oa.modules.workflow.repository.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.ProcessArchive;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4 上午9:29
 */
@Repository
public interface ProcessArchiveRepository extends JpaRepository<ProcessArchive, Long>, JpaSpecificationExecutor<ProcessArchive> {

    ProcessArchive getByProcInstanceId(String procInstanceId);

    /**
     * 根据实例id删除
     * @param procInstanceId
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByProcInstanceId(String procInstanceId);

    /**
     * 根据实例id删除
     * @param procInstanceId
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByProcInstanceIdIn(List<String> procInstanceId);

    /**
     * 根据流程实例id修改历史流程变量值(表单数据)
     * @param procInstId
     * @param obj
     */
    @Modifying
    @Query(value = "update act_ge_bytearray agb, act_hi_varinst ahv set agb.BYTES_ = :obj where agb.ID_ = ahv.BYTEARRAY_ID_ and ahv.PROC_INST_ID_ = :procInstId and ahv.NAME_ = 'formData'", nativeQuery = true)
    void updateHiActGeBytearrayByProcInstId(String procInstId, Object obj);

    /**
     * 修改历史流程变量id信息
     * @param procInstId
     * @param newProcInstId
     */
    @Modifying
    @Query(value = "update act_hi_varinst ahv set ahv.PROC_INST_ID_ = :newProcInstId where ahv.PROC_INST_ID_ = :procInstId", nativeQuery = true)
    void updateActHiVarinstByProcInstId(String procInstId, String newProcInstId);

    /**
     * 修改历史流程活动id信息
     * @param procInstId
     * @param newProcInstId
     */
    @Modifying
    @Query(value = "update act_hi_actinst aha set aha.PROC_INST_ID_ = :newProcInstId where aha.PROC_INST_ID_ = :procInstId", nativeQuery = true)
    void updateActHiActinstByProcInstId(String procInstId, String newProcInstId);

    /**
     * 修改历史流程意见id信息
     * @param procInstId
     * @param newProcInstId
     */
    @Modifying
    @Query(value = "update act_hi_comment ahc set ahc.PROC_INST_ID_ = :newProcInstId where ahc.PROC_INST_ID_ = :procInstId", nativeQuery = true)
    void updateActHiCommentByProcInstId(String procInstId, String newProcInstId);

}
