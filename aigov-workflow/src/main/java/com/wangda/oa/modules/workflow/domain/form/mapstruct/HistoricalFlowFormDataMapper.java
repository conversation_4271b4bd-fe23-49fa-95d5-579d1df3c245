package com.wangda.oa.modules.workflow.domain.form.mapstruct;

import com.wangda.oa.base.BaseMapper;
import com.wangda.oa.modules.workflow.domain.form.HistoricalFlowFormData;
import com.wangda.oa.modules.workflow.dto.workflow.HistoricalFlowFormDataDto;
import org.mapstruct.*;

/**
* <AUTHOR>
* @date 2021-08-06
**/
@Mapper(componentModel = "spring", uses = UserNameMapperUtil.class, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface HistoricalFlowFormDataMapper extends BaseMapper<HistoricalFlowFormDataDto, HistoricalFlowFormData> {

    @Override
    @Mappings({
            @Mapping(source = "operator", target = "userDto"),
    })
    HistoricalFlowFormDataDto toDto(HistoricalFlowFormData entity);
}
