package com.wangda.oa.modules.workflow.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR> leec
 * @description: 我的工作状态
 * @date : 2021/07/11
 */
@Getter
@AllArgsConstructor
@ToString
@JSONType(serializeEnumAsJavaBean = true)
public enum MyWorkStatusEnum {
    BLZ("BLZ", "办理中"),
    YBL("YBL", "已办理");

    private String value;
    private String name;

    public static MyWorkStatusEnum getEnumByValue(String value) {
        for (MyWorkStatusEnum e : MyWorkStatusEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}
