package com.wangda.oa.modules.workflow.dto.workflow;

import com.wangda.oa.annotation.Query;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * 已阅
 */
@Data
@NoArgsConstructor
public class TaskHasReadQueryCriteria {
    @Query(type = Query.Type.INNER_LIKE)
    private String bt;

    @Query(type = Query.Type.EQUAL)
    private String assignee;

    @Query(type = Query.Type.NOT_NULL,propName = "firstReadTime")
    private Boolean isNotNullFirstReadTime;

    @Query(type = Query.Type.IN)
    private List<String> processDefKey;
}
