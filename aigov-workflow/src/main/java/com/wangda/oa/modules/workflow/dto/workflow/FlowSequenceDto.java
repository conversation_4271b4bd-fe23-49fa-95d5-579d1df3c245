package com.wangda.oa.modules.workflow.dto.workflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/5/12 22:59
 */
@Data
@Builder
@ApiModel("连接线节点")
public class FlowSequenceDto implements Serializable {

    @ApiModelProperty("接线节名称")
    private String sequenceName;

    @ApiModelProperty("接线节Key")
    private String sequenceDefKey;

    @ApiModelProperty("规则")
    private String preRule;

    @ApiModelProperty("源节点key")
    private String sourceDefKey;

    @ApiModelProperty("目标节点key")
    private String targetDefKey;

    @ApiModelProperty("连线类型")
    private String flowType;

    @ApiModelProperty("是否默认连线")
    private String isDefaultFlow;

    @ApiModelProperty("连线分组")
    private String flowGroup;

    @ApiModelProperty("是否隐藏")
    @Builder.Default
    private String flowHidden = "false";
}
