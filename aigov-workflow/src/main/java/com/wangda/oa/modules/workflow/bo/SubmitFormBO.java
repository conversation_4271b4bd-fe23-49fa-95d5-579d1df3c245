package com.wangda.oa.modules.workflow.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 提交表单
 *
 * <AUTHOR>
 * @date 2020/12/23 9:05
 */
@Data
public class SubmitFormBO {

    @ApiModelProperty("表单模板ID")
    @NotNull
    private Long formTemplateId;

    @ApiModelProperty("用户填写的表单数据JSON格式")
    @NotBlank
    private String formDataJson;

    @ApiModelProperty("表单流程实例ID")
    private String processInstanceId;

    @ApiModelProperty("表单任务ID")
    private String taskId;
}
