package com.wangda.oa.modules.workflow.domain.definition;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 流程终端表
 *
 * <AUTHOR>
 * @date 2021/4/13下午7:47
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "t_workflow_terminal")
public class WorkflowTerminal extends BaseDomain {

    @Column(name = "flow_define_id")
    @ApiModelProperty(value = "流程定义id")
    private String flowDefineId;

    @Column(name = "name")
    @ApiModelProperty(value = "终端名称")
    private String name;
}
