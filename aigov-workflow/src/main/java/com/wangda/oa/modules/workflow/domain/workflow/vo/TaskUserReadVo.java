package com.wangda.oa.modules.workflow.domain.workflow.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> leec
 * @description: 待阅已阅
 * @date : 2021/07/15
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskUserReadVo {

    @NotBlank
    @ApiModelProperty("流程实例Id")
    private String processInstanceId;

    @ApiModelProperty("任务Id")
    private String taskId;

    @NotBlank
    @ApiModelProperty("流程定义key")
    private String processDefKey;

    @NotBlank
    @ApiModelProperty("标题")
    private String bt;

    @ApiModelProperty("处理人")
    private String assignee;

    @ApiModelProperty("意见")
    private String remark;
}
