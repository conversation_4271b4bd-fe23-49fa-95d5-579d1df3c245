package com.wangda.oa.modules.workflow.controller.definition;

import com.wangda.boot.annotation.ResultBody;
import com.wangda.oa.modules.workflow.bo.flow.InfoBO;
import com.wangda.oa.modules.workflow.bo.flow.ListBO;
import com.wangda.oa.modules.workflow.bo.flow.WorkflowDefineInfoBO;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowClassify;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowDefineInfo;
import com.wangda.oa.modules.workflow.dto.workflow.WorkflowDefineInfoListDto;
import com.wangda.oa.modules.workflow.service.definition.WorkflowDefineInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/13 下午8:08
 */
@Api(tags = "流程定义接口")
@RestController
@CrossOrigin
@RequestMapping("/api/workflowDefine")
public class WorkflowDefineController {

    @Resource
    private WorkflowDefineInfoService workflowDefineInfoService;

    /**
     * 新增或修改流程定义
     * @return
     */
    @ApiOperation(value = "新增或修改流程定义", notes = "新增或修改流程定义")
    @RequestMapping(value = "/addOrUpdate", method = RequestMethod.POST)
    @ResultBody
    public Long addOrUpdate(@RequestBody WorkflowDefineInfoBO bo) {
        Long id=workflowDefineInfoService.addOrUpdate(bo);
        return id;
    }

    /**
     * 查询流程定义列表
     * @return
     */
    @ApiOperation(value = "查询流程定义列表", notes = "查询流程定义列表")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResultBody
    public WorkflowDefineInfoListDto getList(@RequestBody ListBO bo) {
        return workflowDefineInfoService.getList(bo);
    }

    /**
     * 查询流程定义详情
     * @return
     */
    @ApiOperation(value = "查询流程定义详情", notes = "查询流程定义详情")
    @RequestMapping(value = "/getById", method = RequestMethod.POST)
    @ResultBody
    public WorkflowDefineInfo getById(@RequestBody InfoBO bo) {
        return workflowDefineInfoService.getById(bo);
    }

    /**
     * 获取类别集合
     * @return
     */
    @ApiOperation(value = "获取类别集合", notes = "获取类别集合")
    @RequestMapping(value = "/getClassifyList", method = RequestMethod.POST)
    @ResultBody
    public List<WorkflowClassify> getClassifyList() {
        return workflowDefineInfoService.getClassifyList();
    }

    /**
     * 删除流程定义
     * @return
     */
    @ApiOperation(value = "删除流程定义", notes = "删除流程定义")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResultBody
    public Boolean delete(@RequestBody WorkflowDefineInfoBO bo) {
        return workflowDefineInfoService.delete(bo);
    }
}
