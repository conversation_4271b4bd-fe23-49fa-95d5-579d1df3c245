package com.wangda.oa.modules.workflow.controller.workflow;

import com.wangda.boot.annotation.ResultBody;
import com.wangda.oa.modules.workflow.bo.flow.*;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import com.wangda.oa.modules.workflow.dto.workflow.UserTaskListDto;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowUserTaskExtensionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午8:32
 */
@Api(tags = "流程节点定义")
@RestController
@CrossOrigin
@RequestMapping("/api/workflowUserExtension")
public class WorkflowUserTaskExtensionController {

    @Resource
    private WorkflowUserTaskExtensionService workflowUserTaskExtensionService;

    /**
     * 新增或修改用户任务定义扩展
     * @return
     */
    @ApiOperation(value = "新增或修改用户任务定义扩展", notes = "新增或修改用户任务定义扩展")
    @RequestMapping(value = "/addOrUpdateExtension", method = RequestMethod.POST)
    @ResultBody
    public Long addOrUpdateExtension(@RequestBody @Validated WorkflowUserTaskExtensionBO bo) {
        Long id=workflowUserTaskExtensionService.addOrUpdateExtension(bo);
        return id;
    }

    /**
     * 新增或修改用户任务表单读写定义
     * @return
     */
    @ApiOperation(value = "新增或修改用户任务表单读写定义", notes = "新增或修改用户任务表单读写定义")
    @RequestMapping(value = "/addOrUpdateFormRw", method = RequestMethod.POST)
    @ResultBody
    public Long addOrUpdateFormRw(@RequestBody @Validated WorkflowUserTaskFormRwBO bo) {
        Long id=workflowUserTaskExtensionService.addOrUpdateFormRw(bo);
        return id;
    }

    /**
     * 新增或修改用户任务操作按钮定义
     * @return
     */
    @ApiOperation(value = "新增或修改用户任务操作按钮定义", notes = "新增或修改用户任务操作按钮定义")
    @RequestMapping(value = "/addOrUpdateButton", method = RequestMethod.POST)
    @ResultBody
    public Long addOrUpdateButton(@RequestBody @Validated WorkflowUserTaskButtonBO bo) {
        Long id=workflowUserTaskExtensionService.addOrUpdateButton(bo);
        return id;
    }

    /**
     * 查询用户任务定义扩展列表
     * @return
     */
    @ApiOperation(value = "查询用户任务定义扩展列表", notes = "查询用户任务定义扩展列表")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResultBody
    public UserTaskListDto getList(@RequestBody @Validated ListBO bo) {
        return workflowUserTaskExtensionService.getList(bo);
    }

    /**
     * 删除用户任务定义扩展
     * @return
     */
    @ApiOperation(value = "删除用户任务定义扩展", notes = "删除用户任务定义扩展")
    @RequestMapping(value = "/deleteExtension", method = RequestMethod.POST)
    @ResultBody
    public Boolean deleteExtension(@RequestBody @Validated InfoBO bo) {
        return workflowUserTaskExtensionService.deleteExtension(bo);
    }

    /**
     * 删除用户任务表单读写定义
     * @return
     */
    @ApiOperation(value = "删除用户任务表单读写定义", notes = "删除用户任务表单读写定义")
    @RequestMapping(value = "/deleteFormRw", method = RequestMethod.POST)
    @ResultBody
    public Boolean deleteFormRw(@RequestBody @Validated InfoBO bo) {
        return workflowUserTaskExtensionService.deleteFormRw(bo);
    }

    /**
     * 删除用户任务操作按钮定义
     * @return
     */
    @ApiOperation(value = "删除用户任务操作按钮定义", notes = "删除用户任务操作按钮定义")
    @RequestMapping(value = "/deleteButton", method = RequestMethod.POST)
    @ResultBody
    public Boolean deleteButton(@RequestBody @Validated InfoBO bo) {
        return workflowUserTaskExtensionService.deleteButton(bo);
    }

    /**
     * 根据key查询详情
     * @return
     */
    @ApiOperation(value = "根据key查询详情", notes = "根据key查询详情")
    @RequestMapping(value = "/getInfoByKey", method = RequestMethod.POST)
    @ResultBody
    public WorkflowUserTaskExtension getInfoByKey(@RequestBody @Validated GetInfoByKeyBO bo) {
        return workflowUserTaskExtensionService.getInfoByKey(bo);
    }

}
