package com.wangda.oa.modules.workflow.domain.workflow.vo;

import com.wangda.oa.utils.ElAdminConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-03
 */
@Data
@ApiModel("工作流处理单--请求参数")
public class FlowBpmVo {

    @NotBlank
    @ApiModelProperty("任务Id")
    private String taskId;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("处理用户 username,node")
    private List<TaskAssigneeVo> taskAssignee;

    @ApiModelProperty("预签选择用户节点 username,node")
    private List<PreSignVo> preSignList;

    @ApiModelProperty("预签抄送选择用户节点 username,node")
    private List<PreSignVo> preSignCopyToList;

    @ApiModelProperty("任务意见")
    private String remark;

    @ApiModelProperty("手写签名")
    private String signature;

    @ApiModelProperty("意见附件")
    private String remarkAttachment;

    @ApiModelProperty("目标节点")
    private String targetTaskKey;

    @ApiModelProperty("目标连接线")
    private String targetFlowKey;

    @ApiModelProperty("通知方式")
    private String remindWay;

    @ApiModelProperty("流程实例Id")
    private String processInstanceId;

    // 后续节点信息
    public String taskNodesToString() {
        StringBuilder assignee = new StringBuilder();
        if(!CollectionUtils.isEmpty(this.taskAssignee)) {
            this.taskAssignee.forEach(taskAssigneeVo -> {
                if(assignee.indexOf(taskAssigneeVo.getTaskKey()) == -1) {
                    assignee.append(taskAssigneeVo.getTaskKey()).append(ElAdminConstant.SEPARATOR_COMMA);
                }
            });
        }
        if(assignee.length() == 0) {
            return assignee.toString();
        }
        return assignee.substring(0, assignee.length()-1);
    }

    // 后续节点处理人
    public String taskAssigneeFormatToString() {
        StringBuilder assignee = new StringBuilder();
        if(!CollectionUtils.isEmpty(this.taskAssignee)) {
            this.taskAssignee.forEach(taskAssigneeVo -> {
                assignee.append(taskAssigneeVo.getUsername()).append(ElAdminConstant.SEPARATOR_DOLLAR)
                        .append(taskAssigneeVo.getTaskKey()).append(ElAdminConstant.SEPARATOR_COMMA);
            });
        }
        if(assignee.length() == 0) {
            return assignee.toString();
        }
        return assignee.substring(0, assignee.length()-1);
    }

    // 预签用户信息 usertask2:zhangsan$usertask2,lisi$usertask2#usertask3:zhangsan$usertask3
    public String preSignAssigneeFormatToString() {
        StringBuilder assignee = new StringBuilder();
        if(!CollectionUtils.isEmpty(this.preSignList)) {
            preSignList.forEach(preSignVo -> {
                assignee.append(preSignVo.getTaskDefKey()).append(ElAdminConstant.SEPARATOR_COLON);
                preSignVo.getPreSignAssignee().forEach(taskAssigneeVo -> {
                    assignee.append(taskAssigneeVo.getUsername()).append(ElAdminConstant.SEPARATOR_DOLLAR)
                            .append(taskAssigneeVo.getTaskKey()).append(ElAdminConstant.SEPARATOR_COMMA);
                });
                if(assignee.length() > 0) {
                    assignee.deleteCharAt(assignee.length() - 1);
                }
                assignee.append(ElAdminConstant.SEPARATOR_POUND);
            });
        }
        if(assignee.length() == 0) {
            return assignee.toString();
        }
        return assignee.substring(0, assignee.length()-1);
    }

    // 预签抄送用户信息
    public String preSignCopyToAssigneeFormatToString() {
        StringBuilder assignee = new StringBuilder();
        if(!CollectionUtils.isEmpty(this.preSignCopyToList)) {
            preSignCopyToList.forEach(preSignVo -> {
                assignee.append(preSignVo.getTaskDefKey()).append(ElAdminConstant.SEPARATOR_COLON);
                preSignVo.getPreSignAssignee().forEach(taskAssigneeVo -> {
                    assignee.append(taskAssigneeVo.getUsername()).append(ElAdminConstant.SEPARATOR_DOLLAR)
                            .append(taskAssigneeVo.getTaskKey()).append(ElAdminConstant.SEPARATOR_COMMA);
                });
                if(assignee.length() > 0) {
                    assignee.deleteCharAt(assignee.length() - 1);
                }
                assignee.append(ElAdminConstant.SEPARATOR_POUND);
            });
        }
        if(assignee.length() == 0) {
            return assignee.toString();
        }
        return assignee.substring(0, assignee.length()-1);
    }
}
