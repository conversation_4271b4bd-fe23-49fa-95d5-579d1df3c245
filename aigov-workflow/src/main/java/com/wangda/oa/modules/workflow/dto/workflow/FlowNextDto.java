package com.wangda.oa.modules.workflow.dto.workflow;

import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.utils.ElAdminConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 动态人员、组
 * <AUTHOR>
 * @date 2021/5/12 22:59
 */
@Data
@Builder
@ApiModel("下个任务节点")
public class FlowNextDto implements Serializable {

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务key")
    private String taskDefKey;

    @ApiModelProperty("任务执行类型 single|multi-parallel-any|multi-parallel-all|multi-sequential|fork|blank")
    @Builder.Default
    private String taskExecType = "multi-parallel-any";

    @ApiModelProperty("候选人")
    private List<SimpleUserDto> candidateUser;

    @ApiModelProperty("用户选择器 none|normal")
    private String userSelector;

    // 后续节点处理人
    public String taskAssigneeFormatToString() {
        StringBuilder assignee = new StringBuilder();
        if(!CollectionUtils.isEmpty(this.candidateUser)) {
            this.candidateUser.forEach(taskAssigneeVo -> {
                assignee.append(taskAssigneeVo.getUserName()).append(ElAdminConstant.SEPARATOR_COMMA);
            });
        }
        if(assignee.length() == 0) {
            return assignee.toString();
        }
        return assignee.substring(0, assignee.length()-1);
    }
}
