package com.wangda.oa.modules.workflow.enums.workflow;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/28
 * @description 流程类别
 */
@Getter
@AllArgsConstructor
public enum CategoryTypeEnum {

    CATEGORY_GWGL("gwgl","公文管理"),
    CATEGORY_XZFW("xzfw","行政服务"),
    CATEGORY_XXFW("xxfw","信息服务");

    private String value;
    private String name;

    public static CategoryTypeEnum getByValue(String value) {
        for (CategoryTypeEnum workflowType : CategoryTypeEnum.values()) {
            if (workflowType.value.equals(value)) {
                return workflowType;
            }
        }
        return null;
    }

    /**
     * 根据name获取code
     * @param name
     * @return
     */
    public static String getBy(String name) {
        for (CategoryTypeEnum templateModuleEnums : CategoryTypeEnum.values()) {
            if (templateModuleEnums.getName().equals(name)) {
                return templateModuleEnums.getValue();
            }
        }
        return null;
    }
}
