package com.wangda.oa.modules.workflow.service.workflow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.workflow.dto.workflow.FlowProcDefDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowSaveXmlVo;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-04-03 14:41
 */
public interface FlowDefinitionService {

    boolean exist(String processDefinitionKey);


    /**
     * 流程定义列表
     *
     * @param pageNum  当前页码
     * @param pageSize 每页条数
     * @param name
     * @return 流程定义分页列表数据
     */
    Page<FlowProcDefDto> list(Integer pageNum, Integer pageSize, String name);

    /**
     * 导入流程文件
     *
     * @param name
     * @param category
     * @param in
     */
    void importFile(String name, String category, InputStream in);

    /**
     * 修改流程文件
     *
     * @param vo
     */
    void updateFile(FlowSaveXmlVo vo);

    /**
     * 读取xml
     * @param deployId
     * @return
     */
    ResultJson readXml(String deployId) throws IOException;

    /**
     * 读取xml
     * @param procDefKey
     * @return
     */
    ResultJson readXmlByDefKey(String procDefKey) throws IOException;

    /**
     * 根据流程定义ID启动流程实例
     *
     * @param procDefId
     * @param variables
     * @return
     */
    ResultJson startProcessInstanceById(String procDefId, Map<String, Object> variables);

    /**
     * 根据流程定义Key启动流程实例
     *
     * @param procDefKey
     * @param startUserId 启动人Id
     * @return
     */
    ResultJson startProcessInstanceByKey(String procDefKey, Long startUserId);

    /**
     * 激活或挂起流程定义
     *
     * @param state    状态
     * @param deployId 流程部署ID
     */
    void updateState(Integer state, String deployId);


    /**
     * 删除流程定义
     *
     * @param deployId 流程部署ID act_ge_bytearray 表中 deployment_id值
     */
    void delete(String deployId);


    /**
     * 读取图片文件
     * @param deployId
     * @return
     */
    InputStream readImage(String deployId);

    /**
     * 获取节点key列表
     * @param deployId
     * @return
     */
    List<Object> findProcessKeys(String deployId);
}
