package com.wangda.oa.modules.workflow.listener;

import com.alibaba.fastjson.JSON;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.type.LogActionType;
import com.wangda.oa.modules.extension.bo.zwdd.task.TaskFinishBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkNotificationLinkBO;
import com.wangda.oa.modules.extension.bo.zwdd.work.WorkRevokeBO;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.system.domain.BusinessLog;
import com.wangda.oa.modules.system.service.BusinessLogService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.utils.ChildUserUtils;
import com.wangda.oa.modules.workflow.config.SendTaskMessageConfig;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.enums.workflow.TaskRemindWayEnum;
import com.wangda.oa.modules.workflow.factory.AfterTransactionOpt;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.service.IGbzjTaskService;
import com.wangda.oa.modules.workflow.service.ILabTaskService;
import com.wangda.oa.modules.workflow.service.ISysAppService;
import com.wangda.oa.modules.workflow.service.IZwddService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowCustomExtensionService;
import com.wangda.oa.modules.workflow.service.workflow.impl.ProcessELService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.common.engine.api.delegate.event.FlowableEvent;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.event.impl.FlowableEntityEventImpl;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description 普通任务完成监听
 */
@Slf4j
@Component
public class FlowableGlobalTaskCompleteListener implements FlowableEventListener {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RestUrlComponent restUrlComponent;

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private SendTaskMessageConfig sendTaskMessageConfig;

    @Autowired
    private WorkflowCustomExtensionService workflowCustomExtensionService;

    @Autowired
    private UserService userService;

    @Resource
    private ProcessELService processELService;

    @Resource
    private AfterTransactionOpt afterTransactionOpt;

    @Resource
    private BusinessLogService businessLogService;

    // zwdd
    @Resource
    private ZwddProperties zwddProperties;
    @Autowired(required = false)
    private IZwddService zwddService;
    @Autowired(required = false)
    private ISysAppService sysAppService;

    @Autowired(required = false)
    private ILabTaskService labTaskService;
    @Autowired(required = false)
    private IGbzjTaskService gbzjTaskService;

    @Override
    public void onEvent(FlowableEvent flowableEvent) {
        FlowableEntityEventImpl event = (FlowableEntityEventImpl) flowableEvent;
        TaskEntity taskEntity = (TaskEntity) event.getEntity();

        if(StringUtils.isEmpty(taskEntity.getAssignee())) {
            log.warn("任务完成监听，处理人没有");
            return;
        }
        log.info("进入完成任务监听 " + taskEntity.getName() + "," + taskEntity.getAssignee() + "," + taskEntity.getId());

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(taskEntity.getProcessInstanceId())
                .includeProcessVariables()
                .singleResult();

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(processInstance.getProcessDefinitionId()).singleResult();

        String subject = "您的" + processDefinition.getName() + taskEntity.getName() + "已处理";

        //完成oa待办
        String pcUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/webPage?processInstanceId=" + processInstance.getProcessInstanceId();
        String messageUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/page?processInstanceId=" + processInstance.getProcessInstanceId();

        Map<String, Object> variables = processInstance.getProcessVariables();
        try {
            // 发送统一待办
            if(sendTaskMessageConfig.getSendBacklog()) {
                List alreadyUserListBO = new ArrayList();
                UserDto userDto = userService.findById(Long.parseLong(processInstance.getStartUserId()));
                AlreadyUserListBO alreadyUserDto = AlreadyUserListBO.builder().transactionTime(Instant.now().getEpochSecond()).userId(taskEntity.getAssignee()).build();
                alreadyUserListBO.add(alreadyUserDto);
                // 表单数据
                String formData = JSON.toJSONString(variables.get(ProcessConstants.BPM_FORM_DATA));
                flowTaskService.sendBacklogList(null, formData, null, processInstance, userDto.getUsername(), messageUrl, pcUrl, alreadyUserListBO);
            }

            // 发送之江实验室
            if(sendTaskMessageConfig.getSendZhejianglab()) {
                // 实验室完成待办任务
                String completTaskResult = labTaskService.updateBpmTodoTask(Long.parseLong(taskEntity.getProcessInstanceId()), Long.parseLong(taskEntity.getId()), 1, 1, "");
                log.info("实验室完成任务结果：" + completTaskResult);
            }

            // 多实例判断是否完成
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(taskEntity.getTaskDefinitionKey());
            boolean isCompletion = true;
            if(Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
                isCompletion = processELService.completionConditionSatisfied(taskEntity.getExecutionId(), (MultiInstanceActivityBehavior) currentFlowNode.getBehavior());
            }

            // 发送预签抄送
            Object preSignCopyToAssigneeVar = variables.get(ProcessConstants.BPM_BPM_PRESIGNCOPYTOASSIGNEE);
            if(Objects.nonNull(preSignCopyToAssigneeVar) && isCompletion) {
                Map<String, String> preSignCopyToAssigneeMap = FlowableUtils.formatPreSignAssignee(preSignCopyToAssigneeVar.toString());
                if(preSignCopyToAssigneeMap.containsKey(taskEntity.getTaskDefinitionKey())) {
                    String copyToAssignees = preSignCopyToAssigneeMap.get(taskEntity.getTaskDefinitionKey());
                    List<String> userList = processELService.getResolveUsers(copyToAssignees, taskEntity.getTaskDefinitionKey());
                    workflowCustomExtensionService.sendCopyToMessage(processInstance.getProcessInstanceId(), userList);
                }
            }

            // 记录操作日志
            BusinessLog businessLog = new BusinessLog();
            businessLog.setOperationType(LogActionType.UPDATE.getValue());
            businessLog.setBusinessType("BPM");
            businessLog.setBusinessId(processInstance.getProcessInstanceId());
            businessLog.setUsername(ChildUserUtils.getCurrentChildUsername());
            businessLog.setRepresent(taskEntity.getName());
            afterTransactionOpt.execute(new Runnable() {
                @Override
                public void run() {
                    businessLogService.create(businessLog);
                }
            });
        }catch(Exception e) {
            log.error("完成oa待办失败", e.getMessage());
        }

        // 发送政务钉钉
        if(sendTaskMessageConfig.getSendZwdd()) {
            SysUserPlatform sysUserPlatform = sysAppService.getByUserNameAndType(taskEntity.getAssignee(), zwddProperties.getType());
            if(Objects.isNull(sysUserPlatform)) {
                ResultJson resultJson = sysAppService.getUid(taskEntity.getAssignee());
                if(Objects.nonNull(resultJson) && resultJson.isSuccess()) {
                    sysUserPlatform = new SysUserPlatform();
                    sysUserPlatform.setPlatformUserId(resultJson.getData().toString());
                }else {
                    sysUserPlatform = sysAppService.getByUserNameAndType("admin", zwddProperties.getType());
                }
            }
            if(Objects.isNull(sysUserPlatform) || StringUtils.isEmpty(sysUserPlatform.getPlatformUserId())) {
                log.error("FlowableGlobalTaskCompleteListener:" + taskEntity.getAssignee() + ",当前用户没有绑定浙政钉编号");
                return;
            }

            TaskFinishBO taskCreateBO = TaskFinishBO.builder()
                    .userId(sysUserPlatform.getPlatformUserId())
                    .bizTaskId(taskEntity.getId())
                    .build();

            // 完成待办
            afterTransactionOpt.execute(() -> zwddService.taskFinish(taskCreateBO));

            // 获取节点消息提醒方式
            BpmnModel bpmnModel = repositoryService.getBpmnModel(taskEntity.getProcessDefinitionId());
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(taskEntity.getTaskDefinitionKey());
            String taskRemindWays = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMINDWAY);
            // 发送浙政钉通知
            if(StringUtils.isNotBlank(taskRemindWays) && taskRemindWays.contains(TaskRemindWayEnum.ZWDD.getValue())) {
                // 取消已完成处理人工作通知
                WorkRevokeBO workRevokeBO = WorkRevokeBO.builder()
                        .bizMsgId(taskEntity.getId())
                        .build();
                afterTransactionOpt.execute(new Runnable() {
                    @Override
                    public void run() {
                        zwddService.revokeMessageInfo(workRevokeBO);
                    }
                });

                // 发送通知给发起人
                // 流程发起人
                SysUserPlatform creatorUserPlatform = sysAppService.getByUserId(processInstance.getStartUserId(), zwddProperties.getType());

                // 处理钉钉id
                if(Objects.isNull(creatorUserPlatform)) {
                    log.error("浙政钉给发起人发送处理消息失败FlowableGlobalTaskCompleteListener：找不到平台用户" + processInstance.getStartUserId());
                }else if(Objects.nonNull(creatorUserPlatform) && !creatorUserPlatform.getPlatformUserId().equals(sysUserPlatform.getPlatformUserId())) {
                    // 自己提交的节点不发通知
                    WorkNotificationLinkBO workNotificationLinkBO = WorkNotificationLinkBO.builder()
                            .title(processDefinition.getName())
                            .messageUrl(messageUrl)
                            .picUrl("@lALOACZwe2Rk")
                            .text(subject)
                            .build();
                    WorkNotificationBO workNotificationBO = WorkNotificationBO.builder()
                            .receiverIds(creatorUserPlatform.getPlatformUserId())
                            .bizMsgId(taskEntity.getExecutionId())
                            .type(1)
                            .linkBO(workNotificationLinkBO)
                            .build();

                    // 给发起人发送通知
                    afterTransactionOpt.execute(new Runnable() {
                        @Override
                        public void run() {
                            zwddService.workNotification(workNotificationBO);
                        }
                    });
                }
            }
        }

        // 发送干部之家
        if(sendTaskMessageConfig.getSendGbzjoa()) {
            try {
                gbzjTaskService.completeOaTask(taskEntity.getProcessInstanceId(), taskEntity.getId());
            }catch(Exception e) {
                log.error("发送干部之家任务完成错误：procInstId:" + taskEntity.getProcessInstanceId() + " taskId:" + taskEntity.getId());
                e.printStackTrace();
            }
        }
    }

    @Override
    public boolean isFailOnException() {
        return true;
    }

    @Override
    public boolean isFireOnTransactionLifecycleEvent() {
        return false;
    }

    @Override
    public String getOnTransaction() {
        return null;
    }
}
