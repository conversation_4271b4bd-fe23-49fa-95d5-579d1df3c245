package com.wangda.oa.modules.workflow.domain.workflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("工作流任务相关-预签")
public class PreSignVo implements Serializable {

    @ApiModelProperty("任务Key")
    private String taskDefKey;

    @ApiModelProperty("预签选择用户节点 username,node")
    private List<TaskAssigneeVo> preSignAssignee;

}
