package com.wangda.oa.modules.workflow.domain.workflow.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>流程任务<p>
 *
 * <AUTHOR>
 * @date 2021-04-03
 */
@Data
@ApiModel("工作流任务相关--请求参数")
public class FlowTaskVo {

    @ApiModelProperty("表单数据")
    private Object formData;

    @ApiModelProperty("处理单数据")
    private FlowBpmVo bpmData;

}
