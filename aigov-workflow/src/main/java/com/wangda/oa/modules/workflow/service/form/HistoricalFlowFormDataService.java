package com.wangda.oa.modules.workflow.service.form;

import com.wangda.oa.modules.workflow.domain.form.HistoricalFlowFormData;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @date 2022/04/01 15:05
 * @Description 业务表单历史数据服务
 */
public interface HistoricalFlowFormDataService {

    HistoricalFlowFormData save(HistoricalFlowFormData historicalFlowFormData);

    Page queryAllByProcInstId(String procInstId, Pageable pageable);

    /**
     * 获取历史表单数据
     * @param id
     * @return
     */
    HistoricalFlowFormData getHistoricalFormData(Long id);

    /**
     * 根据流程实例id删除所有历史数据
     * @param procInstId
     * @return
     */
    void deleteAllFormDataByProcInstId(String procInstId);

    /**
     * 删除
     * @param id
     * @return
     */
    void deleteById(Long id);
}
