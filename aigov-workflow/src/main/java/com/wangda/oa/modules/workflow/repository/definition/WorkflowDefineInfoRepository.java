package com.wangda.oa.modules.workflow.repository.definition;

import com.wangda.oa.modules.workflow.domain.definition.WorkflowDefineInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/13 下午8:07
 */
public interface WorkflowDefineInfoRepository extends JpaRepository<WorkflowDefineInfo,Long>, JpaSpecificationExecutor<WorkflowDefineInfo> {

    /**
     * 根据流程标识和最新版本
     * @param processDefinitionKey
     * @param latestVersion
     * @return
     */
    List<WorkflowDefineInfo> findByProcessDefinitionKeyAndLatestVersion(String processDefinitionKey,Integer latestVersion);

    /**
     * 根据流程标识查询数量
     * @param processDefinitionKey
     * @return
     */
    int countByProcessDefinitionKey(String processDefinitionKey);
}
