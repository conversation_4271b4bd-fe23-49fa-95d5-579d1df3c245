package com.wangda.oa.modules.workflow.service.workflow;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.workflow.domain.workflow.ProcessArchive;
import com.wangda.oa.modules.workflow.domain.workflow.ProcessCommentArchive;
import com.wangda.oa.modules.workflow.domain.workflow.vo.FlowRemarkVo;
import com.wangda.oa.modules.workflow.domain.workflow.vo.FlowTaskVo;
import com.wangda.oa.modules.workflow.domain.workflow.vo.ProcessQueryVo;
import com.wangda.oa.modules.workflow.domain.workflow.vo.TaskUserReadVo;
import com.wangda.oa.modules.workflow.dto.workflow.FlowProcessInfoDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskAssigneeDto;
import com.wangda.oa.modules.workflow.dto.workflow.ProcessCommentArchiveDto;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-08-03 14:42
 */
public interface FlowInstanceService {

    /**
     * 我发起的流程
     * @param query
     * @return
     */
    Page myProcess(ProcessQueryVo query);

    /**
     * 查询流程
     * @param query
     * @return
     */
    Page queryProcess(ProcessQueryVo query);


    /**
     * 流程历史流转记录
     * @param procInsId 流程实例Id
     * @return
     */
    ResultJson flowRecord(String procInsId);

    /**
     * 保存已阅
     * @param taskUserReadVo
     */
    void saveHasRead(TaskUserReadVo taskUserReadVo);

    /**
     * 撤回流程
     * @param flowTaskVo
     * @return
     */
    ResultJson revokeProcess(FlowTaskVo flowTaskVo);

    /**
     * 获取恢复流程所有可选择的节点
     * @return ResultJson
     */
    ResultJson findReactivateTaskList(String procInstanceId);

    /**
     * 恢复流程
     * @param flowTaskVo
     * @return
     */
    ResultJson reactivateProcess(FlowTaskVo flowTaskVo) throws Exception;

    /**
     * 获取流程过程图
     * @param processId
     * @return
     */
    InputStream diagram(String processId);

    /**
     * 获取当前流程任务处理人
     * @param procInstanceId
     * @return
     */
    ResultJson getTaskAssignees(String procInstanceId);

    /**
     * 同步流程表单意见
     * @param procInstanceId
     * @param isAll          是否全部表单数据同步
     * @return
     */
    ResultJson syncFormRemark(String procInstanceId, Boolean isAll);

    /**
     * 删除启动状态超过1天的流程
     */
    ResultJson deleteProcessForExpired();

    /**
     * 终止流程实例
     * @param flowTaskVo
     */
    ResultJson cancelProcess(FlowTaskVo flowTaskVo);

    /**
     * 删除流程实例
     * @param instanceIds
     * @param deleteReason
     */
    void delete(String instanceIds, String deleteReason);

    /**
     * 流程归档
     * @param processArchive 请求实体参数
     */
    void archiveProcess(ProcessArchive processArchive);

    /**
     * 意见归档
     * @param commentArchiveDto 请求实体参数
     */
    void archiveComment(ProcessCommentArchiveDto commentArchiveDto);

    /**
     * 意见归档
     * @param commentArchiveDtoList 请求实体参数
     */
    void archiveComment(List<ProcessCommentArchiveDto> commentArchiveDtoList);

    /**
     * 获取归档流程
     * @param procInstanceId
     * @return ProcessArchive
     */
    ProcessArchive getByProcInstanceId(String procInstanceId);

    /**
     * 根据流程id获取意见
     * @param procInstanceId
     * @return List
     */
    List<ProcessCommentArchive> findByProcInstanceId(String procInstanceId);

    /**
     * 根据流程实例id获取流程相关id
     * @param procInsId
     * @return
     */
    Map<String, Object> queryParamsByProcInsId(String procInsId);

    /**
     * 根据流程实例id获取流程相关信息
     * @param procInsId
     * @return
     */
    FlowProcessInfoDto getProcessInfoByProcInsId(String procInsId);

    /**
     * 根据流程实例id获取任务相关信息
     * @param procInsId
     * @return
     */
    List<FlowTaskAssigneeDto> getTaskInfoByProcInsId(String procInsId);

    /**
     * 意见排序
     * @param flowRemarkVo
     * @return String
     */
    String sortFormRemark(FlowRemarkVo flowRemarkVo);
}
