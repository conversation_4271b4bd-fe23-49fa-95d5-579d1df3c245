package com.wangda.oa.modules.workflow.repository.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskButtons;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午8:30
 */
public interface WorkflowUserTaskButtonsRepository extends JpaRepository<WorkflowUserTaskButtons,Long>, JpaSpecificationExecutor<WorkflowUserTaskButtons> {

    /**
     * 根据用户任务定义ID删除数据
     * @param taskDefinitionId
     */
    @Transactional
    void deleteByTaskDefinitionId(Long taskDefinitionId);

    /**
     * 查看序号是否存在(编辑)
     * @param sort
     * @return
     */
    int countBySort(Integer sort);

    /**
     * 查看序号是否存在(编辑)
     * @param sort 排序
     * @param id 主键
     * @return
     */
    int countBySortAndIdNot(Integer sort,Long id);
}
