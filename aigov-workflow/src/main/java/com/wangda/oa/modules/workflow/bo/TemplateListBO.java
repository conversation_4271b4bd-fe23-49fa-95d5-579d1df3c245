package com.wangda.oa.modules.workflow.bo;

import com.wangda.boot.platform.base.Pagination;
import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/3/18 下午8:10
 */
@Data
public class TemplateListBO extends Pagination {
    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "填报id不能为空")
    private String userId;

    @ApiModelProperty(value = "类别")
    @Query(type = Query.Type.EQUAL)
    private String category;

    @ApiModelProperty(value = "类型（0:我的,1:全部）")
    private Integer type=1;
}
