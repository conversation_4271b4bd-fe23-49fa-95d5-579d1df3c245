package com.wangda.oa.modules.workflow.service.workflow.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.bo.flow.InfoBO;
import com.wangda.oa.modules.workflow.bo.flow.ListBO;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowDeployForm;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskButtons;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskFormRw;
import com.wangda.oa.modules.workflow.domain.workflow.vo.DeployFormVo;
import com.wangda.oa.modules.workflow.dto.workflow.FlowProcDefDto;
import com.wangda.oa.modules.workflow.dto.workflow.FlowSaveXmlVo;
import com.wangda.oa.modules.workflow.dto.workflow.UserTaskListDto;
import com.wangda.oa.modules.workflow.factory.DeploymentChangedCmd;
import com.wangda.oa.modules.workflow.factory.FlowServiceFactory;
import com.wangda.oa.modules.workflow.repository.workflow.WorkflowUserTaskButtonsRepository;
import com.wangda.oa.modules.workflow.repository.workflow.WorkflowUserTaskExtensionRepository;
import com.wangda.oa.modules.workflow.repository.workflow.WorkflowUserTaskFormRwRepository;
import com.wangda.oa.modules.workflow.service.definition.WorkflowDeployFormService;
import com.wangda.oa.modules.workflow.service.workflow.FlowDefinitionService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowCustomExtensionService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowUserTaskExtensionService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.ManagementService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;
import org.flowable.task.api.Task;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.Principal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程定义
 * <AUTHOR>
 * @date 2021-04-03
 */
@Service
@Slf4j
@Transactional
public class FlowDefinitionServiceImpl extends FlowServiceFactory implements FlowDefinitionService {

    @Resource
    private WorkflowDeployFormService sysDeployFormService;

    @Resource
    private ManagementService managementService;

    @Resource
    private UserService userService;

    @Resource
    private WorkflowCustomExtensionService workflowCustomExtensionService;

    @Resource
    private WorkflowUserTaskExtensionService workflowUserTaskExtensionService;

    @Resource
    private WorkflowDeployFormService workflowDeployFormService;

    @Resource
    private WorkflowUserTaskExtensionRepository workflowUserTaskExtensionRepository;

    @Resource
    private WorkflowUserTaskButtonsRepository workflowUserTaskButtonsRepository;

    @Resource
    private WorkflowUserTaskFormRwRepository workflowUserTaskFormRwRepository;

    private static final String BPMN_FILE_SUFFIX = ".bpmn";

    @Override
    public boolean exist(String processDefinitionKey) {
        ProcessDefinitionQuery processDefinitionQuery
                = repositoryService.createProcessDefinitionQuery().processDefinitionKey(processDefinitionKey);
        long count = processDefinitionQuery.count();
        return count > 0 ? true : false;
    }

    /**
     * 流程定义列表
     * @param pageNum  当前页码
     * @param pageSize 每页条数
     * @return 流程定义分页列表数据
     */
    @Override
    public Page<FlowProcDefDto> list(Integer pageNum, Integer pageSize, String name) {
        Page<FlowProcDefDto> page = new Page<>();
        // 流程定义列表数据查询
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery()
                .latestVersion()
                .orderByProcessDefinitionKey().asc();
        if(StringUtils.isNotEmpty(name)) {
            processDefinitionQuery.processDefinitionNameLike(name + "%");
        }
        page.setTotal(processDefinitionQuery.count());
        List<ProcessDefinition> processDefinitionList = processDefinitionQuery.listPage(pageNum * pageSize, pageSize);

        List<FlowProcDefDto> dataList = new ArrayList<>();
        for(ProcessDefinition processDefinition : processDefinitionList) {
            String deploymentId = processDefinition.getDeploymentId();
            Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(deploymentId).singleResult();
            FlowProcDefDto reProcDef = new FlowProcDefDto();
            BeanUtils.copyProperties(processDefinition, reProcDef);
            DeployFormVo sysForm = sysDeployFormService.selectWorkflowDeployFormByDeployId(deploymentId);
            String workflowTypeIcon = workflowCustomExtensionService.getWorkTypeIconByProcDefKey(processDefinition.getKey());
            if(StringUtils.isNotBlank(workflowTypeIcon)) {
                reProcDef.setIcon(workflowTypeIcon);
            }
            if(Objects.nonNull(sysForm)) {
                reProcDef.setDeployFormId(sysForm.getId());
                reProcDef.setFormName(sysForm.getFormName());
                reProcDef.setFormId(sysForm.getFormId());
                reProcDef.setFormSkin(sysForm.getFormSkin());
                reProcDef.setMobileFormSkin(sysForm.getMobileFormSkin());
                reProcDef.setMobileFormId(sysForm.getMobileFormId());
            }
            // 流程定义时间
            reProcDef.setDeploymentTime(deployment.getDeploymentTime());
            dataList.add(reProcDef);
        }
        page.setRecords(dataList);
        return page;
    }

    /**
     * 导入流程文件
     * @param name
     * @param category
     * @param in
     */
    @Override
    public void importFile(String name, String category, InputStream in) {
        Deployment deploy = repositoryService.createDeployment().addInputStream(name + BPMN_FILE_SUFFIX, in).name(name).category(category).deploy();
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
        repositoryService.setProcessDefinitionCategory(definition.getId(), category);

    }

    /**
     * 修改流程文件
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFile(FlowSaveXmlVo vo) {
        InputStream in = null;
        try {
            in = new ByteArrayInputStream(vo.getXml().getBytes(StandardCharsets.UTF_8));

            if(vo.getOverride()) {
                // 获取流
                ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().deploymentId(vo.getDeployId()).latestVersion().singleResult();
                managementService.executeCommand(new DeploymentChangedCmd(definition.getId(), in));
            }else {
                Deployment deploy = repositoryService.createDeployment().addInputStream(vo.getName() + BPMN_FILE_SUFFIX, in).name(vo.getName()).category(vo.getCategory()).deploy();
                ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().deploymentId(deploy.getId()).singleResult();
                repositoryService.setProcessDefinitionCategory(definition.getId(), vo.getCategory());

                // 复制老的流程扩展和挂载表单，删除同等操作
                this.flowExtensionHandle(vo.getDeployId(), deploy.getId());
            }
        }catch(Exception e) {
            log.error("导入失败:", e);
            throw new CustomException("导入失败，" + e.getMessage());
        }finally {
            try {
                if(in != null) {
                    in.close();
                }
            }catch(IOException e) {
                log.error("关闭输入流出错", e);
            }
        }

    }

    // 复制老的流程扩展和挂载表单
    @Transactional(rollbackFor = Exception.class)
    public void flowExtensionHandle(String flowDeployId, String newFlowDeployId) {
        if(StringUtils.isEmpty(flowDeployId)) {
            return;
        }
        ListBO bo = new ListBO();
        bo.setFlowDefineId(flowDeployId);
        bo.setSize(Integer.MAX_VALUE);
        UserTaskListDto userTaskListDto = workflowUserTaskExtensionService.getList(bo);
        List<WorkflowUserTaskExtension> workflowUserTaskExtensionList = userTaskListDto.getContent();

        // 处理流程扩展
        List<WorkflowUserTaskExtension> newWorkflowUserTaskExtensionList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(workflowUserTaskExtensionList)) {
            for(WorkflowUserTaskExtension workflowUserTaskExtension : workflowUserTaskExtensionList) {
                WorkflowUserTaskExtension newWorkflowUserTaskExtension = new WorkflowUserTaskExtension();
                BeanUtils.copyProperties(workflowUserTaskExtension, newWorkflowUserTaskExtension);
                newWorkflowUserTaskExtension.setId(null);
                newWorkflowUserTaskExtension.setFlowDefineId(newFlowDeployId);
                newWorkflowUserTaskExtensionList.add(newWorkflowUserTaskExtension);
            }
            newWorkflowUserTaskExtensionList = workflowUserTaskExtensionRepository.saveAll(newWorkflowUserTaskExtensionList);
            Map<String, Long> newWorkflowUserTaskExtensionMap = newWorkflowUserTaskExtensionList.stream().collect(Collectors.toMap(WorkflowUserTaskExtension::getLinkKey, WorkflowUserTaskExtension::getId));

            for(WorkflowUserTaskExtension workflowUserTaskExtension : workflowUserTaskExtensionList) {
                List<WorkflowUserTaskButtons> buttons = workflowUserTaskExtension.getButtons();
                if(!CollectionUtils.isEmpty(buttons)) {
                    List<WorkflowUserTaskButtons> newButtons = new ArrayList<>();
                    for(WorkflowUserTaskButtons button : buttons) {
                        WorkflowUserTaskButtons newButton = new WorkflowUserTaskButtons();
                        BeanUtils.copyProperties(button, newButton);
                        newButton.setId(null);
                        newButton.setFlowDefineId(newFlowDeployId);
                        newButton.setTaskDefinitionId(newWorkflowUserTaskExtensionMap.get(newButton.getLinkKey()));
                        newButtons.add(newButton);
                    }
                    workflowUserTaskButtonsRepository.saveAll(newButtons);
                }

                List<WorkflowUserTaskFormRw> readWrites = workflowUserTaskExtension.getReadWrite();
                if(!CollectionUtils.isEmpty(readWrites)) {
                    List<WorkflowUserTaskFormRw> newReadWrites = new ArrayList<>();
                    for(WorkflowUserTaskFormRw rw : readWrites) {
                        WorkflowUserTaskFormRw newRw = new WorkflowUserTaskFormRw();
                        BeanUtils.copyProperties(rw, newRw);
                        newRw.setId(null);
                        newRw.setFlowDefineId(newFlowDeployId);
                        newRw.setTaskDefinitionId(newWorkflowUserTaskExtensionMap.get(newRw.getLinkKey()));
                        newReadWrites.add(newRw);
                    }
                    workflowUserTaskFormRwRepository.saveAll(newReadWrites);
                }
            }
        }

        // 挂载表单
        DeployFormVo sysForm = sysDeployFormService.selectWorkflowDeployFormByDeployId(flowDeployId);
        WorkflowDeployForm workflowDeployForm = new WorkflowDeployForm();
        workflowDeployForm.setDeployId(newFlowDeployId);
        workflowDeployForm.setFormId(sysForm.getFormId());
        workflowDeployForm.setFormSkin(sysForm.getFormSkin());
        workflowDeployForm.setMobileFormSkin(sysForm.getMobileFormSkin());
        workflowDeployForm.setMobileFormId(sysForm.getMobileFormId());
        workflowDeployFormService.saveDeployForm(workflowDeployForm);
    }

    /**
     * 读取xml
     * @param deployId
     * @return
     */
    @Override
    public ResultJson readXml(String deployId) throws IOException {
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().deploymentId(deployId).singleResult();
        InputStream inputStream = repositoryService.getResourceAsStream(definition.getDeploymentId(), definition.getResourceName());
        String result = IOUtils.toString(inputStream, StandardCharsets.UTF_8.name());
        return ResultJson.generateResult("", result);
    }

    @Override
    public ResultJson readXmlByDefKey(String procDefKey) throws IOException {
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(procDefKey).latestVersion().singleResult();
        InputStream inputStream = repositoryService.getResourceAsStream(definition.getDeploymentId(), definition.getResourceName());
        String result = IOUtils.toString(inputStream, StandardCharsets.UTF_8.name());
        return ResultJson.generateResult("", result);
    }

    /**
     * 读取xml
     * @param deployId
     * @return
     */
    @Override
    public InputStream readImage(String deployId) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployId).singleResult();
        //获得图片流
        DefaultProcessDiagramGenerator diagramGenerator = new DefaultProcessDiagramGenerator();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        //输出为图片
        return diagramGenerator.generateDiagram(
                bpmnModel,
                "png",
                Collections.emptyList(),
                Collections.emptyList(),
                "宋体",
                "宋体",
                "宋体",
                null,
                1.0,
                false);

    }

    /**
     * 根据流程定义ID启动流程实例
     * @param procDefId 流程定义Id
     * @param variables 流程变量
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson startProcessInstanceById(String procDefId, Map<String, Object> variables) {
        if(StringUtils.isEmpty(procDefId)) {
            return ResultJson.generateResult("流程启动错误，" + ResultCodeEnum.ILLEGAL_ARGUMENT.getMessageCN(), ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(procDefId).latestVersion().singleResult();

        if(Objects.nonNull(processDefinition)) {
            DeployFormVo sysForm = sysDeployFormService.selectWorkflowDeployFormByDeployId(processDefinition.getDeploymentId());
            if(Objects.isNull(sysForm)) {
                return ResultJson.generateResult("流程启动错误，请先绑定表单", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
            }

            variables = variables == null ? new HashMap<>() : variables;
            variables.put(ProcessConstants.BPM_FORM_TEMPLATE_ID, sysForm.getFormId());
            variables.put(ProcessConstants.BPM_FORM_SKIN, sysForm.getMobileFormSkin());
            variables.put(ProcessConstants.BPM_FORM_PC_SKIN, sysForm.getFormSkin());
            variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, SecurityUtils.getCurrentUsername());
            variables.put(ProcessConstants.BPM_FORM_MOBILE_TEMPLATE_ID, sysForm.getMobileFormId());

            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
            Process process = bpmnModel.getProcess(null);
            String customEndpointApi = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.PROCESS_CUSTOM_ENDPOINT_API);
            if(StringUtils.isNotEmpty(customEndpointApi)) {
                variables.put(ProcessConstants.PROCESS_CUSTOM_ENDPOINT_API, customEndpointApi);
            }

            Long startUserId = SecurityUtils.getCurrentUserId();
            // 设置启动人
            if(Objects.nonNull(startUserId)) {
                UserDto userDto = userService.findById(startUserId);
                if(Objects.isNull(userDto)) {
                    throw new CustomException("启动错误，未找到启动人");
                }
                Authentication.setAuthenticatedUserId(startUserId.toString());
            }else {
                Authentication.setAuthenticatedUserId(SecurityUtils.getCurrentUserId().toString());
            }
            ProcessInstance processInstance = runtimeService.createProcessInstanceBuilder()
                    .processDefinitionId(procDefId)
                    .businessKey(sysForm.getFormId().toString())
                    .variables(variables)
                    .name(processDefinition.getName())
                    .start();
            Authentication.setAuthenticatedUserId(null);

            Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).active().singleResult();

            Map result = new HashMap();
            result.put("processInstanceId", processInstance.getId());
            result.put("taskId", task.getId());
            result.put(ProcessConstants.PROCESS_CUSTOM_ENDPOINT_API, customEndpointApi);

            return ResultJson.generateResult(result);
        }
        return ResultJson.generateResult("流程启动失败，流程定义ID错误", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
    }

    /**
     * 根据流程定义Key启动流程实例
     * @param procDefKey 流程定义Key
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson startProcessInstanceByKey(String procDefKey, Long startUserId) {
        if(StringUtils.isEmpty(procDefKey)) {
            return ResultJson.generateResult("流程启动错误，" + ResultCodeEnum.ILLEGAL_ARGUMENT.getMessageCN(), ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(procDefKey).latestVersion().singleResult();

        if(Objects.nonNull(processDefinition)) {
            DeployFormVo sysForm = sysDeployFormService.selectWorkflowDeployFormByDeployId(processDefinition.getDeploymentId());
            if(Objects.isNull(sysForm)) {
                return ResultJson.generateResult("流程启动错误，请先绑定表单", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
            }

            String currentUsername = SecurityUtils.getCurrentUsername();
            if(Objects.nonNull(startUserId)) {
                UserDto userDto = userService.findById(startUserId);
                if(Objects.isNull(userDto)) {
                    throw new CustomException("启动错误，未找到启动人");
                }
                currentUsername = userDto.getUsername();
            }else {
                startUserId = SecurityUtils.getCurrentUserId();
            }

            HashMap<String, Object> variables = new HashMap<>();
            variables.put(ProcessConstants.BPM_FORM_TEMPLATE_ID, sysForm.getFormId());
            variables.put(ProcessConstants.BPM_FORM_SKIN, sysForm.getMobileFormSkin());
            variables.put(ProcessConstants.BPM_FORM_PC_SKIN, sysForm.getFormSkin());
            variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, currentUsername);
            variables.put(ProcessConstants.BPM_FORM_MOBILE_TEMPLATE_ID, sysForm.getMobileFormId());

            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
            Process process = bpmnModel.getProcess(null);
            String customEndpointApi = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.PROCESS_CUSTOM_ENDPOINT_API);
            if(StringUtils.isNotEmpty(customEndpointApi)) {
                variables.put(ProcessConstants.PROCESS_CUSTOM_ENDPOINT_API, customEndpointApi);
            }

            // 设置启动人
            Principal originalPrincipal = Authentication.getAuthenticationContext().getPrincipal();
            ProcessInstance processInstance = null;
            try {
                identityService.setAuthenticatedUserId(startUserId.toString());
                processInstance = runtimeService.createProcessInstanceBuilder()
                        .processDefinitionKey(procDefKey)
                        .businessKey(sysForm.getFormId().toString())
                        .variables(variables)
                        .name(processDefinition.getName())
                        .start();
            }finally {
                Authentication.getAuthenticationContext().setPrincipal(originalPrincipal);
            }

            Task task = taskService.createTaskQuery().processInstanceId(processInstance.getId()).active().singleResult();

            Map result = new HashMap();
            result.put("processInstanceId", processInstance.getId());
            result.put("taskId", task.getId());
            result.put(ProcessConstants.PROCESS_CUSTOM_ENDPOINT_API, customEndpointApi);
            log.info("processInstance", result);

            return ResultJson.generateResult(result);
        }else {
            return ResultJson.generateResult("流程启动错误，Key不正确", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }
    }

    /**
     * 激活或挂起流程定义
     * @param state    状态
     * @param deployId 流程部署ID
     */
    @Override
    public void updateState(Integer state, String deployId) {
        ProcessDefinition procDef = repositoryService.createProcessDefinitionQuery().deploymentId(deployId).singleResult();
        // 激活
        if(state == 1) {
            repositoryService.activateProcessDefinitionById(procDef.getId(), true, null);
        }
        // 挂起
        if(state == 2) {
            repositoryService.suspendProcessDefinitionById(procDef.getId(), true, null);
        }
    }

    /**
     * 删除流程定义
     * @param deployId 流程部署ID act_ge_bytearray 表中 deployment_id值
     */
    @Override
    @Transactional
    public void delete(String deployId) {
        // true 允许级联删除 ,不设置会导致数据库外键关联异常
        repositoryService.deleteDeployment(deployId, true);

        // 删除流程扩展
        ListBO bo = new ListBO();
        bo.setFlowDefineId(deployId);
        bo.setSize(Integer.MAX_VALUE);
        UserTaskListDto userTaskListDto = workflowUserTaskExtensionService.getList(bo);
        List<WorkflowUserTaskExtension> workflowUserTaskExtensionList = userTaskListDto.getContent();
        if(!CollectionUtils.isEmpty(workflowUserTaskExtensionList)) {
            for(WorkflowUserTaskExtension workflowUserTaskExtension : workflowUserTaskExtensionList) {
                InfoBO infoBO = new InfoBO();
                infoBO.setId(workflowUserTaskExtension.getId());
                workflowUserTaskExtensionService.deleteExtension(infoBO);
            }
        }

        // 删除挂载表单
        DeployFormVo sysForm = sysDeployFormService.selectWorkflowDeployFormByDeployId(deployId);
        if(Objects.nonNull(sysForm)) {
            workflowDeployFormService.deleteWorkflowDeployFormById(sysForm.getId());
        }
    }

    /**
     * 获取流程节点key
     * @param deployId
     * @return
     */
    @Override
    public List<Object> findProcessKeys(String deployId) {
        List<Object> result = new ArrayList<>();

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployId).singleResult();

        // 获取所有节点信息
        Process process = repositoryService.getBpmnModel(processDefinition.getId()).getProcesses().get(0);
        // 获取全部节点列表，包含子节点
        Collection<FlowElement> allElements = FlowableUtils.getAllElements(process.getFlowElements(), null);

        // 获取当前任务节点元素
        if(allElements != null) {
            for(FlowElement flowElement : allElements) {
                // 当前任务节点元素
                if(flowElement instanceof UserTask) {
                    Map<String, String> obj = new HashMap<>();
                    obj.put("key", flowElement.getId());
                    obj.put("name", flowElement.getName() + ElAdminConstant.SEPARATOR_COLON + flowElement.getId());
                    result.add(obj);
                }
            }
        }

        return result;
    }

}
