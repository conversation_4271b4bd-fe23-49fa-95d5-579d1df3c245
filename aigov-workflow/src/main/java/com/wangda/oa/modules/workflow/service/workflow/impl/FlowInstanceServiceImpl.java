package com.wangda.oa.modules.workflow.service.workflow.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.modules.extension.domain.ShortLink;
import com.wangda.oa.modules.extension.service.ShortLinkService;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import com.wangda.oa.modules.workflow.bo.QueryFormBO;
import com.wangda.oa.modules.workflow.config.CustomProcessDiagramGenerator;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserRead;
import com.wangda.oa.modules.workflow.domain.workflow.ProcessArchive;
import com.wangda.oa.modules.workflow.domain.workflow.ProcessCommentArchive;
import com.wangda.oa.modules.workflow.domain.workflow.mapstruct.ProcessCommentArchiveMapper;
import com.wangda.oa.modules.workflow.domain.workflow.vo.*;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.dto.workflow.*;
import com.wangda.oa.modules.workflow.enums.workflow.AttachmentTypeEnum;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.enums.workflow.TaskCommentTypeEnum;
import com.wangda.oa.modules.workflow.factory.FlowServiceFactory;
import com.wangda.oa.modules.workflow.factory.IFlowFormHandleFactory;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadRepository;
import com.wangda.oa.modules.workflow.repository.workflow.ProcessArchiveRepository;
import com.wangda.oa.modules.workflow.repository.workflow.ProcessCommentArchiveRepository;
import com.wangda.oa.modules.workflow.service.definition.WorkflowDeployFormService;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.workflow.FlowDefinitionService;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowCustomExtensionService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricActivityInstanceQuery;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Attachment;
import org.flowable.engine.task.Comment;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/8
 * @description 流程实例服务
 */
@Service
@Slf4j
public class FlowInstanceServiceImpl extends FlowServiceFactory implements FlowInstanceService {

    @Autowired
    private ProcessArchiveRepository processArchiveRepository;

    @Autowired
    private ProcessCommentArchiveRepository processCommentArchiveRepository;

    @Resource
    private ProcessCommentArchiveMapper processCommentArchiveMapper;

    @Resource
    private UserService sysUserService;

    @Resource
    private RestUrlComponent restUrlComponent;

    @Resource
    private WorkflowDeployFormService sysInstanceFormService;

    @Resource
    private WorkflowCustomExtensionService workflowCustomExtensionService;

    @Resource
    private IFlowFormHandleFactory flowFormHandleFactory;

    @Resource
    private FormTemplateService formTemplateService;

    @Resource
    private BpmTaskUserReadRepository bpmTaskUserReadRepository;

    @Resource
    private FlowDefinitionService flowDefinitionService;

    @Resource
    private ProcessELService processELService;

    @Resource
    private UserMapperUtil userMapperUtil;

    @Resource
    private ShortLinkService shortLinkService;

    @Resource
    @Lazy
    private FlowTaskService flowTaskService;

    private final String ymdhmFormat = "yyyy-MM-dd HH:mm";

    /**
     * 我发起的流程
     * @param query
     * @return
     */
    @Override
    public Page myProcess(ProcessQueryVo query) {

        // 获取当前用户的权限 bpm:admin 流程管理员可以查看所有流程
        if(!workflowCustomExtensionService.isBpmAdmin()) {
            query.setUsername(SecurityUtils.getCurrentUsername());
        }
        Page<FlowTaskDto> page = this.queryProcess(query);
        return page;
    }

    /**
     * 查询流程列表
     * @param query
     * @return
     */
    @Override
    public Page queryProcess(ProcessQueryVo query) {
        Page<FlowTaskDto> page = new Page<>();

        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery()
                .variableExists(ProcessConstants.BPM_BPM_STARTEDBY)
                .includeProcessVariables()
                .orderByProcessInstanceStartTime()
                .desc();

        if(StringUtils.isNotEmpty(query.getUsername())) {
            UserDto startUser = sysUserService.findByName(query.getUsername());
            if(Objects.nonNull(startUser)) {
                historicProcessInstanceQuery.startedBy(startUser.getId().toString());
            }
        }else {
            //判断是否是流程管理员，若是，则查询所有被管理的流程     ----by caiyy  time:2021/10/09   start
            List<String> authorities = workflowCustomExtensionService.getBpmAdminKeys();
            if(!CollectionUtils.isEmpty(authorities)) {
                historicProcessInstanceQuery.processDefinitionKeyIn(authorities);
            }
            //判断是否是流程管理员，若是，则查询所有被管理的流程     ----by caiyy  time:2021/10/09   end
        }

        if(StringUtils.isNotEmpty(query.getProcDefKey())) {
            historicProcessInstanceQuery.processDefinitionKey(query.getProcDefKey());
        }
        if(StringUtils.isNotEmpty(query.getName())) {
            historicProcessInstanceQuery.processInstanceNameLike(query.getName() + "%");
        }
        if(!CollectionUtils.isEmpty(query.getTimeRange())) {
            try {
                historicProcessInstanceQuery.startedAfter(DateUtils.parseDate(query.getTimeRange().get(0), ymdhmFormat));
                historicProcessInstanceQuery.startedBefore(DateUtils.parseDate(query.getTimeRange().get(1), ymdhmFormat));
            }catch(Exception e) {
                log.error("日期转换错误queryProcess", e.getMessage());
            }
        }

        //流程类型
        if(StringUtils.isNotEmpty(query.getCategory())) {
            historicProcessInstanceQuery.processDefinitionCategory(query.getCategory());
        }

        // 流程状态
        if(StringUtils.isNotEmpty(query.getProcStatus())) {
            historicProcessInstanceQuery.processInstanceBusinessStatus(query.getProcStatus());
        }

        // 当前办理人
        if(StringUtils.isNotEmpty(query.getAssigneeName())) {
            List<UserDto> userDtoList = sysUserService.findByUserNickNameLike("%" + query.getAssigneeName() + "%", true);
            if(CollectionUtils.isEmpty(userDtoList)) {
                historicProcessInstanceQuery.involvedUser("-1");
            }else {
                List<String> usernameList = userDtoList.stream().map(UserDto::getUsername).collect(Collectors.toList());
                List<Task> taskList = taskService.createTaskQuery().taskAssigneeIds(usernameList).list();
                if(CollectionUtils.isEmpty(taskList)) {
                    historicProcessInstanceQuery.involvedUser("-1");
                }else {
                    Set<String> procInstIdList = taskList.stream().map(Task::getProcessInstanceId).collect(Collectors.toSet());
                    historicProcessInstanceQuery.processInstanceIds(procInstIdList);
                }
            }
        }

        // 标题
        if(StringUtils.isNotEmpty(query.getFormTitle())) {
            historicProcessInstanceQuery.variableValueLikeIgnoreCase(ProcessConstants.BPM_FORM_TITLE, "%" + query.getFormTitle() + "%");
        }

        page.setTotal(historicProcessInstanceQuery.count());

        List<HistoricProcessInstance> historicProcessInstances = historicProcessInstanceQuery.listPage(query.getPage() * query.getSize(), query.getSize());
        List<FlowTaskDto> flowList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(historicProcessInstances)) {
            Set<String> processDefinitionIds = historicProcessInstances.stream().map(HistoricProcessInstance::getProcessDefinitionId).collect(Collectors.toSet());
            Map<String, List<ProcessDefinition>> pdMap = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionIds(processDefinitionIds)
                    .list()
                    .stream().collect(Collectors.groupingBy(ProcessDefinition::getId));

            List<String> processIds = historicProcessInstances.stream().map(HistoricProcessInstance::getId).collect(Collectors.toList());
            Map<String, List<Task>> taskMap = taskService.createTaskQuery()
                    .processInstanceIdIn(processIds)
                    .list()
                    .stream().collect(Collectors.groupingBy(Task::getProcessInstanceId, Collectors.toList()));

            for(HistoricProcessInstance hisIns : historicProcessInstances) {
                Map<String, Object> variables = hisIns.getProcessVariables();
                FlowTaskDto flowTask = new FlowTaskDto();
                flowTask.setCreateTime(hisIns.getStartTime());
                flowTask.setFinishTime(hisIns.getEndTime());
                flowTask.setProcInsId(hisIns.getId());
                if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_TITLE))) {
                    flowTask.setFormTitle(String.valueOf(variables.get(ProcessConstants.BPM_FORM_TITLE)));
                }

                // 流程状态
                if(Objects.nonNull(hisIns.getEndTime())) {
                    String procStatus = hisIns.getBusinessStatus();
                    if(StringUtils.isNotEmpty(procStatus)) {
                        ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(procStatus);
                        flowTask.setProcStatus(procStatusEnum);
                    }else {
                        flowTask.setProcStatus(ProcStatusEnum.TG);
                    }
                }else {
                    flowTask.setProcStatus(ProcStatusEnum.INPROGRESS);
                }

                if(StringUtils.isNotEmpty(hisIns.getStartUserId())) {
                    UserDto startUser = sysUserService.findById(Long.parseLong(hisIns.getStartUserId()));
                    FlowableUtils.setStartUserInfo(flowTask, startUser);
                }

                // 计算耗时
                if(Objects.nonNull(hisIns.getEndTime())) {
                    long time = hisIns.getEndTime().getTime() - hisIns.getStartTime().getTime();
                    flowTask.setDuration(FlowableUtils.getDate(time));
                }else {
                    long time = System.currentTimeMillis() - hisIns.getStartTime().getTime();
                    flowTask.setDuration(FlowableUtils.getDate(time));
                }

                // 流程定义信息
                ProcessDefinition pd = pdMap.get(hisIns.getProcessDefinitionId()).get(0);
                flowTask.setDeployId(pd.getDeploymentId());
                flowTask.setProcDefName(pd.getName());
                flowTask.setProcDefVersion(pd.getVersion());
                flowTask.setCategory(pd.getCategory());
                flowTask.setProcDefVersion(pd.getVersion());
                flowTask.setProcDefId(hisIns.getProcessDefinitionId());

                // 当前所处流程
                List<Task> tasks = taskMap.get(hisIns.getId());
                if(!CollectionUtils.isEmpty(tasks)) {
                    for(Task task : tasks) {
                        flowTask.setTaskId(task.getId());
                        UserDto sysUser = sysUserService.findByName(task.getAssignee());
                        if(Objects.isNull(sysUser)) {
                            log.error("queryProcess 找不到该用户 " + task.getAssignee() + " 任务id：" + task.getId());
                        }
                        FlowTaskUserDto flowTaskUserDto = null;
                        if(Objects.nonNull(flowTask.getTaskName())) {
                            if(!flowTask.getTaskName().contains(task.getName())) {
                                flowTask.setTaskName(flowTask.getTaskName() + "," + task.getName());
                            }
                            flowTaskUserDto = flowTask.getAssigneeInfo();
                            if(Objects.nonNull(flowTaskUserDto.getAssigneeName())) {
                                if(Objects.nonNull(sysUser) && !flowTaskUserDto.getAssigneeName().contains(sysUser.getNickName())) {
                                    flowTaskUserDto.setAssigneeName(flowTaskUserDto.getAssigneeName() + "," + sysUser.getNickName());
                                }
                            }else {
                                log.error("queryProcess 任务节点没有处理人" + pd.getName() + " 任务id：" + task.getId());
                            }
                        }else {
                            flowTask.setTaskName(task.getName());
                            flowTaskUserDto = new FlowTaskUserDto();
                            if(Objects.nonNull(sysUser)) {
                                flowTaskUserDto.setAssigneeName(sysUser.getNickName());
                            }
                            flowTask.setAssigneeInfo(flowTaskUserDto);
                        }
                    }
                }
                flowList.add(flowTask);
            }
        }
        page.setRecords(flowList);
        return page;
    }

    /**
     * 流程历史流转记录
     * @param procInsId 流程实例Id
     * @return
     */
    @Override
    @Transactional
    public ResultJson flowRecord(String procInsId) {
        if(StringUtils.isBlank(procInsId)) {
            return ResultJson.generateResult(ResultCodeEnum.NULL_ARGUMENT.getMessageCN(), ResultCodeEnum.NULL_ARGUMENT.getCode());
        }

        FlowTaskVariablesDto.FlowTaskVariablesDtoBuilder map = FlowTaskVariablesDto.builder();

        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().includeProcessVariables().processInstanceId(procInsId).singleResult();

        // 归档数据处理
        if(Objects.nonNull(historicProcessInstance)) {
            List<HistoricActivityInstance> histList = historyService
                    .createHistoricActivityInstanceQuery()
                    .processInstanceId(procInsId)
                    .orderByHistoricActivityInstanceStartTime()
                    .asc()
                    .list();

            Map<String, Object> variables = historicProcessInstance.getProcessVariables();

            // 获取意见内容列表
            Map<String, List<Comment>> commentMap = taskService.getProcessInstanceComments(procInsId).stream().collect(Collectors.groupingBy(Comment::getTaskId));

            // 阅读记录
            List<BpmTaskUserRead> bpmTaskUserReadList = bpmTaskUserReadRepository.findAllByProcessInstanceId(procInsId);
            Map<String, List<BpmTaskUserRead>> bpmTaskUserReadMap = bpmTaskUserReadList.stream().collect(Collectors.groupingBy(BpmTaskUserRead::getTaskId, Collectors.toList()));

            // 附件
            List<Attachment> procAttachments = taskService.getProcessInstanceAttachments(procInsId);

            List<FlowTaskDto> hisFlowList = new ArrayList<>();
            List<FlowTaskCopyToDto> copyToData = new ArrayList<>();
            List<BpmTaskUserRead> taskUserReadList = null;

            for(HistoricActivityInstance histActIns : histList) {
                if(StringUtils.isNotBlank(histActIns.getTaskId()) && (StringUtils.isEmpty(histActIns.getDeleteReason()) || histActIns.getDeleteReason().indexOf(ProcessConstants.DELETE_REASON_CHANGE) != -1
                        || !CollectionUtils.isEmpty(commentMap.get(histActIns.getTaskId())))) {
                    FlowTaskDto flowTask = new FlowTaskDto();
                    flowTask.setTaskId(histActIns.getTaskId());
                    flowTask.setTaskName(histActIns.getActivityName());
                    flowTask.setCreateTime(histActIns.getStartTime());
                    flowTask.setFinishTime(histActIns.getEndTime());
                    flowTask.setTaskDefKey(histActIns.getActivityId());
                    flowTask.setProcDefId(histActIns.getProcessDefinitionId());
                    if(StringUtils.isNotBlank(histActIns.getAssignee())) {
                        UserDto sysUser = sysUserService.findByName(histActIns.getAssignee());
                        FlowableUtils.setAssigneeUserInfo(flowTask, sysUser);
                    }

                    // 接收用户
                    Object toAssignees = null;
                    try {
                        if(histActIns.getActivityType().equals(BpmnXMLConstants.ELEMENT_TASK_USER)) {
                            toAssignees = variables.get(ProcessConstants.BPM_TASK_TO_ASSIGNEES + ElAdminConstant.SEPARATOR_UNDERLINE + histActIns.getTaskId());
                        }
                    }catch(Exception e) {
                        log.warn("找不到接收用户：" + e.getMessage());
                    }
                    if(Objects.nonNull(toAssignees)) {
                        List<String> toTaskAssignees = FlowableUtils.parseNextTaskAssignee(toAssignees.toString());
                        StringBuilder stringBuilder = new StringBuilder();
                        for(String assignee : toTaskAssignees) {
                            UserDto sysUser = sysUserService.findByName(assignee);
                            if(Objects.nonNull(sysUser)) {
                                stringBuilder.append(sysUser.getNickName()).append(",");
                            }
                        }
                        if(stringBuilder.length() > 0) {
                            flowTask.setCandidate(stringBuilder.substring(0, stringBuilder.length() - 1));
                        }
                    }

                    // 阅读时间设置
                    taskUserReadList = bpmTaskUserReadMap.get(histActIns.getTaskId());
                    if(CollUtil.isNotEmpty(taskUserReadList)) {
                        taskUserReadList = taskUserReadList.stream().filter(taskUserRead -> taskUserRead.getAssignee().equals(histActIns.getAssignee())).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(taskUserReadList)) {
                            flowTask.setReadTime(taskUserReadList.get(0).getFirstReadTime());
                        }
                    }

                    flowTask.setDuration(histActIns.getDurationInMillis() == null || histActIns.getDurationInMillis() == 0 ? null : FlowableUtils.getDate(histActIns.getDurationInMillis()));

                    // 设置意见内容
                    List<Comment> commentList = commentMap.get(histActIns.getTaskId());
                    if(!CollectionUtils.isEmpty(commentList)) {
                        commentList.forEach(comment -> {
                            if(comment.getType() != ProcessConstants.PROCESS_COMMENT_TYPE_EVENT) {
                                String type = TaskCommentTypeEnum.getEnumInfoByType(comment.getType());
                                flowTask.setComment(FlowCommentDto.builder().type(type).comment(comment.getFullMessage()).build());
                            }
                        });
                    }

                    if(CollUtil.isNotEmpty(procAttachments)) {
                        List<Attachment> taskAttachments = procAttachments.stream().filter(attachment -> attachment.getTaskId().equals(histActIns.getTaskId())).collect(Collectors.toList());
                        taskAttachments.forEach(attachment -> {
                            // 手写签名
                            if(attachment.getType().equals(AttachmentTypeEnum.SXQM.name())) {
                                flowTask.setSignature(restUrlComponent.getServerUrl() + attachment.getUrl());
                            }
                        });
                    }

                    hisFlowList.add(flowTask);
                }else if(histActIns.getActivityType().equals(BpmnXMLConstants.ELEMENT_TASK_SERVICE) && (StringUtils.isEmpty(histActIns.getDeleteReason()) || histActIns.getDeleteReason().indexOf(ProcessConstants.DELETE_REASON_CHANGE) != -1)) {

                    // 增加抄送人
                    Object copyToAssignee = variables.get(ProcessConstants.BPM_BPM_COPYTO_PREFIX + histActIns.getActivityId());
                    if(Objects.nonNull(copyToAssignee) && StringUtils.isNotBlank(copyToAssignee.toString())) {
                        List<FlowTaskUserDto> taskUserDtos = Arrays.stream(copyToAssignee.toString().split(ElAdminConstant.SEPARATOR_COMMA)).filter(username -> StringUtils.isNotEmpty(username)).map(username -> {
                            UserDto sysUser = sysUserService.findByName(username);
                            if(Objects.nonNull(sysUser)) {
                                FlowTaskUserDto flowTaskUserDto = FlowTaskUserDto.builder()
                                        .assigneeId(sysUser.getId())
                                        .assigneeUsername(sysUser.getUsername())
                                        .assigneeName(sysUser.getNickName())
                                        .deptName(sysUser.getDept().getName())
                                        .build();
                                return flowTaskUserDto;
                            }else {
                                log.error("flowRecord 抄送人sysUser is null, username: " + username);
                                return null;
                            }
                        }).collect(Collectors.toList());

                        FlowTaskCopyToDto flowTaskCopyToDto = FlowTaskCopyToDto.builder()
                                .taskName(histActIns.getActivityName())
                                .taskDefKey(histActIns.getActivityId())
                                .createTime(histActIns.getStartTime())
                                .finishTime(histActIns.getEndTime())
                                .procInsId(histActIns.getProcessInstanceId())
                                .taskUserDtos(taskUserDtos)
                                .build();
                        copyToData.add(flowTaskCopyToDto);
                    }
                }
            }

            // 处理单数据 bpmData
            map.bpmData(hisFlowList);
            map.copyToData(copyToData);

            // 表单模版
            DeployFormVo sysForm = sysInstanceFormService.selectWorkflowDeployFormByDeployId(historicProcessInstance.getDeploymentId());
            if(Objects.nonNull(sysForm)) {
                Map<String, String> formRwhMap = new HashMap<>();
                formRwhMap.put(ProcessConstants.BPM_RWH_DEFAULT, "R");
                // PC端服务端处理读写状态
                JSONObject formObject = JSON.parseObject(sysForm.getFormContent());
                // 处理流转表单读写属性
                formTemplateService.formRwHandle(formObject.getJSONArray("list"), formRwhMap);
                map.formJson(JSON.toJSONString(formObject));
                if(StringUtils.isNotEmpty(sysForm.getMobileFormContent())) {
                    map.mobileFormJson(sysForm.getMobileFormContent());
                }
            }

            // 表单数据
            if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_DATA))) {
                map.formData(variables.get(ProcessConstants.BPM_FORM_DATA));
            }

            // 表单样式
            if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_SKIN))) {
                map.formSkin(variables.get(ProcessConstants.BPM_FORM_SKIN).toString());
            }
            if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_PC_SKIN))) {
                map.formPcSkin(variables.get(ProcessConstants.BPM_FORM_PC_SKIN).toString());
            }
            if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID))) {
                map.formTemplateId(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()));
            }
            if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_MOBILE_TEMPLATE_ID))) {
                map.mobileFormTemplateId(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_MOBILE_TEMPLATE_ID).toString()));
            }

            // 流程发起人信息
            UserDto startUser = null;
            if(StringUtils.isNotEmpty(historicProcessInstance.getStartUserId())) {
                startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
                FlowTaskApplicantDto flowTaskApplicantDto = FlowTaskApplicantDto.builder()
                        .startNickName(startUser.getNickName())
                        .startUsername(startUser.getUsername())
                        .startDeptName(startUser.getDept().getName())
                        .startTime(historicProcessInstance.getStartTime()
                        )
                        .build();
                map.applicantInfo(flowTaskApplicantDto);
            }

            // 流程状态
            if(Objects.nonNull(historicProcessInstance.getEndTime())) {
                String procStatus = historicProcessInstance.getBusinessStatus();
                if(StringUtils.isNotEmpty(procStatus)) {
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(procStatus);
                    map.procStatus(procStatusEnum);
                }else {
                    map.procStatus(ProcStatusEnum.TG);
                }
            }else {
                map.procStatus(ProcStatusEnum.INPROGRESS);
            }

            String currentUsername = SecurityUtils.getCurrentUsername();

            // 阅读状态
            TaskUserReadVo taskUserReadVo = TaskUserReadVo.builder()
                    .processInstanceId(procInsId)
                    .assignee(currentUsername)
                    .build();
            map.userHasRead(this.userHasRead(taskUserReadVo));

            // 预签信息
            Object preSignAssigneeVar = variables.get(ProcessConstants.BPM_BPM_PRESIGNASSIGNEE);
            if(Objects.nonNull(preSignAssigneeVar)) {
                Map<String, String> preSignAssigneeMap = FlowableUtils.formatPreSignAssignee(preSignAssigneeVar.toString());
                List<FlowNextPreSignTaskDto> nextPreSignData = new ArrayList<>();
                preSignAssigneeMap.forEach((key, value) -> {
                    List<String> userList = processELService.getResolveUsers(value, key);
                    List<SimpleUserDto> simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(userList);
                    StringBuilder userNames = new StringBuilder();
                    for(SimpleUserDto simpleUserDto : simpleUserDtoList) {
                        userNames.append(simpleUserDto.getNickName()).append(ElAdminConstant.SEPARATOR_COMMA);
                    }
                    if(userNames.length() > 0) {
                        userNames = userNames.deleteCharAt(userNames.length() - 1);
                    }
                    FlowNextPreSignTaskDto flowNextPreSignTaskDto = FlowNextPreSignTaskDto.builder()
                            .taskDefKey(key)
                            .userNames(userNames.toString())
                            .build();
                    nextPreSignData.add(flowNextPreSignTaskDto);
                });
                map.nextPreSignData(nextPreSignData);
            }

            // 发送待办已阅
            try {
                workflowCustomExtensionService.readBacklog(procInsId, currentUsername);

                // 发送抄送已办
                if(Objects.nonNull(startUser) && workflowCustomExtensionService.checkCopyTo(procInsId, currentUsername)) {
                    String mobileUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/page?processInstanceId=" + procInsId;
                    String pcUrl = restUrlComponent.getServerUrl() + "/api/bpm/handle/webPage?processInstanceId=" + procInsId;
                    List alreadyUserListBO = new ArrayList();
                    AlreadyUserListBO alreadyUserDto = AlreadyUserListBO.builder().transactionTime(Instant.now().getEpochSecond()).userId(currentUsername).build();
                    alreadyUserListBO.add(alreadyUserDto);
                    flowTaskService.sendBacklogList(null, variables.get(ProcessConstants.BPM_FORM_DATA).toString(), null, procInsId, startUser.getUsername(), mobileUrl, pcUrl, alreadyUserListBO);
                }
            }catch(Exception e) {
                e.printStackTrace();
                log.error("发送待办已阅失败" + e.getMessage());
            }

            // 流程相关信息
            map.processName(historicProcessInstance.getProcessDefinitionName())
                    .processVersion(historicProcessInstance.getProcessDefinitionVersion());
        }else {
            this.HandleOldRecord(map, procInsId);
        }

        if(Objects.isNull(map.build().getBpmData()) && Objects.isNull(map.build().getFormData())) {
            return ResultJson.generateResult("该流程不存在或已删除", map.build(), ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }else {
            return ResultJson.generateResult(map.build());
        }
    }

    // 查询阅读状态
    private Boolean userHasRead(TaskUserReadVo taskUserReadVo) {
        if(StringUtils.isBlank(taskUserReadVo.getAssignee())) {
            taskUserReadVo.setAssignee(SecurityUtils.getCurrentUsername());
        }
        BpmTaskUserRead bpmTaskUserRead = bpmTaskUserReadRepository.findFirstByProcessInstanceIdAndAssignee(taskUserReadVo.getProcessInstanceId(), taskUserReadVo.getAssignee());
        if(Objects.isNull(bpmTaskUserRead)) {
            return null;
        }
        if(Objects.nonNull(bpmTaskUserRead.getFirstReadTime())) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public void saveHasRead(TaskUserReadVo taskUserReadVo) {
        if(StringUtils.isBlank(taskUserReadVo.getAssignee())) {
            taskUserReadVo.setAssignee(SecurityUtils.getCurrentUsername());
        }
        BpmTaskUserRead bpmTaskUserRead = bpmTaskUserReadRepository.findFirstByProcessInstanceIdAndAssignee(taskUserReadVo.getProcessInstanceId(), taskUserReadVo.getAssignee());
        if(Objects.isNull(bpmTaskUserRead)) {
            return;
        }
        Date readTime = new Date();
        if(Objects.isNull(bpmTaskUserRead.getFirstReadTime())) {
            bpmTaskUserRead.setFirstReadTime(readTime);
        }
        bpmTaskUserRead.setLastReadTime(readTime);
        bpmTaskUserRead.setRemark(taskUserReadVo.getRemark());
        bpmTaskUserReadRepository.save(bpmTaskUserRead);

        if(Objects.isNull(bpmTaskUserRead.getTaskId())) {
            workflowCustomExtensionService.updateBpmReadTask(Long.parseLong(bpmTaskUserRead.getProcessInstanceId()), bpmTaskUserRead.getId(), 1);
        }
    }

    /**
     * 处理老OA数据展示
     * @param map
     * @param procInsId
     */
    private void HandleOldRecord(FlowTaskVariablesDto.FlowTaskVariablesDtoBuilder map, String procInsId) {

        ProcessArchive processArchive = this.getByProcInstanceId(procInsId);
        if(Objects.isNull(processArchive)) {
            log.error(String.format("HandleOldRecord 处理老OA数据展示错误，没有该数据 procInstId：%s, username: %s", procInsId, SecurityUtils.getCurrentUsername()));
            return;
        }

        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(processArchive.getProcDefKey()).latestVersion().singleResult();
        DeployFormVo sysForm = sysInstanceFormService.selectWorkflowDeployFormByDeployId(definition.getDeploymentId());
        if(Objects.nonNull(sysForm)) {
            map.formJson(sysForm.getFormContent());
            map.mobileFormJson(sysForm.getMobileFormContent());
        }
        // 表单样式
        map.formPcSkin("1");
        map.formSkin(processArchive.getProcDefKey());

        // 流程发起人信息
        if(StringUtils.isNotEmpty(processArchive.getUsername())) {
            UserDto startUser = sysUserService.findByName(processArchive.getUsername());
            if(Objects.nonNull(startUser)) {
                FlowTaskApplicantDto flowTaskApplicantDto = FlowTaskApplicantDto.builder()
                        .startNickName(startUser.getNickName())
                        .startUsername(startUser.getUsername())
                        .startDeptName(startUser.getDept().getName())
                        .startTime(processArchive.getStartTime()
                        )
                        .build();
                map.applicantInfo(flowTaskApplicantDto);
            }
        }

        // 获取意见内容列表
        List<ProcessCommentArchive> commentList = this.findByProcInstanceId(procInsId);

        List<FlowTaskDto> hisFlowList = new ArrayList<>();
        for(ProcessCommentArchive processComment : commentList) {
            FlowTaskDto flowTask = new FlowTaskDto();
            flowTask.setTaskId(processComment.getTaskId());
            flowTask.setTaskName(processComment.getTaskName());
            flowTask.setCreateTime(processComment.getCreateDate());
            flowTask.setFinishTime(processComment.getTime());
            if(StringUtils.isNotBlank(processComment.getUsername())) {
                UserDto sysUser = sysUserService.findByName(processComment.getUsername());
                if(sysUser == null) {
                    FlowTaskUserDto flowTaskUserDto = FlowTaskUserDto.builder()
                            .assigneeName(processComment.getUsername())
                            .build();
                    flowTask.setAssigneeInfo(flowTaskUserDto);
                }else {
                    FlowableUtils.setAssigneeUserInfo(flowTask, sysUser);
                }
            }

            // 设置意见内容
            flowTask.setComment(FlowCommentDto.builder().type(processComment.getType()).comment(processComment.getComment()).build());
            hisFlowList.add(flowTask);
        }

        // 处理单数据 bpmData
        map.bpmData(hisFlowList);

        // 表单数据
        Object formData = flowFormHandleFactory.handleFormRecord(processArchive.getProcDefKey(), procInsId, null, null);
        if(Objects.nonNull(formData)) {
            map.formData(formData);
        }

        // 流程状态
        map.procStatus(ProcStatusEnum.END);
    }

    /**
     * 撤回流程
     * @param flowTaskVo
     * @return
     */
    @Override
    public ResultJson revokeProcess(FlowTaskVo flowTaskVo) {
        String processInstanceId = flowTaskVo.getBpmData().getProcessInstanceId();
        if(StringUtils.isEmpty(processInstanceId)) {
            return ResultJson.generateResult(ResultCodeEnum.NULL_ARGUMENT.getMessageCN(), ResultCodeEnum.NULL_ARGUMENT.getCode());
        }

        List<Task> runTaskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if(CollectionUtils.isEmpty(runTaskList)) {
            return ResultJson.generateResult("流程未启动或已执行完成，无法撤回", ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }

        String username = SecurityUtils.getCurrentUsername();
        List<HistoricTaskInstance> htiList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .taskAssignee(username)
                .finished()
                .includeProcessVariables()
                .orderByTaskCreateTime()
                .desc()
                .list();

        // 原任务 task
        HistoricTaskInstance myTask = null;
        if(!CollectionUtils.isEmpty(htiList)) {
            myTask = htiList.get(0);
        }

        // 任务没有该用户提交
        if(Objects.isNull(myTask)) {
            throw new CustomException("该任务非当前用户提交，无法撤回");
        }

        // 获取所有节点信息
        Process process = repositoryService.getBpmnModel(myTask.getProcessDefinitionId()).getProcess(null);

        // 获取跳转的节点元素
        FlowElement target = process.getFlowElement(myTask.getTaskDefinitionKey());

        // 获取所有正常进行的任务节点 Key，这些任务不能直接使用，需要找出其中需要撤回的任务
        List<String> runTaskKeyList = new ArrayList<>();
        runTaskList.forEach(item -> runTaskKeyList.add(item.getTaskDefinitionKey()));

        if(Sets.newHashSet(runTaskKeyList).size() > 1) {
            return ResultJson.generateResult("暂不支持撤回后续有多处理节点任务", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }

        // 通过父级网关的出口连线，结合 runTaskList 比对，获取需要撤回的任务
        List<UserTask> currentUserTaskList = FlowableUtils.findOutgoingUserTasks(target, null, null);

        // 需退回任务列表，如果改节点有多人并行处理，撤回自己，修改节点状态
        List<String> currentIds = new ArrayList<>();
        currentUserTaskList.forEach(item -> currentIds.add(item.getId()));

        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .taskDefinitionKeys(currentIds)
                .taskCreatedAfter(myTask.getCreateTime())
                .finished().list();
        if(!currentIds.containsAll(runTaskKeyList) || !CollectionUtils.isEmpty(historicTaskInstanceList)) {
            return ResultJson.generateResult("后续节点已有完成审批或已撤回，无法撤回", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }

        Map variables = myTask.getProcessVariables();

        // 撤回流程意见及业务表上表单意见
        UserDto userDto = sysUserService.findByName(username);

        // 意见上表单
        String remarkToFormField = target.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
        if(StringUtils.isNotBlank(remarkToFormField)) {
            FlowFormRemarkDto flowFormRemarkDto = FlowFormRemarkDto.builder()
                    .remark("")
                    .username(userDto.getUsername())
                    .nickName(userDto.getNickName())
                    .deptId(userDto.getDeptId())
                    .deptName(userDto.getDept().getName())
                    .processedTime(new Date())
                    .fj(flowTaskVo.getBpmData().getRemarkAttachment())
                    .build();

            String processRemarkSort = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_REMARK_SORT);
            boolean insertBefore = false;
            if(StringUtils.isNotBlank(processRemarkSort)) {
                insertBefore = Boolean.parseBoolean(processRemarkSort);
            }

            try {
                ResponseInfo responseInfo = formTemplateService.deleteFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), myTask.getProcessInstanceId(), remarkToFormField, flowFormRemarkDto, insertBefore);
                if(ResultCodeEnum.SUCCESS.getCode() == responseInfo.getCode()) {
                    // 更新意见上表单数据
                    JSONObject formData = (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA);
                    JSONObject jsonObject = (JSONObject) responseInfo.getData();
                    List<String> remarkToFormFieldList = FlowableUtils.findAllRemarkToFormField(process);
                    for(String key : remarkToFormFieldList) {
                        if(jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
                            formData.put(key, jsonObject.get(key));
                        }
                    }
                    variables.replace(ProcessConstants.BPM_FORM_DATA, formData);
                }
            }catch(Exception e) {
                throw new CustomException("更新业务表单数据错误");
            }
        }
        taskService.deleteComments(myTask.getId(), null);


        // 循环获取那些需要被撤回的节点的ID，用来设置撤回原因
        List<String> deleteIds = new ArrayList<>();
        runTaskList.forEach(runTask -> {
            deleteIds.add(runTask.getId());
            taskService.addComment(runTask.getId(), processInstanceId, TaskCommentTypeEnum.CH.name(), TaskCommentTypeEnum.CH.getName());
        });

        variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, username);

        try {
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(myTask.getProcessInstanceId())
                    .processVariables(variables)
                    .moveActivityIdsToSingleActivityId(runTaskKeyList, target.getId())
                    .changeState();

            // 清除运行任务节点
            taskService.deleteTasks(deleteIds, true);
            for(Task task : runTaskList) {
                historyService.createHistoricActivityInstanceQuery().processInstanceId(task.getProcessInstanceId()).activityId(task.getTaskDefinitionKey()).executionId(task.getExecutionId()).deleteWithRelatedData();
            }
            // 清除原来任务节点
            historyService.deleteHistoricTaskInstance(myTask.getId());
            historyService.createHistoricActivityInstanceQuery().processInstanceId(myTask.getProcessInstanceId()).activityId(myTask.getTaskDefinitionKey()).executionId(myTask.getExecutionId()).deleteWithRelatedData();

            // TODO: 清除已发待办消息，目前转已办
        }catch(FlowableException e) {
            return ResultJson.generateResult("撤回失败", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }
        return ResultJson.generateResult("撤回成功!");
    }

    @Override
    public ResultJson findReactivateTaskList(String procInstanceId) {
        HistoricProcessInstance hisProcInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInstanceId)
                .singleResult();
        if(Objects.isNull(hisProcInstance)) {
            throw new CustomException("该流程不存在不能重新恢复");
        }
        UserDto startUser = sysUserService.findById(Long.parseLong(hisProcInstance.getStartUserId()));

        // 获取所有用户节点信息
        BpmnModel bpmnModel = repositoryService.getBpmnModel(hisProcInstance.getProcessDefinitionId());
        Process process = bpmnModel.getProcess(null);
        Collection<UserTask> userTaskList = FlowableUtils.getAllUserTaskElements(process.getFlowElements(), null);

        // 获取任务执行
        String executionId = null;
        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(procInstanceId).list();
        if(!CollectionUtils.isEmpty(historicTaskInstanceList)) {
            executionId = historicTaskInstanceList.get(0).getExecutionId();
        }

        // 处理单
        FlowTaskProcessDto.FlowTaskProcessDtoBuilder flowTaskProcessDtoBuilder = FlowTaskProcessDto.builder()
                .processInstanceId(hisProcInstance.getId())
                .procDefKey(hisProcInstance.getProcessDefinitionKey())
                .procDefId(hisProcInstance.getProcessDefinitionId());

        if(!CollectionUtils.isEmpty(userTaskList)) {
            List<FlowNextDto> allFlowNextDtos = new ArrayList<>();
            for(UserTask userTask : userTaskList) {

                // 当前节点可选人员信息在流程任务定义里面定义
                FlowNextDto flowNextDto = flowTaskService.currentUserTaskHandle(bpmnModel, userTask.getId(), startUser, executionId);
                if(Objects.nonNull(flowNextDto)) {
                    allFlowNextDtos.add(flowNextDto);
                }
            }

            flowTaskProcessDtoBuilder.userTaskList(allFlowNextDtos);
        }
        return ResultJson.generateResult(flowTaskProcessDtoBuilder.build());
    }

    @Log("重新恢复流程")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson reactivateProcess(FlowTaskVo flowTaskVo) throws Exception {
        String nextTaskNodes = flowTaskVo.getBpmData().taskNodesToString();
        if(StringUtils.isEmpty(nextTaskNodes)) {
            return ResultJson.generateResult("任务处理人不能为空", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }
        if(nextTaskNodes.split(ElAdminConstant.SEPARATOR_COMMA).length > 1) {
            return ResultJson.generateResult("重新恢复流程暂不支持选择多个任务处理", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }

        String procInstId = flowTaskVo.getBpmData().getProcessInstanceId();
        if(StringUtils.isEmpty(procInstId)) {
            return ResultJson.generateResult(ResultCodeEnum.ILLEGAL_ARGUMENT.getMessageCN(), ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).includeProcessVariables().singleResult();
        if(Objects.isNull(historicProcessInstance)) {
            return ResultJson.generateResult(ResultCodeEnum.ILLEGAL_ARGUMENT.getMessageCN(), ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }

        ResultJson resultJson = flowDefinitionService.startProcessInstanceByKey(historicProcessInstance.getProcessDefinitionKey(), Long.parseLong(historicProcessInstance.getStartUserId()));
        Map<String, Object> newResult = (Map) resultJson.getData();
        String newProcInstId = newResult.get("processInstanceId").toString();
        String newTaskId = newResult.get("taskId").toString();
        flowTaskVo.getBpmData().setTaskId(newTaskId);

        Map variables = historicProcessInstance.getProcessVariables();

        // 删除旧的待办人节点及以后信息
        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
        Process process = bpmnModel.getProcess(null);
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(nextTaskNodes);
        List<FlowElement> userTaskList = FlowableUtils.iteratorFindOutgoingFlowElements(flowNode, null, null);

        List<String> taskDefKeys = userTaskList.stream().map(FlowElement::getId).collect(Collectors.toList());
        taskDefKeys.add(nextTaskNodes);
        FlowElement startFlowElement = FlowableUtils.findStartFlowElement(process.getFlowElements());
        taskDefKeys.add(startFlowElement.getId());

        // 获取旧的节点
        List<HistoricTaskInstance> currentTaskList = historyService.createHistoricTaskInstanceQuery().processInstanceId(procInstId).taskDefinitionKey(nextTaskNodes).orderByTaskCreateTime().desc().list();
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(procInstId).taskDefinitionKeys(taskDefKeys);
        if(!CollectionUtils.isEmpty(currentTaskList)) {
            historicTaskInstanceQuery = historicTaskInstanceQuery.taskCreatedAfter(DateUtil.offsetMillisecond(currentTaskList.get(0).getCreateTime(), -1));
        }
        List<HistoricTaskInstance> historicTaskInstanceList = historicTaskInstanceQuery.list();
        for(HistoricTaskInstance hisTask : historicTaskInstanceList) {
            // 撤回流程意见及业务表上表单意见
            UserDto userDto = sysUserService.findByName(hisTask.getAssignee());

            // 意见上表单
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(hisTask.getTaskDefinitionKey());
            String remarkToFormField = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
            if(StringUtils.isNotBlank(remarkToFormField)) {
                FlowFormRemarkDto flowFormRemarkDto = FlowFormRemarkDto.builder()
                        .remark("")
                        .username(userDto.getUsername())
                        .nickName(userDto.getNickName())
                        .deptId(userDto.getDeptId())
                        .deptName(userDto.getDept().getName())
                        .processedTime(new Date())
                        .fj(flowTaskVo.getBpmData().getRemarkAttachment())
                        .build();

                String processRemarkSort = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_REMARK_SORT);
                boolean insertBefore = false;
                if(StringUtils.isNotBlank(processRemarkSort)) {
                    insertBefore = Boolean.parseBoolean(processRemarkSort);
                }

                try {
                    ResponseInfo responseInfo = formTemplateService.deleteFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), procInstId, remarkToFormField, flowFormRemarkDto, insertBefore);
                    if(ResultCodeEnum.SUCCESS.getCode() == responseInfo.getCode()) {
                        // 更新意见上表单数据
                        JSONObject formData = (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA);
                        JSONObject jsonObject = (JSONObject) responseInfo.getData();
                        List<String> remarkToFormFieldList = FlowableUtils.findAllRemarkToFormField(process);
                        for(String key : remarkToFormFieldList) {
                            if(jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
                                formData.put(key, jsonObject.get(key));
                            }
                        }
                        variables.replace(ProcessConstants.BPM_FORM_DATA, formData);
                    }
                }catch(Exception e) {
                    throw new CustomException("更新业务表单数据错误");
                }
            }
            taskService.deleteComments(hisTask.getId(), null);
            historyService.deleteHistoricTaskInstance(hisTask.getId());
            historyService.createHistoricActivityInstanceQuery().processInstanceId(hisTask.getProcessInstanceId()).activityId(hisTask.getTaskDefinitionKey()).executionId(hisTask.getExecutionId()).deleteWithRelatedData();
        }

        // 删除旧的历史流转记录
        HistoricActivityInstanceQuery historicActivityInstanceQuery = historyService.createHistoricActivityInstanceQuery().processInstanceId(procInstId);
        if(!CollectionUtils.isEmpty(currentTaskList)) {
            historicActivityInstanceQuery = historicActivityInstanceQuery.startedAfter(DateUtil.offsetMillisecond(currentTaskList.get(0).getCreateTime(), -1));
        }
        List<HistoricActivityInstance> historicActivityInstanceList = historicActivityInstanceQuery.list().stream().filter(t -> taskDefKeys.contains(t.getActivityId())).collect(Collectors.toList());
        for(HistoricActivityInstance hisActivity : historicActivityInstanceList) {
            historyService.createHistoricActivityInstanceQuery().processInstanceId(hisActivity.getProcessInstanceId()).activityId(hisActivity.getActivityId()).executionId(hisActivity.getExecutionId()).deleteWithRelatedData();
        }

        // 处理旧的待办信息
        shortLinkService.updateLongUrl(procInstId, newProcInstId);
        ShortLink shortLink = new ShortLink();
        shortLink.setCode(procInstId);
        shortLink.setLongUrl(newProcInstId);
        shortLink.setRemark("重新恢复流程");
        shortLinkService.save(shortLink);

        // 重新关联业务表
        ResponseInfo processInfo = formTemplateService.updateFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), procInstId, "bpmInstanceId", newProcInstId, false, true);
        formTemplateService.updateFormStatus(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), newProcInstId, ProcStatusEnum.INPROGRESS);

        // 重新关联流程信息
        processArchiveRepository.updateActHiVarinstByProcInstId(procInstId, newProcInstId);
        processArchiveRepository.updateActHiActinstByProcInstId(procInstId, newProcInstId);
        processArchiveRepository.updateActHiCommentByProcInstId(procInstId, newProcInstId);

        // 创建待办
        variables.put(ProcessConstants.BPM_TASK_DEF_KEY, nextTaskNodes);
        List<String> activityIds = Lists.newArrayList(nextTaskNodes.split(ElAdminConstant.SEPARATOR_COMMA).clone());

        // 设置后续节点处理人
        String nextAssignee = flowTaskVo.getBpmData().taskAssigneeFormatToString();
        variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, nextAssignee);

        Task task = taskService.createTaskQuery().taskId(newTaskId).includeProcessVariables().singleResult();
        try {
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(newProcInstId)
                    .processVariables(variables)
                    .moveSingleExecutionToActivityIds(task.getExecutionId(), activityIds)
                    .changeState();
        }catch(FlowableException e) {
            log.error(e.getMessage());
            throw new CustomException("无法重新恢复，请联系管理员");
        }

        historyService.createHistoricActivityInstanceQuery().processInstanceId(task.getProcessInstanceId()).activityId(task.getTaskDefinitionKey()).executionId(task.getExecutionId()).deleteWithRelatedData();

        if(Objects.isNull(historicProcessInstance.getEndTime())) {
            runtimeService.deleteProcessInstance(procInstId, "重新恢复流程");
        }
        historyService.deleteHistoricProcessInstance(procInstId);

        return ResultJson.generateResult("重新恢复流程成功");
    }

    /**
     * 获取流程过程图
     * @param processId
     * @return
     */
    @Override
    public InputStream diagram(String processId) {
        String processDefinitionId;
        // 获取当前的流程实例
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processId).singleResult();
        // 如果流程已经结束，则得到结束节点
        if(Objects.isNull(processInstance)) {
            HistoricProcessInstance pi = historyService.createHistoricProcessInstanceQuery().processInstanceId(processId).singleResult();

            processDefinitionId = pi.getProcessDefinitionId();
        }else {// 如果流程没有结束，则取当前活动节点
            // 根据流程实例ID获得当前处于活动状态的ActivityId合集
            ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processId).singleResult();
            processDefinitionId = pi.getProcessDefinitionId();
        }

        // 获得活动的节点
        List<HistoricActivityInstance> highLightedFlowList = historyService.createHistoricActivityInstanceQuery().processInstanceId(processId).orderByHistoricActivityInstanceStartTime().asc().list();

        List<String> highLightedFlows = new ArrayList<>();
        List<String> highLightedNodes = new ArrayList<>();
        //高亮线
        for(HistoricActivityInstance tempActivity : highLightedFlowList) {
            if("sequenceFlow".equals(tempActivity.getActivityType())) {
                //高亮线
                highLightedFlows.add(tempActivity.getActivityId());
            }else {
                //高亮节点
                highLightedNodes.add(tempActivity.getActivityId());
            }
        }

        //获取流程图
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
        ProcessEngineConfiguration configuration = processEngine.getProcessEngineConfiguration();
        //获取自定义图片生成器
        ProcessDiagramGenerator diagramGenerator = new CustomProcessDiagramGenerator();
        InputStream in = diagramGenerator.generateDiagram(bpmnModel, "png", highLightedNodes, highLightedFlows, configuration.getActivityFontName(),
                configuration.getLabelFontName(), configuration.getAnnotationFontName(), configuration.getClassLoader(), 1.0, true);
        return in;
    }

    /**
     * 获取当前流程任务处理人
     * @param procInstanceId
     * @return
     */
    @Override
    public ResultJson getTaskAssignees(String procInstanceId) {
        // 获取任务
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(procInstanceId).list();
        if(CollectionUtils.isEmpty(taskList)) {
            throw new RuntimeException("当前任务节点不存在或该流程已结束");
        }

        // bpm定义模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(taskList.get(0).getProcessDefinitionId());

        Map<String, FlowNextDto> flowNodeVosMap = new HashMap<>();
        for(Task taskNode : taskList) {
            // 当前节点
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(taskNode.getTaskDefinitionKey());

            UserDto userDto = sysUserService.findByName(taskNode.getAssignee());
            SimpleUserDto simpleUserDto = SimpleUserDto.builder()
                    .id(userDto.getId())
                    .userName(taskNode.getAssignee())
                    .nickName(userDto.getNickName())
                    .taskId(taskNode.getId())
                    .build();

            String taskKey = taskNode.getTaskDefinitionKey();

            // 子流程
            FlowElementsContainer parentContainer = currentFlowNode.getParentContainer();
            if(Objects.nonNull(parentContainer) && parentContainer instanceof SubProcess) {
                taskKey = parentContainer.getId();
            }

            if(flowNodeVosMap.containsKey(taskKey)) {
                List<SimpleUserDto> candidateUser = flowNodeVosMap.get(taskKey).getCandidateUser();
                candidateUser.add(simpleUserDto);
            }else {
                List<SimpleUserDto> candidateUser = new ArrayList<>();
                candidateUser.add(simpleUserDto);

                String taskExecType = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_TASKEXECTYPE);

                FlowNextDto flowNodeVo = FlowNextDto.builder()
                        .taskName(taskNode.getName())
                        .taskDefKey(taskKey)
                        .taskExecType(taskExecType)
                        .candidateUser(candidateUser)
                        .build();
                flowNodeVosMap.put(taskKey, flowNodeVo);
            }
        }

        if(CollectionUtils.isEmpty(flowNodeVosMap.values())) {
            return ResultJson.generateResult("该任务不支持加减签操作", ResultCodeEnum.UN_SUPPORT_OPERATER.getCode());
        }
        return ResultJson.generateResult(new ArrayList(flowNodeVosMap.values()));
    }

    @Transactional
    @Override
    public ResultJson syncFormRemark(String procInstanceId, Boolean isAll) {

        //获取流程定义意见上表单字段
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstanceId).includeProcessVariables().singleResult();
        if(Objects.isNull(historicProcessInstance)) {
            return ResultJson.generateResult("未找到该流程", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }
        Map variables = historicProcessInstance.getProcessVariables();
        JSONObject processFormData = (JSONObject) variables.getOrDefault(ProcessConstants.BPM_FORM_DATA, new JSONObject());
        if(Objects.isNull(processFormData)) {
            processFormData = new JSONObject();
        }

        //获取业务表意见
        JSONObject formData = (JSONObject) flowFormHandleFactory.handleFormRecord(historicProcessInstance.getProcessDefinitionKey(), procInstanceId, null, null);
        if(Objects.nonNull(isAll) && isAll && Objects.nonNull(formData)) {
            for(String key: formData.keySet()) {
                Object value = formData.get(key);
                if(processFormData.containsKey(key)) {
                    if(value instanceof JSONObject) {
                        // 表单字段对象都不保存id信息
                        JSONObject jsonObject = (JSONObject) value;
                        jsonObject.remove("id");
                    }
                    // 格式匹配转换
                    Object destValue = processFormData.get(key);
                    if(Objects.nonNull(destValue) && Objects.nonNull(value) && destValue.getClass() != value.getClass()) {
                        processFormData.put(key, JSON.parseObject(JSON.toJSONString(value), destValue.getClass()));
                    }else {
                        processFormData.put(key, value);
                    }
                }else {
                    // 如果业务表结构新增
                    if(value instanceof JSONObject || value instanceof JSONArray) {
                        processFormData.put(key, JSON.parseObject(JSON.toJSONString(value), value.getClass()));
                    }else {
                        processFormData.put(key, value);
                    }
                }
            }
        }

        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
        Collection<UserTask> userTasks = FlowableUtils.getAllUserTaskElements(bpmnModel.getProcess(null).getFlowElements(), null);
        for(UserTask userTask : userTasks) {
            String remarkToFormField = userTask.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
            if(StringUtils.isNotBlank(remarkToFormField)) {
                Object remark = formData.get(remarkToFormField);
                processFormData.put(remarkToFormField, remark);
            }
        }

        //更新流程缓存
        if(Objects.isNull(historicProcessInstance.getEndTime())) {
            runtimeService.setVariable(procInstanceId, ProcessConstants.BPM_FORM_DATA, processFormData);
        }
        processArchiveRepository.updateHiActGeBytearrayByProcInstId(procInstanceId, processFormData);
        return ResultJson.generateResult("同步流程表单意见成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson deleteProcessForExpired() {
        StringBuffer message = new StringBuffer();
        List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().active().startedBefore(DateUtils.addDays(new Date(), -1)).variableNotExists(ProcessConstants.BPM_BPM_STARTEDBY).includeProcessVariables().list();
        if(!CollectionUtils.isEmpty(processInstanceList)) {
            AtomicInteger counter = new AtomicInteger();
            Iterator<ProcessInstance> instanceIterator = processInstanceList.listIterator();
            QueryFormBO queryFormBO = new QueryFormBO();
            while(instanceIterator.hasNext()) {
                ProcessInstance processInstance = instanceIterator.next();
                try {
                    queryFormBO.setProcessInstanceId(processInstance.getProcessInstanceId());
                    queryFormBO.setTemplateId(Long.parseLong(processInstance.getBusinessKey()));
                    ResponseEntity responseEntity = formTemplateService.getFormData(queryFormBO);
                    Object formData = processInstance.getProcessVariables().get(ProcessConstants.BPM_FORM_DATA);
                    // 暂存过，不删除
                    if(Objects.nonNull(responseEntity.getBody()) || Objects.nonNull(formData)) {
                        continue;
                    }
                }catch(Exception e) {
                    log.error("deleteProcessForExpired获取表单数据错误 " + e.getMessage());
                    continue;
                }

                // 上个节点是启动节点
                List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
                if(CollectionUtils.isEmpty(tasks)) {
                    continue;
                }
                Task task = tasks.get(0);
                BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
                FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());
                // 输入连线
                if(Objects.nonNull(flowNode)) {
                    List<SequenceFlow> inFlows = flowNode.getIncomingFlows();
                    for(SequenceFlow sequenceFlow : inFlows) {
                        // 上一个审批节点
                        FlowElement sourceFlow = sequenceFlow.getSourceFlowElement();
                        // 如果上个审批节点为开始节点
                        if(sourceFlow instanceof StartEvent) {
                            try {
                                runtimeService.deleteProcessInstance(processInstance.getId(), "过期清除");
                                historyService.deleteHistoricProcessInstance(processInstance.getId());
                                counter.incrementAndGet();
                                break;
                            }catch(Exception e) {
                                log.warn("deleteProcessForExpired", e.getMessage());
                            }
                            log.info("删除processInstanceId:{}", processInstance.getId());
                        }
                    }
                }
            }
            message.append("运行流程总数：" + processInstanceList.size() + " ，已删除过期无效数：" + counter.get());
        }else {
            message.append("没有符合运行的流程实例!");
        }
        return ResultJson.generateResult(message.toString());
    }

    @Override
    public ResultJson cancelProcess(FlowTaskVo flowTaskVo) {
        if(StringUtils.isEmpty(flowTaskVo.getBpmData().getProcessInstanceId())) {
            throw new CustomException("参数错误");
        }
        ProcessInstance processInstance =
                runtimeService.createProcessInstanceQuery().processInstanceId(flowTaskVo.getBpmData().getProcessInstanceId()).includeProcessVariables().singleResult();
        if(Objects.isNull(processInstance)) {
            throw new CustomException("流程未启动或已执行完成，终止申请失败");
        }

        // 流程启动人是自己或者用户是管理员
        if(!workflowCustomExtensionService.isBpmAdmin() && !(processInstance.getStartUserId().equals(SecurityUtils.getCurrentUserId().toString()))) {
            throw new CustomException("没有权限，终止申请失败");
        }

        // 设置表单状态
        try {
            Map<String, Object> variables = processInstance.getProcessVariables();
            formTemplateService.updateFormStatus(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), processInstance.getProcessInstanceId(), ProcStatusEnum.END);
        }catch(Exception e) {
            throw new FlowableException("更新流程表单状态出错", e);
        }

        // 设置流程状态为终止
        runtimeService.updateBusinessStatus(processInstance.getProcessInstanceId(), ProcStatusEnum.END.getValue());
        runtimeService.deleteProcessInstance(processInstance.getId(), ProcStatusEnum.END.getValue());

        return ResultJson.generateResult("终止成功");
    }

    /**
     * 删除流程实例ID
     * @param instanceIds  流程实例ID
     * @param deleteReason 删除原因
     */
    @Log("删除流程实例")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String instanceIds, String deleteReason) {
        Long currentUserId = SecurityUtils.getCurrentUserId();
        Boolean isBpmAdmin = workflowCustomExtensionService.isBpmAdmin();
        List<String> instanceIdList = Arrays.asList(instanceIds.split(ElAdminConstant.SEPARATOR_COMMA));
        instanceIdList.forEach(instanceId -> {
            // 查询历史数据
            HistoricProcessInstance historicProcessInstance =
                    historyService.createHistoricProcessInstanceQuery().processInstanceId(instanceId).singleResult();
            if(Objects.isNull(historicProcessInstance)) {
                throw new FlowableObjectNotFoundException("流程实例不存在: " + instanceId);
            }

            // 管理员或流程发起人才有权限
            if(!isBpmAdmin && !(currentUserId.toString().equals(historicProcessInstance.getStartUserId()))) {
                throw new CustomException("你没有权限删除该流程");
            }

            //删除待办
            Boolean deleteSuccess = workflowCustomExtensionService.deleteBackLog(instanceId);
            if(deleteSuccess) {
                if(Objects.isNull(historicProcessInstance.getEndTime())) {
                    // 删除流程实例
                    runtimeService.deleteProcessInstance(instanceId, deleteReason);
                }
                // 删除历史流程实例
                historyService.deleteHistoricProcessInstance(instanceId);
                //删除业务数据
                flowFormHandleFactory.deleteFormRecord(historicProcessInstance.getProcessDefinitionKey(), instanceId);
            }

            log.warn(SecurityUtils.getCurrentUsername() + "删除流程实例:" + instanceId);
        });
    }

    @Override
    @Transactional
    public void archiveProcess(ProcessArchive processArchive) {
        processArchiveRepository.save(processArchive);
    }

    @Override
    @Transactional
    public void archiveComment(ProcessCommentArchiveDto commentArchiveDto) {
        processCommentArchiveRepository.save(processCommentArchiveMapper.toEntity(commentArchiveDto));
    }

    @Override
    @Transactional
    public void archiveComment(List<ProcessCommentArchiveDto> commentArchiveDtoList) {
        if(CollectionUtils.isEmpty(commentArchiveDtoList)) {
            return;
        }
        List<ProcessCommentArchive> commentArchives = processCommentArchiveMapper.toEntity(commentArchiveDtoList);
        processCommentArchiveRepository.saveAll(commentArchives);
    }

    @Override
    public ProcessArchive getByProcInstanceId(String procInstanceId) {
        return processArchiveRepository.getByProcInstanceId(procInstanceId);
    }

    @Override
    public List<ProcessCommentArchive> findByProcInstanceId(String procInstanceId) {
        return processCommentArchiveRepository.findByProcInstanceIdOrderByTime(procInstanceId);
    }

    @Override
    public Map<String, Object> queryParamsByProcInsId(String procInsId) {
        if(StringUtils.isBlank(procInsId)) {
            return null;
        }
        Map<String, Object> map = new HashMap<>(2);
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInsId).singleResult();
        if(Objects.isNull(historicProcessInstance)) {
            return map;
        }

        // 流程定义信息
        ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(historicProcessInstance.getProcessDefinitionId()).singleResult();

        // 放入deployId
        map.put("deployId", pd.getDeploymentId());
        // 放入procDefId
        map.put("procDefId", historicProcessInstance.getProcessDefinitionId());
        return map;
    }

    @Override
    public FlowProcessInfoDto getProcessInfoByProcInsId(String procInsId) {
        if(StringUtils.isBlank(procInsId)) {
            return null;
        }
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInsId).includeProcessVariables().singleResult();
        if(Objects.isNull(historicProcessInstance)) {
            return null;
        }

        // 流程定义信息
        ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(historicProcessInstance.getProcessDefinitionId()).singleResult();

        UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));

        FlowProcessInfoDto.FlowProcessInfoDtoBuilder flowProcessInfoDtoBuilder = FlowProcessInfoDto.builder()
                .createTime(historicProcessInstance.getStartTime())
                .createBy(startUser.getNickName())
                .processInstanceId(procInsId)
                .procDeployId(pd.getDeploymentId())
                .procDefId(historicProcessInstance.getProcessDefinitionId())
                .procDefKey(historicProcessInstance.getProcessDefinitionKey());

        Map variables = historicProcessInstance.getProcessVariables();
        Object title = variables.get(ProcessConstants.BPM_FORM_TITLE);
        if(Objects.nonNull(title)) {
            flowProcessInfoDtoBuilder.title(title.toString());
        }

        // 当前处理人
        List<Task> taskList = taskService.createTaskQuery()
                .processInstanceId(procInsId)
                .list();
        if(!CollectionUtils.isEmpty(taskList)) {
            StringBuilder taskNames = new StringBuilder();
            StringBuilder taskAssignees = new StringBuilder();
            StringBuilder taskCopyToUsers = new StringBuilder();
            UserDto currentUserDto = null;
            for(Task task : taskList) {
                currentUserDto = sysUserService.findByName(task.getAssignee());
                taskNames.append(task.getName()).append(ElAdminConstant.SEPARATOR_COMMA);
                taskAssignees.append(currentUserDto.getNickName()).append(ElAdminConstant.SEPARATOR_COMMA);

                // 当前抄送人
                Object copyToAssignee = variables.get(ProcessConstants.BPM_BPM_COPYTO_PREFIX + task.getTaskDefinitionKey());
                if(Objects.nonNull(copyToAssignee) && StringUtils.isNotBlank(copyToAssignee.toString())) {
                    List<FlowTaskUserDto> taskUserDtos = Arrays.stream(copyToAssignee.toString().split(ElAdminConstant.SEPARATOR_COMMA)).filter(username -> StringUtils.isNotEmpty(username)).map(username -> {
                        UserDto sysUser = sysUserService.findByName(username);
                        if(Objects.nonNull(sysUser)) {
                            FlowTaskUserDto flowTaskUserDto = FlowTaskUserDto.builder()
                                    .assigneeId(sysUser.getId())
                                    .assigneeUsername(sysUser.getUsername())
                                    .assigneeName(sysUser.getNickName())
                                    .deptName(sysUser.getDept().getName())
                                    .build();
                            return flowTaskUserDto;
                        }else {
                            log.error("getProcessInfoByProcInsId 抄送人sysUser is null, username: " + username);
                            return null;
                        }
                    }).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(taskUserDtos)) {
                        for(FlowTaskUserDto taskUserDto : taskUserDtos) {
                            taskCopyToUsers.append(taskUserDto.getAssigneeName()).append(ElAdminConstant.SEPARATOR_COMMA);
                        }
                    }
                }
            }
            if(taskNames.length() > 0) {
                taskNames.deleteCharAt(taskNames.length() - 1);
            }
            if(taskAssignees.length() > 0) {
                taskAssignees.deleteCharAt(taskAssignees.length() - 1);
            }
            if(taskCopyToUsers.length() > 0) {
                taskCopyToUsers.deleteCharAt(taskCopyToUsers.length() - 1);
            }
            flowProcessInfoDtoBuilder.taskNames(taskNames.toString());
            flowProcessInfoDtoBuilder.taskAssignees(taskAssignees.toString());
            flowProcessInfoDtoBuilder.taskCopyToUsers(taskCopyToUsers.toString());
        }

        // 流程状态
        if(Objects.nonNull(historicProcessInstance.getEndTime())) {
            String procStatus = historicProcessInstance.getBusinessStatus();
            if(StringUtils.isNotEmpty(procStatus)) {
                ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(procStatus);
                flowProcessInfoDtoBuilder.procStatus(procStatusEnum);
            }else {
                flowProcessInfoDtoBuilder.procStatus(ProcStatusEnum.TG);
            }
        }else {
            flowProcessInfoDtoBuilder.procStatus(ProcStatusEnum.INPROGRESS);
        }

        return flowProcessInfoDtoBuilder.build();
    }

    @Override
    public List<FlowTaskAssigneeDto> getTaskInfoByProcInsId(String procInsId) {
        if(StringUtils.isBlank(procInsId)) {
            return null;
        }

        // 当前处理人
        List<FlowTaskAssigneeDto> flowTaskAssigneeDtoList = new ArrayList<>();
        List<Task> taskList = taskService.createTaskQuery()
                .processInstanceId(procInsId)
                .list();
        if(!CollectionUtils.isEmpty(taskList)) {
            UserDto currentUserDto = null;
            for(Task task : taskList) {
                TaskEntityImpl taskEntity = (TaskEntityImpl) task;
                currentUserDto = sysUserService.findByName(task.getAssignee());
                FlowTaskAssigneeDto flowTaskAssigneeDto = FlowTaskAssigneeDto.builder()
                        .procInstId(procInsId)
                        .taskId(task.getId())
                        .taskName(task.getName())
                        .taskDefKey(task.getTaskDefinitionKey())
                        .assigneeName(currentUserDto.getNickName())
                        .status(taskEntity.getSuspensionState())
                        .build();
                flowTaskAssigneeDtoList.add(flowTaskAssigneeDto);
            }
        }
        return flowTaskAssigneeDtoList;
    }

    @Override
    public String sortFormRemark(FlowRemarkVo flowRemarkVo) {
        if(StringUtils.isEmpty(flowRemarkVo.getRemarkJson())) {
            return null;
        }
        List<FlowFormRemarkDto> result = new ArrayList<>();

        JSONArray jsonArray = JSON.parseArray(flowRemarkVo.getRemarkJson());
        List<String> usernameList = new ArrayList<>();
        List<FlowFormRemarkDto> flowFormRemarkDtoList = new ArrayList<>();
        jsonArray.forEach((obj) -> {
            FlowFormRemarkDto flowFormRemarkDto = JSON.parseObject(obj.toString(), FlowFormRemarkDto.class);
            flowFormRemarkDtoList.add(flowFormRemarkDto);
            usernameList.add(flowFormRemarkDto.getUsername());
        });
        Map<String, List<FlowFormRemarkDto>> remarkMap = flowFormRemarkDtoList.stream().collect(Collectors.groupingBy(FlowFormRemarkDto::getUsername));

        switch(flowRemarkVo.getOrderBy()) {
            case "user-asc":
                List<UserDto> userDtoList = sysUserService.findByUserNames(usernameList);
                Collections.sort(userDtoList, (o1, o2) -> {
                    if(Objects.isNull(o1.getSort())) {
                        o1.setSort(0);
                    }
                    if(Objects.isNull(o2.getSort())) {
                        o2.setSort(0);
                    }
                    return o1.getSort() - o2.getSort();
                });
                for(UserDto userDto : userDtoList) {
                    result.addAll(remarkMap.get(userDto.getUsername()));
                }
                break;
            case "user-desc":
                userDtoList = sysUserService.findByUserNames(usernameList);
                Collections.sort(userDtoList, (o1, o2) -> {
                    if(Objects.isNull(o1.getSort())) {
                        o1.setSort(0);
                    }
                    if(Objects.isNull(o2.getSort())) {
                        o2.setSort(0);
                    }
                    return o2.getSort() - o1.getSort();
                });
                for(UserDto userDto : userDtoList) {
                    result.addAll(remarkMap.get(userDto.getUsername()));
                }
                break;
            case "offices-priority-dept-user-asc":
                // 本处室优先后部门用户正序
                userDtoList = sysUserService.findByUserNames(usernameList);
                Comparator byDept = Comparator.comparing(UserDto::getDept, (o1, o2) -> {
                    if(Objects.isNull(o1.getDeptSort())) {
                        o1.setDeptSort(0);
                    }
                    if(Objects.isNull(o2.getDeptSort())) {
                        o2.setDeptSort(0);
                    }
                    return o1.getDeptSort() - o2.getDeptSort();
                });
                Comparator byUser = Comparator.comparing(UserDto::getSort, Comparator.nullsLast(Comparator.naturalOrder()));
                Collections.sort(userDtoList, byDept.thenComparing(byUser));

                flowFormRemarkDtoList.clear();
                for(UserDto userDto : userDtoList) {
                    flowFormRemarkDtoList.addAll(remarkMap.get(userDto.getUsername()));
                }
                Long deptId = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getDept().getId();
                List<FlowFormRemarkDto> currentDeptRemarkList = flowFormRemarkDtoList.stream().filter(remarkDto -> Objects.nonNull(remarkDto.getDeptId()) && remarkDto.getDeptId().equals(deptId)).collect(Collectors.toList());
                result.addAll(currentDeptRemarkList);
                flowFormRemarkDtoList.removeAll(currentDeptRemarkList);
                result.addAll(flowFormRemarkDtoList);
                break;
            case "offices-priority":
                Collections.sort(flowFormRemarkDtoList, (o1, o2) -> DateUtil.compare(o1.getProcessedTime(), o2.getProcessedTime()));
                deptId = ((JwtUserDto) SecurityUtils.getCurrentUser()).getUser().getDept().getId();
                currentDeptRemarkList = flowFormRemarkDtoList.stream().filter(remarkDto -> Objects.nonNull(remarkDto.getDeptId()) && remarkDto.getDeptId().equals(deptId)).collect(Collectors.toList());
                result.addAll(currentDeptRemarkList);
                flowFormRemarkDtoList.removeAll(currentDeptRemarkList);
                result.addAll(flowFormRemarkDtoList);
                break;
            case "time-asc":
                Collections.sort(flowFormRemarkDtoList, (o1, o2) -> DateUtil.compare(o1.getProcessedTime(), o2.getProcessedTime()));
                result.addAll(flowFormRemarkDtoList);
                break;
            case "time-desc":
            default:
                Collections.sort(flowFormRemarkDtoList, (o1, o2) -> DateUtil.compare(o2.getProcessedTime(), o1.getProcessedTime()));
                result.addAll(flowFormRemarkDtoList);
        }
        return JSON.toJSONString(result);
    }
}
