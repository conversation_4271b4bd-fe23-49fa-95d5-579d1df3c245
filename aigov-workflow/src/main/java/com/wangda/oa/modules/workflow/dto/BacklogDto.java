package com.wangda.oa.modules.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @program: oa-mgt-server
 * @description: 统一待办请求对象
 * @author: maogy
 * @create: 2021-07-16 17:17
 **/
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BacklogDto {
    /**
     * 系统appId
     */
    private String appId;

    /**
     * 已办用户列表
     */
    private List<AlreadyUserListBO> alreadyUserList;


    /**
    * 待办用户列表
    */
    private List<BacklogUserDto> backlogUserList;

    /**
     * 外部业务id(同个任务相同)
     */
    private String bizId;

    /**
     *删除用户列表
     */
    private List<DeleteUserListBO> deleteUserList;

    /**
     * 模块名称
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 标题
     */
    private String title;

    /**
     * pc跳转url
     */
    private String pcUrl;

    /**
     *跳转详情url
     */
    private String url;

    /**
     * 是否全部紧急(0:否,1:是,2:部分紧急)
     */
    private Integer urgent;

    /**
     * 紧急用户集合（当urgent为2时使用）
     */
    private List<String> urgentUserList;

    /**
     * 业务扩展属性，json格式字符串
     */
    private String extensionJson;

    /**
     * 徽标
     */
    private String logo;

    /**
     * 用户
     */
    private String userId;

    @ApiModelProperty(value = "创建时间(若为当前时间则不用传)")
    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;
}
