package com.wangda.oa.modules.workflow.service.form;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.modules.workflow.bo.QueryFormBO;
import com.wangda.oa.modules.workflow.bo.SubmitFormBO;
import com.wangda.oa.modules.workflow.bo.TemplateListBO;
import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import com.wangda.oa.modules.workflow.dto.TemplateListDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import org.springframework.http.ResponseEntity;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/22 15:05
 */
public interface FormTemplateService {

    ResponseInfo save(WdFormTemplate template);

    WdFormTemplate queryById(Long id);

    TemplateListDto queryAll(TemplateListBO bo);

    ResponseInfo submitForm(SubmitFormBO submitFormBO) throws Exception;

    /**
     * 获取表单数据
     * @param queryFormBO
     * @return
     */
    ResponseEntity getFormData(QueryFormBO queryFormBO) throws Exception;

    /**
     * 表单数据提交
     * @param templateId
     * @param formData
     * @return
     */
    ResponseInfo formSubmit(Long templateId, String formData) throws Exception;

    /**
     * 表单数据提交，过滤任务自读属性
     * @param templateId
     * @param formData
     * @param taskId 流程任务id
     * @return
     */
    ResponseInfo formSubmit(Long templateId, String formData, String taskId) throws Exception;

    /**
     * 处理表单读写状态
     * @param jsonArray
     * @param formRwhMap
     */
    void formRwHandle(JSONArray jsonArray, Map<String, String> formRwhMap);

    /**
     * 修改表单流程状态
     * @param templateId
     * @param procInstanceId
     * @param procStatusEnum
     * @throws Exception
     */
    void updateFormStatus(Long templateId, String procInstanceId, ProcStatusEnum procStatusEnum) throws Exception;

    /**
     * 更新表单数据
     * @param templateId
     * @param procInstanceId
     * @param fieldName
     * @param data
     * @param append 追加还是替换
     * @throws Exception
     */
    ResponseInfo updateFormFieldData(Long templateId, String procInstanceId, String fieldName, Object data, boolean append) throws Exception;

    /**
     * 更新表单数据
     * @param templateId
     * @param procInstanceId
     * @param fieldName
     * @param data
     * @param append 追加还是替换
     * @param insertBefore 当追加时，插入前面还是追加后面
     * @return
     * @throws Exception
     */
    ResponseInfo updateFormFieldData(Long templateId, String procInstanceId, String fieldName, Object data, boolean append, boolean insertBefore) throws Exception;

    /**
     * 更新流程变量表单数据
     * @param formData
     * @param fieldName
     * @param data
     * @param append
     * @param insertBefore
     * @return
     * @throws Exception
     */
    void updateVariableFormFieldData(JSONObject formData, String fieldName, Object data, boolean append, boolean insertBefore) throws Exception;

    /**
     * 删除表单数据
     * @param templateId
     * @param procInstanceId
     * @param fieldName
     * @param data
     * @return
     * @throws Exception
     */
    ResponseInfo deleteFormFieldData(Long templateId, String procInstanceId, String fieldName, Object data, boolean insertBefore) throws Exception;

    /**
     * 删除表单
     * @param id
     * @return
     */
    boolean deleteFrom(Long id);
}
