package com.wangda.oa.modules.workflow.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wangda.oa.modules.workflow.bo.DynamicFormInfoBO;
import com.wangda.oa.modules.workflow.bo.MyDynamicFormListBO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/11 上午10:59
 */
public interface DynamicTableMapper {

    /**
     * 查询动态表单是否存在数据
     * @param tableName
     * @return
     */
    Integer checkTableData(@Param("tableName")String tableName);

    /**
     * 删除动态表单
     * @param tableName
     */
    void deleteTable(@Param("tableName")String tableName);

    /**
     * 根据tableName,控件field新增数据
     * @param tableName
     * @param tableFields
     */
    void addTableData(@Param("tableName")String tableName,@Param("tableFields") Map<String, Object> tableFields);

    /**
     * 查询我的填报
     * @param page
     * @param qry
     * @return
     */
    IPage<Map<String,Object>> getMyFormList(Page page, @Param("qry") MyDynamicFormListBO qry);

    /**
     * 查询自动表单详情
     * @param qry
     * @return
     */
    Map<String,Object> getFormInfo(@Param("qry") DynamicFormInfoBO qry);

    /**
     * 查询自动表单详情
     * @param qry
     * @return
     */
    Map<String,Object> getFormInfoByPid(@Param("qry") DynamicFormInfoBO qry);

    /**
     * 删除自动表单
     * @param qry
     */
    void deleteFormInfo(@Param("qry") DynamicFormInfoBO qry);

    /**
     * 判断表是否存在
     * @param tableName
     * @return
     */
    int checkTableExist(@Param("tableName")String tableName);

    /**
     * 判断表中字段是否存在
     * @param tableName
     * @param filed
     * @return
     */
    int checkTableFieldExist(@Param("tableName")String tableName,@Param("filed")String filed);

    /**
     * 给表新增字段
     * @param tableName
     * @param key
     * @param value
     */
    void insertTableField(@Param("tableName")String tableName,@Param("key") String key,@Param("value") String value);

}
