package com.wangda.oa.modules.workflow.service;

import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/13
 * @description 流程表单处理
 */
public interface FlowFormHandle {

    /**
     * 根据流程实例编号处理业务表单数据到我的工作
     * @param myWork 我的工作实例
     * @param myWorkDto
     */
    void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto);

    /**
     * 根据流程定义中标题规则解析
     * @param formDataObj 表单对象
     * @param subjectRule 标题规则
     * @return String
     */
    String handleSubjectRule(JSONObject formDataObj, String subjectRule);

    /**
     * 获取表单数据
     * @param procInstanceId
     * @param taskDefKey
     * @param bpmFormData
     * @return
     */
    Object handleFormRecord(String procInstanceId, String taskDefKey, JSONObject bpmFormData);

    /**
     * 删除表单数据
     * @param instanceId 表单instanceId
     * @return
     */
    void deleteFormRecord(String instanceId);
}
