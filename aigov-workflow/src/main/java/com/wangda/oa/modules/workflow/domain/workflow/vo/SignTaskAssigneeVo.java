package com.wangda.oa.modules.workflow.domain.workflow.vo;

import com.wangda.oa.utils.ElAdminConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-05-03
 */
@Data
@NoArgsConstructor
@ApiModel("加减签处理人--请求参数")
public class SignTaskAssigneeVo {

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("任务key")
    private String taskKey;

    @ApiModelProperty("任务Id")
    private String taskId;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SignTaskAssigneeVo that = (SignTaskAssigneeVo) o;
        return Objects.equals(username, that.getUsername()) && Objects.equals(taskKey, that.getTaskKey());
    }

    @Override
    public String toString() {
        return this.username.concat(ElAdminConstant.SEPARATOR_DOLLAR).concat(this.taskKey);
    }

}
