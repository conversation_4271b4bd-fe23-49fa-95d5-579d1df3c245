package com.wangda.oa.modules.workflow.repository.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午8:30
 */
public interface WorkflowUserTaskExtensionRepository extends JpaRepository<WorkflowUserTaskExtension,Long>, JpaSpecificationExecutor<WorkflowUserTaskExtension> {

    /**
     * 查看序号是否存在(编辑)
     * @param sort
     * @return
     */
    int countBySort(Integer sort);

    /**
     * 查看序号是否存在(编辑)
     * @param sort 排序
     * @param id 主键
     * @return
     */
    int countBySortAndIdNot(Integer sort,Long id);

    /**
     * 根据linkKey查询数据
     * @param deployId
     * @param linkKey
     * @return
     */
    List<WorkflowUserTaskExtension> findByFlowDefineIdAndLinkKey(String deployId, String linkKey);

    /**
     * 根据flowDefineId查询数据
     * @param flowDefineId
     * @return
     */
    Page<WorkflowUserTaskExtension> findAllByFlowDefineId(String flowDefineId, Pageable pageable);
}
