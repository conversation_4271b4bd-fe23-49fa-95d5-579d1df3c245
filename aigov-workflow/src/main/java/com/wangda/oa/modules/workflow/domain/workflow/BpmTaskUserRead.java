package com.wangda.oa.modules.workflow.domain.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/2
 * @description 待阅已阅
 */

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Builder
public class BpmTaskUserRead extends BaseDomain {

    @NotBlank
    @ApiModelProperty("流程实例Id")
    private String processInstanceId;

    @ApiModelProperty("任务Id")
    private String taskId;

    @NotBlank
    @ApiModelProperty("流程定义key")
    private String processDefKey;

    @NotBlank
    @ApiModelProperty("标题")
    private String bt;

    @ApiModelProperty("办理人")
    private String assignee;

    @ApiModelProperty(value = "初次阅读时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date firstReadTime;

    @JSONField(format = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "最后阅读时间")
    private Date lastReadTime;

    @ApiModelProperty("意见")
    private String remark;

}
