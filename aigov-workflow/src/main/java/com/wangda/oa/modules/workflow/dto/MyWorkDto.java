package com.wangda.oa.modules.workflow.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.oa.modules.workflow.enums.MyWorkStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 我的工作
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MyWorkDto {

    @ApiModelProperty(value = "编号")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @NotBlank
    @ApiModelProperty(value = "流程编号")
    private String processInstanceId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "类别")
    private String type;

    @ApiModelProperty(value = "缓急(普通,急)")
    private String hj;

    @ApiModelProperty(value = "领导批示")
    private String instruction;

    @JSONField(format = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "状态")
    private MyWorkStatusEnum status;

    @ApiModelProperty(value = "pc跳转url")
    private String pcUrl;

    @ApiModelProperty(value = "角标")
    private String logo;

}
