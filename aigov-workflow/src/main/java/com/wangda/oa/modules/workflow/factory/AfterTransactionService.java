package com.wangda.oa.modules.workflow.factory;

import com.wangda.oa.modules.system.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/25
 * @description 事务提交后执行服务
 */
@Slf4j
@Component
public class AfterTransactionService extends TransactionSynchronizationAdapter {

    public static final ExecutorService executorService = Executors.newFixedThreadPool(2);
    public static final int MaxRetryNum = 3;
    public static volatile int curRetryNum = 0;

    @Override
    public void afterCompletion(int status) {
        Map<String, LinkedBlockingQueue<Runnable>> afterStack =  AfterTransactionOpt.getAfterTransactionOpts().get();
        //将任务提交到线程池执行
        LinkedBlockingQueue<Runnable> executeQueue =  afterStack.get(AfterTransactionOpt.getTransactionName());
        while(!executeQueue.isEmpty()){
            Runnable task = executeQueue.poll();
            switch(status) {
                case 0: // AFTER_COMMIT
                    try {
                        // 阻塞方法
                        executorService.submit(task).get();
                        if(curRetryNum > 0) {
                            curRetryNum--;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        if(curRetryNum <= MaxRetryNum) {
                            curRetryNum++;
                            try {
                                TimeUnit.SECONDS.sleep(5);
                                executeQueue.offer(task, 5, TimeUnit.SECONDS);
                            } catch (InterruptedException interruptedException) {
                                interruptedException.printStackTrace();
                            }
                        }else {
                            throw new CustomException("待办服务不可用，请联系管理员");
                        }
                    }
                    break;
                case 1: // AFTER_ROLLBACK
                default:
            }
            log.info("afterCompletion end...., execute map key name is:"+AfterTransactionOpt.getTransactionName());
        }
    }
}
