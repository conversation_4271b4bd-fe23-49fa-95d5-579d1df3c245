package com.wangda.oa.modules.workflow.bo;

import com.wangda.boot.platform.base.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/1/19 下午5:38
 */
@Data
public class MyDynamicFormListBO extends Pagination {

    @ApiModelProperty(value = "填报人id")
    private String creatorId;

    @ApiModelProperty(value = "模板id")
    @NotNull(message = "模板id不能为空")
    private Long templateId;

}
