package com.wangda.oa.modules.workflow.enums.workflow;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR> leec
 * @title: : CommentTypeEnum
 * @description: 审批意见的类型
 * @date : 2021/04/11
 */
@Getter
@AllArgsConstructor
@ToString
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@JSONType(serializeEnumAsJavaBean = true)
public enum TaskCommentTypeEnum {
    SP("审批"),
    JJ("拒绝"),
    TH("退回"),
    TJ("提交"),
    /**
     * 记录不留痕
     */
    CH("撤回"),
    /**
     * 记录留痕
     */
    SH("收回"),
    CX("撤销"),
    ZC("暂存"),
    QS("签收"),
    WP("委派"),
    <PERSON><PERSON>("转阅"),
    <PERSON><PERSON>("已阅"),
    Z<PERSON>("转办"),
    QJQ("前加签"),
    HJQ("后加签"),
    XTZX("系统执行"),
    CXTJ("重新提交"),
    LCZZ("流程终止"),
    CFTG("重复跳过");

    //名称
    private String name;

    /**
     * 通过type获取Msg
     * @param type
     * @return
     * @Description:
     */
    public static String getEnumInfoByType(String type) {
        for (TaskCommentTypeEnum e : TaskCommentTypeEnum.values()) {
            if (e.name().equals(type)) {
                return e.name;
            }
        }
        return null;
    }
}
