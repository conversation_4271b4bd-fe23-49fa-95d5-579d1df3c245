package com.wangda.oa.modules.workflow.dto.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskButtons;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-03
 */
@Data
@Builder
@ApiModel("工作流任务相关-处理单结构")
public class FlowTaskProcessDto implements Serializable {

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务Key")
    private String taskDefKey;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("流程实例id")
    private String processInstanceId;

    @ApiModelProperty("流程定义key")
    private String procDefKey;

    @ApiModelProperty("流程定义id")
    private String procDefId;

    @ApiModelProperty("按钮操作")
    private List<WorkflowUserTaskButtons> taskButtons;

    @ApiModelProperty("意见框是否显示")
    private boolean remarkDisable;

    @ApiModelProperty("意见框是否允许为空")
    @Builder.Default
    private boolean remarkBlankAllow = true;

    @ApiModelProperty("意见框是否允许附件上传")
    private boolean remarkAttachmentAllow;

    @ApiModelProperty("是否手写签名")
    private boolean remarkSeal;

    @ApiModelProperty("通知方式 sms,zwdd-ding")
    private String remindWay;

    @ApiModelProperty("提示消息")
    private String message;

    @ApiModelProperty("任务意见")
    private String remark;

    @ApiModelProperty("多端支持")
    private String mutliDeviceSupport;

    @ApiModelProperty("下个任务节点")
    private List<FlowNextDto> userTaskList;

    @ApiModelProperty("下个连接线节点")
    private List<FlowSequenceDto> flowSequenceList;

}
