package com.wangda.oa.modules.workflow.service;

import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/28
 * @description 系统应用相关接口
 */
public interface ISysAppService {

    /**
     * 根据用户名和类型查询数据
     * @param username
     * @param type
     * @return
     */
    SysUserPlatform getByUserNameAndType(String username, String type);

    /**
     * userId
     * @param
     * @return
     */
    SysUserPlatform getByUserId(String userId, String type);

    /**
     * 根据username获取uid
     * @param username
     * @return
     */
    ResultJson getUid(String username);
}
