package com.wangda.oa.modules.workflow.dto.workflow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/3/28 15:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProcessCommentArchiveDto implements Serializable {

    @ApiModelProperty(value = "类别", notes = "CommentTypeEnum的属性名")
    @NotBlank
    private String type;

    @ApiModelProperty(value = "意见内容")
    @NotBlank
    private String comment;

    @ApiModelProperty(value = "意见名称")
    private String name;

    @ApiModelProperty(value = "处理用户名")
    @NotBlank
    private String username;

    @ApiModelProperty(value = "处理人部门名称")
    private String deptName;

    @ApiModelProperty(value = "处理时间")
    @NotBlank
    private Date time;

    @ApiModelProperty(value = "处理人任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "流程实例名称")
    private String procInstanceName;

    @ApiModelProperty(value = "流程实例ID")
    private String procInstanceId;
}
