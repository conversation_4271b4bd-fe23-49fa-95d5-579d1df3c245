package com.wangda.oa.modules.workflow.repository.workflow;

import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserRead;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4 上午9:29
 * 待阅已阅
 */
@Repository
public interface BpmTaskUserReadRepository extends JpaRepository<BpmTaskUserRead, Long>, JpaSpecificationExecutor<BpmTaskUserRead> {

    BpmTaskUserRead findFirstByTaskIdAndAssignee(String taskId, String assignee);

    BpmTaskUserRead findFirstByProcessInstanceIdAndAssignee(String processInstanceId, String assignee);

    List<BpmTaskUserRead> findAllByProcessInstanceId(String processInstanceId);
}
