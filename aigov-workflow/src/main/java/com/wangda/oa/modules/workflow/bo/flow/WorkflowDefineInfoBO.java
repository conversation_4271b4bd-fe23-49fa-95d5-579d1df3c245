package com.wangda.oa.modules.workflow.bo.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 流程定义表
 *
 * <AUTHOR>
 * @date 2021/4/14上午12:32
 */
@Data
public class WorkflowDefineInfoBO{

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "流程总标识(XXX:XX:XX)")
    private String processDefinitionId;

    @ApiModelProperty(value = "流程标识")
    private String processDefinitionKey;

    @ApiModelProperty(value = "流程名称")
    private String name;

    @ApiModelProperty(value = "流程分类id")
    private Long classifyId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否主流程(0:否,1:是)")
    private Integer mainProcess;

    @ApiModelProperty(value = "流程图标id(看菜单图标怎么存)")
    private String icon;

    @ApiModelProperty(value = "多端支持")
    private List<String> terminals;

    @ApiModelProperty(value = "状态(0:禁用,1:启用)")
    private Integer state;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "流转表单")
    private String formKey;

    @ApiModelProperty(value = "打印模板(id或url)")
    private String printTemplate;

    @ApiModelProperty(value = "自定义后端api")
    private String customizeBackendApi;

    @ApiModelProperty(value = "待办标题规则")
    private String backlogTitleRule;

    @ApiModelProperty(value = "待办扩展属性规则")
    private String backlogExtensionRule;

    @ApiModelProperty(value = "办理时限类型(0:工作日,1:自然日)")
    private Integer dealWithType;

    @ApiModelProperty(value = "办理时限")
    private Integer dealWithTime;

    @ApiModelProperty(value = "流程管理人")
    private String processManager;

    @ApiModelProperty(value = "流程启动人")
    private String processStart;

    @ApiModelProperty(value = "流程查阅人")
    private String processConsult;
}
