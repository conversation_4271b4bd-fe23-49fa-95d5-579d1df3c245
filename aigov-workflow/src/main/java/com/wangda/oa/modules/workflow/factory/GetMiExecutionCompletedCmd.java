package com.wangda.oa.modules.workflow.factory;

import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * flowable 获取多实例完成数量
 * <AUTHOR>
 * @date 2022-03-23
 */
@Component
@Slf4j
@NoArgsConstructor
public class GetMiExecutionCompletedCmd implements Command<Integer> {

    private String executionId;
    private MultiInstanceActivityBehavior multiInstanceActivityBehavior;

    public GetMiExecutionCompletedCmd(String executionId, MultiInstanceActivityBehavior multiInstanceActivityBehavior) {
        this.executionId = executionId;
        this.multiInstanceActivityBehavior = multiInstanceActivityBehavior;
    }

    @Override
    public Integer execute(CommandContext commandContext) {
        ExecutionEntity executionEntity = CommandContextUtil.getExecutionEntityManager(commandContext).findById(executionId);
        Integer nrOfCompletedInstances = multiInstanceActivityBehavior.getLoopVariable(executionEntity, ProcessConstants.NUMBER_OF_COMPLETED_INSTANCES);
        if(Objects.isNull(nrOfCompletedInstances)) {
            nrOfCompletedInstances = 0;
        }
        return nrOfCompletedInstances;
    }
}
