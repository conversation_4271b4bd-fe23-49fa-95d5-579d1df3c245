package com.wangda.oa.modules.workflow.service.form.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.config.ElPermissionConfig;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.workflow.bo.QueryFormBO;
import com.wangda.oa.modules.workflow.bo.SubmitFormBO;
import com.wangda.oa.modules.workflow.bo.TemplateListBO;
import com.wangda.oa.modules.workflow.bo.flow.GetInfoByKeyBO;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.definition.WorkflowDeployForm;
import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskFormRw;
import com.wangda.oa.modules.workflow.dto.TemplateListDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.repository.definition.WorkflowDeployFormRepository;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowUserTaskExtensionService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.CloneUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.dao.ConcurrencyFailureException;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/22 15:06
 */
@Slf4j
@Service
public class FormTemplateServiceImpl implements FormTemplateService {

    @Resource
    private WdFormTemplateRepository wdFormTemplateRepository;

    @Resource
    private UserService userService;

    @Resource
    private WorkflowDeployFormRepository workflowDeployFormRepository;

    @Resource
    private TaskService taskService;

    @Resource
    private HistoryService historyService;

    @Resource
    private WorkflowUserTaskExtensionService workflowUserTaskExtensionService;

    @Resource
    private ElPermissionConfig elPermissionConfig;

    @PersistenceContext
    private EntityManager em;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseInfo save(WdFormTemplate template) {
        if (StringUtils.isBlank(template.getFormJson())) {
            return new ResponseInfo(ResultCodeEnum.ILLEGAL_ARGUMENT.getCode(),
                    ResultCodeEnum.ILLEGAL_ARGUMENT.getMessageCN());
        }
        if(template.getUserId()==null){
            UserDto userInfoDto = userService.findById(SecurityUtils.getCurrentUserId());
            template.setUserId(userInfoDto.getId());
            template.setCreatorName(userInfoDto.getNickName());
        }
        WdFormTemplate wdFormTemplate;
        if (Objects.nonNull(template.getId())){
            wdFormTemplate = wdFormTemplateRepository.findById(template.getId()).orElse(new WdFormTemplate());
        } else {
            wdFormTemplate = new WdFormTemplate();
        }
        wdFormTemplate.setFormJson(template.getFormJson());

        // 设置表对象名
        JSONObject formJsonObj = JSONObject.parseObject(template.getFormJson());
        JSONObject formConfigObj = formJsonObj.getJSONObject("config");
        wdFormTemplate.setClassName(formConfigObj.getString("tableClazzName"));
        wdFormTemplate.setShared(template.getShared());
        wdFormTemplate.setUserId(template.getUserId());
        wdFormTemplate.setCreatorName(template.getCreatorName());
        wdFormTemplate.setTitle(template.getTitle());
        wdFormTemplate.setCategory(template.getCategory());

        wdFormTemplateRepository.save(wdFormTemplate);

        return new ResponseInfo(ResultCodeEnum.SUCCESS.getCode(),
                ResultCodeEnum.SUCCESS.getMessageCN());
    }

    @Override
    public WdFormTemplate queryById(Long id) {
        return wdFormTemplateRepository.findById(id).orElse(null);
    }

    @Override
    public TemplateListDto queryAll(TemplateListBO bo) {
        if(StringUtils.isBlank(bo.getUserId())){
            bo.setUserId(String.valueOf(SecurityUtils.getCurrentUserId()));
        }
        if(elPermissionConfig.check("admin")) {
            bo.setUserId(null);
        }
        Pageable pageable = PageRequest.of(bo.getPage(), bo.getSize());
        org.springframework.data.domain.Page<WdFormTemplate> page= wdFormTemplateRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,bo,criteriaBuilder), pageable);
        TemplateListDto templateListDto=new TemplateListDto();
        templateListDto.setContent(page.getContent());
        templateListDto.setTotalElements(page.getTotalElements());
        return templateListDto;
    }

    @Override
    public ResponseInfo submitForm(SubmitFormBO submitFormBO) throws Exception {

        // 预处理formData
        String formData = this.preFormatFormData(submitFormBO.getFormDataJson(), null);

        WdFormTemplate template = wdFormTemplateRepository.getOne(submitFormBO.getFormTemplateId());
        return this.submitByClazz(template.getClassName(), formData, "id", null);
    }

    @Override
    public ResponseEntity getFormData(QueryFormBO queryFormBO) throws Exception {
        WdFormTemplate template = wdFormTemplateRepository.getOne(queryFormBO.getTemplateId());

        // 根据流程实例编号获取表单数据
        String queryField = null, queryValue = null;
        if(StringUtils.isNotBlank(queryFormBO.getUsername())) {
            queryField = "createBy";
            queryValue = queryFormBO.getUsername();
        }else if(StringUtils.isNotBlank(queryFormBO.getProcessInstanceId())) {
            queryField = "bpmInstanceId";
            queryValue = queryFormBO.getProcessInstanceId();
        }
        Object formData = this.queryFormData(template.getClassName(), queryField, queryValue);
        if(Objects.nonNull(formData)) {
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(formData, SerializerFeature.WriteDateUseDateFormat));
            return ResponseEntity.ok(jsonObject);
        }
        return ResponseEntity.ok(formData);
    }

    /**
     * 预处理表单数据，x$y的字符串解析成对象
     * 判断表单key是否带'$'
     */
    private String preFormatFormData(String formData, JSONArray jsonArray) {
        JSONObject result = new JSONObject();
        JSONObject jsonObject = JSONObject.parseObject(formData);
        if(Objects.nonNull(jsonObject)) {
            jsonObject.forEach((s, o) -> {
                String[] keys = s.split("\\"+ElAdminConstant.SEPARATOR_DOLLAR);
                if(keys.length >= 2) {
                    JSONObject subResult = result;
                    for(int i=0; i<keys.length; i++) {
                        if(i == keys.length - 2) {
                            if(subResult.containsKey(keys[i])) {
                                JSONObject subObject = subResult.getJSONObject(keys[i]);
                                subObject.put(keys[i+1], o);
                            }else {
                                JSONObject subObject = new JSONObject();
                                subObject.put(keys[i+1], o);
                                subResult.put(keys[i], subObject);
                            }
                            break;
                        }else {
                            if(subResult.containsKey(keys[i])) {
                                subResult = subResult.getJSONObject(keys[i]);
                            }else {
                                JSONObject subObject = new JSONObject();
                                subResult.put(keys[i], subObject);
                                subResult = subObject;
                            }
                        }
                    }
                }else {
                    if(o instanceof JSONArray) {
                        JSONArray subArray = new JSONArray();
                        for (Object a : ((JSONArray) o)) {
                            if(a instanceof JSONObject) {
                                this.preFormatFormData(a.toString(), subArray);
                            }else {
                                subArray.add(a);
                            }
                        }
                        result.put(s, subArray);
                    }else {
                        result.put(s, o);
                    }
                }
            });
            if(Objects.nonNull(jsonArray)) {
                jsonArray.add(result);
            }
        }
        return result.toJSONString();
    }

    @Override
    @Transactional
    public ResponseInfo formSubmit(Long templateId, String formData) throws Exception {
        return this.formSubmit(templateId, formData, null);
    }

    @Override
    @Transactional
    public ResponseInfo formSubmit(Long templateId, String formData, String taskId) throws Exception {
        // 预处理formData
        formData = this.preFormatFormData(formData, null);

        WdFormTemplate template = wdFormTemplateRepository.getOne(templateId);
        if(StringUtils.isBlank(template.getClassName())) {
            return new ResponseInfo("表单没有配置持久化对象");
        }

        // 表单模版
        JSONObject formObject = JSON.parseObject(template.getFormJson());

        // 获取流程扩展
        if(StringUtils.isNotEmpty(taskId)) {
            JSONObject formObj = JSONObject.parseObject(formData);
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if(Objects.nonNull(task)) {
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(formObj.getString("bpmInstanceId")).singleResult();

                GetInfoByKeyBO getInfoByKeyBO = GetInfoByKeyBO.builder().linkKey(task.getTaskDefinitionKey()).flowDefineId(historicProcessInstance.getDeploymentId()).build();
                WorkflowUserTaskExtension workflowUserTaskExtension = workflowUserTaskExtensionService.getInfoByKey(getInfoByKeyBO);
                Map<String, String> formRwhMap = new HashMap<>();
                if(Objects.nonNull(workflowUserTaskExtension)) {
                    List<WorkflowUserTaskFormRw> formReadWrite = workflowUserTaskExtension.getReadWrite();
                    if(!CollectionUtils.isEmpty(formReadWrite)) {
                        formRwhMap = formReadWrite.stream().collect(Collectors.toMap(k -> StringUtils.isNotEmpty(k.getSubFormId())? k.getSubFormId()+k.getFormFieldIdentifier(): k.getFormFieldIdentifier(), v -> v.getReadState()));
                    }
                    formRwhMap.put(ProcessConstants.BPM_RWH_DEFAULT, workflowUserTaskExtension.getFormRead());
                }

                // 处理流转表单读写属性
                this.formRwHandle(formObject.getJSONArray("list"), formRwhMap);
            }
        }

        // 过滤属性值
        List<String> ignoreProperties = this.findFormReadProperties(formObject.getJSONArray("list"), null);

        return this.submitByClazz(template.getClassName(), formData, "bpmInstanceId", ignoreProperties);
    }

    /**
     * 处理表单读写
     * @param jsonArray
     * @param formRwhMap
     */
    public void formRwHandle(JSONArray jsonArray, Map<String, String> formRwhMap) {

        if(jsonArray.isEmpty()) {
            return;
        }
        jsonArray.forEach((object) -> {
            if(object instanceof JSONObject) {
                JSONObject formObject = (JSONObject) object;
                if(Objects.nonNull(formObject)) {
                    String model = formObject.getString("model");
                    if(StringUtils.isNotEmpty(model)) {
                        JSONObject option = formObject.getJSONObject("options");
                        String status = formRwhMap.get(ProcessConstants.BPM_RWH_DEFAULT);
                        if(formRwhMap.containsKey(model)) {
                            status = formRwhMap.get(model);
                        }
                        if(StringUtils.isEmpty(status)) {
                            status = "RWH";
                        }
                        switch (status) {
                            case "R":
                                option.put("disabled", true);
                                if(formRwhMap.containsKey(model)) {
                                    option.put("hidden", false);
                                }
                                break;
                            case "W":
                                // 表单原始值是只读的不处理
                                Boolean oriDisabled = option.getBoolean("disabled");
                                if(Objects.isNull(oriDisabled) || !oriDisabled) {
                                    option.put("disabled", false);
                                }
                                if(formRwhMap.containsKey(model)) {
                                    option.put("hidden", false);
                                }
                                break;
                            case "H":
                                option.put("hidden", true);
                                break;
                        }
                    }

                    // 如果是嵌套
                    if(Objects.nonNull(formObject.getJSONArray("rows")) && !formObject.getJSONArray("rows").isEmpty()) {
                        this.formRwHandle(formObject.getJSONArray("rows"), formRwhMap);
                    }else if(Objects.nonNull(formObject.getJSONArray("columns")) && !formObject.getJSONArray("columns").isEmpty()) {
                        this.formRwHandle(formObject.getJSONArray("columns"),formRwhMap);
                    }else if(Objects.nonNull(formObject.getJSONArray("list")) && !formObject.getJSONArray("list").isEmpty()) {
                        this.formRwHandle(formObject.getJSONArray("list"),formRwhMap);
                    }
                }
            }
        });
    }

    /**
     * 处理表单读写
     * @param jsonArray
     */
    public List<String> findFormReadProperties(JSONArray jsonArray, List<String> resultList) {
        resultList = resultList == null? new ArrayList<>(): resultList;
        if(jsonArray.isEmpty()) {
            return resultList;
        }
        for (Object object: jsonArray) {
            if(object instanceof JSONObject) {
                JSONObject formObject = (JSONObject) object;
                if(Objects.nonNull(formObject)) {
                    String model = formObject.getString("model");
                    if(StringUtils.isNotEmpty(model)) {
                        JSONObject option = formObject.getJSONObject("options");
                        Boolean status = option.getBoolean("disabled");
                        if(Objects.nonNull(status) && status) {
                            resultList.add(model);
                        }
                    }

                    // 如果是嵌套
                    if(Objects.nonNull(formObject.getJSONArray("rows")) && !formObject.getJSONArray("rows").isEmpty()) {
                        this.findFormReadProperties(formObject.getJSONArray("rows"), resultList);
                    }else if(Objects.nonNull(formObject.getJSONArray("columns")) && !formObject.getJSONArray("columns").isEmpty()) {
                        this.findFormReadProperties(formObject.getJSONArray("columns"),resultList);
                    }else if(Objects.nonNull(formObject.getJSONArray("list")) && !formObject.getJSONArray("list").isEmpty()) {
                        this.findFormReadProperties(formObject.getJSONArray("list"),resultList);
                    }
                }
            }
        }
        return resultList;
    }

    /**
     *
     * @param fullClazz 全路径类名
     * @param formData 表单数据
     * @param queryField 查询字段
     * @return
     * @throws Exception
     */
    @Transactional
    protected ResponseInfo submitByClazz(String fullClazz, String formData, String queryField, List<String> ignoreProperties) throws Exception {

        Class clazz = Class.forName(fullClazz);
        Object formBean = JSON.parseObject(formData, clazz);

        String clazzName = clazz.getSimpleName();
        clazzName = clazzName.substring(0, 1).toLowerCase() + clazzName.substring(1);
        JpaRepository beanRepository = SpringContextHolder.getBean(clazzName+"Repository");

        // 根据流程实例编号获取表单数据
        JSONObject jsonObject = JSONObject.parseObject(formData);
        Object sourceFormBean = this.queryFormData(fullClazz, queryField,  jsonObject.getString(queryField));
        if(Objects.nonNull(sourceFormBean)) {
            // 过滤掉只读属性，不覆盖值
            if(Objects.isNull(ignoreProperties)) {
                ignoreProperties = new ArrayList<>();
            }
            BeanUtil.copyProperties(formBean, sourceFormBean, CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties(ignoreProperties.stream().toArray(String[]::new)));
            try {
                beanRepository.saveAndFlush(sourceFormBean);
            }catch (OptimisticLockingFailureException lockingFailureException) {
                lockingFailureException.printStackTrace();
                throw new CustomException("办理提交冲突，请刷新页面后重新提交");
            }
            JSONObject sourceFormBeanJSON = (JSONObject)JSON.toJSON(sourceFormBean);
            // 更新只读属性
            for (String key: ignoreProperties) {
                if(sourceFormBeanJSON.containsKey(key)) {
                    Object value = sourceFormBeanJSON.get(key);
                    if(Objects.nonNull(value)) {
                        jsonObject.put(key, value);
                    }
                }
            }
        }else {
            beanRepository.saveAndFlush(formBean);
        }
        em.clear();
        return new ResponseInfo(ResultCodeEnum.SUCCESS.getCode(),
                ResultCodeEnum.SUCCESS.getMessageCN(), jsonObject);
    }

    @Override
    @Transactional
    public void updateFormStatus(Long templateId, String procInstanceId, ProcStatusEnum procStatusEnum) throws Exception {
        if(Objects.isNull(templateId) || Objects.isNull(procInstanceId) || Objects.isNull(procStatusEnum)) {
            throw new CustomException("更新流程表单状态，参数错误");
        }

        this.updateFormFieldData(templateId, procInstanceId, "bpmStatus", procStatusEnum.getValue(), false);
    }

    @Override
    @Transactional
    public ResponseInfo updateFormFieldData(Long templateId, String procInstanceId, String fieldName, Object data, boolean append) throws Exception {
        return this.updateFormFieldData(templateId, procInstanceId, fieldName, data, append, false);
    }

    @Override
    @Transactional
    public ResponseInfo updateFormFieldData(Long templateId, String procInstanceId, String fieldName, Object data, boolean append, boolean insertBefore) throws Exception {
        if(Objects.isNull(templateId) || Objects.isNull(procInstanceId) || StringUtils.isBlank(fieldName) || Objects.isNull(data)) {
            throw new CustomException("更新流程表单状态，参数错误");
        }

        WdFormTemplate template = wdFormTemplateRepository.getOne(templateId);
        Class clazz = null;
        try {
            clazz = Class.forName(template.getClassName());
        }catch (ClassNotFoundException e) {
            log.warn("updateFormFieldData 表单没有配置实体类");
            return new ResponseInfo(ResultCodeEnum.DATA_NOT_FOUND.getCode(), "表单没有配置实体类");
        }

        String clazzName = clazz.getSimpleName();
        clazzName = clazzName.substring(0, 1).toLowerCase() + clazzName.substring(1);
        JpaRepository beanRepository = SpringContextHolder.getBean(clazzName+"Repository");

        // 根据流程实例编号获取表单数据
        Object formBean = this.queryFormData(template.getClassName(), "bpmInstanceId", procInstanceId);
        if(Objects.isNull(formBean)) {
            throw new CustomException("更新流程表单状态，参数错误");
        }

        // 更新状态
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(formBean));
        this.updateVariableFormFieldData(jsonObject, fieldName, data, append, insertBefore);
        formBean = JSON.toJavaObject(jsonObject, clazz);
        beanRepository.saveAndFlush(formBean);
        return new ResponseInfo(ResultCodeEnum.SUCCESS.getCode(),
                ResultCodeEnum.SUCCESS.getMessageCN(), jsonObject);
    }

    @Override
    public void updateVariableFormFieldData(JSONObject formData, String fieldName, Object data, boolean append, boolean insertBefore) throws Exception {
        if(Objects.isNull(formData) || StringUtils.isBlank(fieldName) || Objects.isNull(data)) {
            throw new CustomException("更新流程表单状态，参数错误");
        }

        // 更新状态
        Object fieldObj = formData.get(fieldName);
        if(fieldObj instanceof String && FlowableUtils.isJSONValid(fieldObj.toString())) {
            fieldObj = JSONObject.parse((String) fieldObj);
        }
        if(append) {
            if(Objects.nonNull(fieldObj) && fieldObj instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray)fieldObj;
                if(insertBefore) {
                    jsonArray.add(0, data);
                }else {
                    jsonArray.add(data);
                }
                formData.replace(fieldName, jsonArray.toJSONString());
            }else {
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(data);
                formData.put(fieldName, jsonArray.toJSONString());
            }
        }else {
            // 处理数组，覆盖替换数组中数据
            if(Objects.nonNull(fieldObj) && fieldObj instanceof JSONArray && !(data instanceof String)) {
                JSONArray jsonArray = (JSONArray)fieldObj;
                JSONArray eachArray = (JSONArray)CloneUtils.clone(jsonArray);
                for (Object re : eachArray) {
                    JSONObject jo = (JSONObject) re;
                    Object objectBean = jo.toJavaObject(data.getClass());
                    if (objectBean.equals(data)) {
                        jsonArray.remove(re);
                    }
                }
                if(insertBefore) {
                    jsonArray.add(0, data);
                }else {
                    jsonArray.add(data);
                }
                formData.replace(fieldName, jsonArray.toJSONString());
            }else {
                if(data instanceof String) {
                    formData.put(fieldName, data);
                }else {
                    JSONArray jsonArray = new JSONArray();
                    jsonArray.add(data);
                    formData.put(fieldName, jsonArray.toJSONString());
                }
            }
        }
    }

    @Override
    @Transactional
    public ResponseInfo deleteFormFieldData(Long templateId, String procInstanceId, String fieldName, Object data, boolean insertBefore) throws Exception {
        if(Objects.isNull(templateId) || Objects.isNull(procInstanceId) || StringUtils.isBlank(fieldName) || Objects.isNull(data)) {
            throw new CustomException("删除流程表单数据，参数错误");
        }

        WdFormTemplate template = wdFormTemplateRepository.getOne(templateId);
        Class clazz = Class.forName(template.getClassName());

        String clazzName = clazz.getSimpleName();
        clazzName = clazzName.substring(0, 1).toLowerCase() + clazzName.substring(1);
        JpaRepository beanRepository = SpringContextHolder.getBean(clazzName+"Repository");

        // 根据流程实例编号获取表单数据
        Object formBean = this.queryFormData(template.getClassName(), "bpmInstanceId", procInstanceId);
        if(Objects.isNull(formBean)) {
            throw new CustomException("删除流程表单数据，参数错误");
        }

        // 更新状态
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(formBean));
        Object fieldObj = jsonObject.get(fieldName);
        if(fieldObj instanceof String && FlowableUtils.isJSONValid(fieldObj.toString())) {
            fieldObj = JSONObject.parse((String) fieldObj);
        }
        // 处理数组
        if(Objects.nonNull(fieldObj) && fieldObj instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray)fieldObj;
            JSONArray eachArray = (JSONArray)CloneUtils.clone(jsonArray);
            if(!insertBefore) {
                Collections.reverse(eachArray);
            }
            for (Object re : eachArray) {
                JSONObject jo = (JSONObject) re;
                Object objectBean = jo.toJavaObject(data.getClass());
                if (objectBean.equals(data)) {
                    jsonArray.remove(re);
                    break;
                }
            }
            jsonObject.replace(fieldName, jsonArray.toJSONString());
        }else if(data instanceof String) {
            jsonObject.remove(fieldName);
        }
        formBean = JSON.toJavaObject(jsonObject, clazz);
        beanRepository.save(formBean);
        return new ResponseInfo(ResultCodeEnum.SUCCESS.getCode(),
                ResultCodeEnum.SUCCESS.getMessageCN(), jsonObject);
    }

    @Override
    public boolean deleteFrom(Long id) {
        WdFormTemplate wdFormTemplate = wdFormTemplateRepository.getOne(id);
        //获取当前用户id
        UserDto userInfoDto = userService.findById(SecurityUtils.getCurrentUserId());
        // 判断是否是自己的表单，不是自己的则无法删除
        if(Objects.nonNull(wdFormTemplate) && wdFormTemplate.getUserId().equals(userInfoDto.getId())){
            //判断是否有流程绑定表单
            List<WorkflowDeployForm> workflowDeployFormList = workflowDeployFormRepository.findByFormId(id);
            if (!CollectionUtils.isEmpty(workflowDeployFormList)){
                return false;
            }
            wdFormTemplateRepository.deleteById(id);
            return true;
        }
        return false;
    }

    /**
     * 获取表单数据
     * @param fullClazz 全路径类名
     * @param queryField 查询字段
     * @param queryValue 查询值
     * @return
     * @throws Exception
     */
    private Object queryFormData(String fullClazz, String queryField, String queryValue) throws Exception {
        Class clazz = Class.forName(fullClazz);

        String clazzName = clazz.getSimpleName();
        clazzName = clazzName.substring(0, 1).toLowerCase() + clazzName.substring(1);
        JpaRepository beanRepository = SpringContextHolder.getBean(clazzName+"Repository");

        // 根据流程实例编号获取表单数据
        Object queryObj = clazz.newInstance();
        if(StringUtils.isNotBlank(queryField)) {
            JSONObject queryJsonObject = JSONObject.parseObject(JSON.toJSONString(queryObj));
            // 默认过滤Null值查询，需要有值才能查询
            if(StringUtils.isBlank(queryValue)) {
                queryValue = "-1";
            }
            queryJsonObject.put(queryField, queryValue);
            queryObj = JSON.toJavaObject(queryJsonObject, clazz);
        }
        Example<Object> example = Example.of(queryObj);
        try {
            Optional optional = beanRepository.findOne(example);
            if(optional.isPresent()) {
                return optional.get();
            }
        }catch (ConcurrencyFailureException e) {
            throw new CustomException("数据被更新，请刷新页面后重新提交");
        }
        return null;
    }
}
