package com.wangda.oa.modules.workflow.service.workflow.impl;

import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/6
 * @description
 */
@Service
@Slf4j
public class MultiInstanceCompleter {

    /**
     * 会签是否都已经审批完成
     * @param execution
     * @return
     */
    public boolean isComplete(ExecutionEntity execution) {
        Integer completeCounter = (Integer) execution.getVariable(ProcessConstants.NUMBER_OF_COMPLETED_INSTANCES); // 完成会签的次数
        Integer instanceOfNumbers = (Integer) execution.getVariable(ProcessConstants.NUMBER_OF_INSTANCES); // 总循环次数

        String tmpTaskBackFlag = (String) execution.getVariableLocal("tmpTaskBackFlag"); // 是否收回标志，1表示收回
        if ("1".equals(tmpTaskBackFlag)) {
            return true;
        }

        return completeCounter >= instanceOfNumbers;
    }
}
