package com.wangda.oa.modules.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/25 下午3:42
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricProcessBO {

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "时间范围")
    private List<String> timeRange;

    @ApiModelProperty(value = "流程名")
    private String name;

    @ApiModelProperty(value = "公文类型key")
    private String processDefinitionKey;

    @ApiModelProperty(value = "实例类型type（1:公文管理,2:行政服务）")
    private Integer processDefinitionType;

    @ApiModelProperty(value = "状态(0:待办,1:已办)")
    private Integer status;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "年份")
    private Integer year;
}
