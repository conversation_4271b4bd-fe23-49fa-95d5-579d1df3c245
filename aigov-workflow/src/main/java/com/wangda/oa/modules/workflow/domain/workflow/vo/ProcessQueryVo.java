package com.wangda.oa.modules.workflow.domain.workflow.vo;

import com.wangda.boot.platform.base.Pagination;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> lee
 * @projectName : flowable
 * @description: 流程查询VO
 * @date : 2021/05/23
 */
@Data
@NoArgsConstructor
public class ProcessQueryVo extends Pagination {

    /**
     * 用户名
     */
    private String username;

    /**
     * 时间范围
     */
    private List<String> timeRange;

    /**
     * 流程名
     */
    private String name;

    /**
     * 流程定义key
     */
    private String procDefKey;

    /**
     * 流程类别
     */
    private String category;

    /**
     * 表单标题
     */
    private String formTitle;

    /**
     * 流程状态
     */
    private String procStatus;

    /**
     * 办理人
     */
    private String assigneeName;
}
