package com.wangda.oa.modules.workflow.factory;

import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27
 * @description
 */
public interface IFlowFormHandleFactory {

    /**
     * 处理我们工作
     * @param myWork
     * @param myWorkDto
     */
    void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto);

    /**
     * 根据流程定义中标题规则解析
     * @param formDataObj
     * @param subjectRule
     * @return
     */
    String handleSubjectRule(JSONObject formDataObj, String subjectRule);

    /**
     * 处理获取表单数据
     * @param procDefKey
     * @param procInstanceId
     * @param taskDefKey
     * @param bpmFormData
     * @return
     */
    Object handleFormRecord(String procDefKey, String procInstanceId, String taskDefKey, JSONObject bpmFormData);

    /**
     * 处理流程变量缓存
     * @param taskId 任务id
     * @param procDefkey 流程定义key
     * @param variables 流程变量
     * @param formDataObj 表单数据
     * @return
     */
    void handleAddVariables(String taskId, String procDefkey, Map<String, Object> variables, JSONObject formDataObj);

    /**
     * 删除表单记录
     * @param procDefKey
     * @param instanceId
     */
    void deleteFormRecord(String procDefKey, String instanceId);

    /**
     * 扩展业务处理
     * @param procInstanceId 流程变量
     * @param procDefKey 流程定义key
     * @param procStatus 流程状态
     * @return void
     */
    default void handleFlowBizData(String procInstanceId, String procDefKey, ProcStatusEnum procStatus) {
    }
}
