package com.wangda.oa.modules.workflow.service.workflow.impl;


import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.modules.extension.bo.PositionListBO;
import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.enums.PositionEnum;
import com.wangda.oa.modules.extension.service.SysDeptUserPositionService;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.RoleService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.RoleDto;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.mapstruct.UserMapperUtil;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.modules.workflow.bo.flow.GetInfoByKeyBO;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import com.wangda.oa.modules.workflow.domain.workflow.BpmTaskUserRead;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskFormRw;
import com.wangda.oa.modules.workflow.domain.workflow.vo.*;
import com.wangda.oa.modules.workflow.dto.AlreadyUserListBO;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.BacklogUserDto;
import com.wangda.oa.modules.workflow.dto.HistoricProcessBO;
import com.wangda.oa.modules.workflow.dto.workflow.*;
import com.wangda.oa.modules.workflow.enums.definition.WdTaskExecTypeEnum;
import com.wangda.oa.modules.workflow.enums.workflow.AttachmentTypeEnum;
import com.wangda.oa.modules.workflow.enums.workflow.CategoryTypeEnum;
import com.wangda.oa.modules.workflow.enums.workflow.ProcStatusEnum;
import com.wangda.oa.modules.workflow.enums.workflow.TaskCommentTypeEnum;
import com.wangda.oa.modules.workflow.factory.*;
import com.wangda.oa.modules.workflow.repository.form.WdFormTemplateRepository;
import com.wangda.oa.modules.workflow.repository.workflow.BpmTaskUserReadRepository;
import com.wangda.oa.modules.workflow.service.form.FormTemplateService;
import com.wangda.oa.modules.workflow.service.workflow.FlowInstanceService;
import com.wangda.oa.modules.workflow.service.workflow.FlowTaskService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowCustomExtensionService;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowUserTaskExtensionService;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEventType;
import org.flowable.common.engine.api.query.NativeQuery;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.engine.delegate.event.impl.FlowableEventBuilder;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.bpmn.behavior.MultiInstanceActivityBehavior;
import org.flowable.engine.impl.persistence.entity.*;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.hibernate.query.criteria.internal.OrderImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.concurrent.DelegatingSecurityContextRunnable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.text.ParseException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-04-03
 **/
@Service
@Slf4j
public class FlowTaskServiceImpl extends FlowServiceFactory implements FlowTaskService {

    @Resource
    private UserService sysUserService;

    @Resource
    private RoleService sysRoleService;

    @Resource
    private WdFormTemplateRepository wdFormTemplateRepository;

    @Resource
    private WorkflowUserTaskExtensionService workflowUserTaskExtensionService;

    @Autowired
    private UserMapperUtil userMapperUtil;

    @Autowired
    private FormTemplateService formTemplateService;

    @Autowired
    private SysDeptUserPositionService sysDeptUserPositionService;

    @Resource
    private IFlowFormHandleFactory flowFormHandleFactory;

    @Resource
    private ProcessELService processELService;

    @Resource
    private FlowInstanceService flowInstanceService;

    @Resource
    private WorkflowCustomExtensionService workflowCustomExtensionService;

    @Resource
    private BpmTaskUserReadRepository bpmTaskUserReadRepository;

    @Resource
    private RestUrlComponent restUrlComponent;

    @Resource
    private AfterTransactionOpt afterTransactionOpt;

    @PersistenceContext
    EntityManager entityManager;

    private final String ymdhmFormat = "yyyy-MM-dd HH:mm";

    /**
     * 完成任务
     * @param task 请求实体参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void completeTask(FlowTaskVo task) throws Exception {
        String taskId = task.getBpmData().getTaskId();
        ProcessInstance processInstance =
                runtimeService.createProcessInstanceQuery().processInstanceId(task.getBpmData().getProcessInstanceId()).includeProcessVariables().singleResult();

        Task myTask = taskService.createTaskQuery().taskId(taskId).singleResult();
        if(Objects.isNull(processInstance) || Objects.isNull(myTask)) {
            throw new CustomException("任务已办理或已撤回");
        }
        if(myTask.isSuspended()) {
            throw new CustomException("该任务已锁定，暂无法办理");
        }

        String currentUsername = task.getBpmData().getUsername();
        if(StringUtils.isBlank(currentUsername)) {
            currentUsername = SecurityUtils.getCurrentUsername();
        }
        UserDto userDto = sysUserService.findByName(currentUsername);

        if(!workflowCustomExtensionService.isBpmAdmin() && StringUtils.isNotEmpty(myTask.getAssignee()) && !(myTask.getAssignee().equals(currentUsername))) {
            log.warn("completeTask用户任务不匹配：taskId: " + taskId + " assignee:" + currentUsername);
            throw new CustomException("该任务不是您办理，请重新刷新页面或联系管理员");
        }

        // 业务校验
        String taskNodes = task.getBpmData().taskNodesToString();
        JSONObject formDataObj = null;
        if(Objects.nonNull(task.getFormData())) {
            String formData = JSON.toJSONString(task.getFormData());
            formDataObj = JSONObject.parseObject(formData);
            ResultJson validateResult = workflowCustomExtensionService.validateFormData(myTask.getProcessInstanceId(), processInstance.getProcessDefinitionKey(), myTask, taskNodes, formDataObj);
            if(!validateResult.isSuccess()) {
                throw new CustomException(validateResult.getMessage());
            }
        }

        // 变量信息
        Map<String, Object> variables = processInstance.getProcessVariables();

        // bpm定义模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        Process process = bpmnModel.getProcess(null);

        // 当前节点
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(myTask.getTaskDefinitionKey());

        String nextAssignee = task.getBpmData().taskAssigneeFormatToString();
        if(StringUtils.isBlank(nextAssignee)) {
            boolean hasNextTaskFlowNode = this.hasNextTaskFlowNode(bpmnModel, myTask.getTaskDefinitionKey(), null, myTask.getExecutionId());
            if(hasNextTaskFlowNode && processELService.isLastAssignee(bpmnModel, myTask)) {
                // 从预签中获取后续处理人
                String preSignAssignee = workflowCustomExtensionService.getNextTaskPreSignAssignee(processInstance, myTask.getTaskDefinitionKey(), bpmnModel);
                if(StringUtils.isNotEmpty(preSignAssignee)) {
                    nextAssignee = preSignAssignee;
                    taskNodes = String.join(ElAdminConstant.SEPARATOR_COMMA, FlowableUtils.parseNextTaskNode(preSignAssignee));
                }else {
                    throw new CustomException("该流程后续办理人没有，暂不能提交，请刷新页面后再次提交");
                }
            }
        }

        // 意见
        String remark = task.getBpmData().getRemark();
        if(StringUtils.isEmpty(remark)) {
            remark = "";
        }else {
            remark = remark.trim();
        }
        variables.put(ProcessConstants.BPM_BPM_APPROVALREMARK, remark);

        // 提交节点设置
        if(Objects.isNull(variables.get(ProcessConstants.BPM_BPM_STARTEDBY))) {
            variables.put(ProcessConstants.BPM_BPM_STARTEDBY, userDto.getUsername());
            runtimeService.updateBusinessStatus(processInstance.getProcessInstanceId(), ProcStatusEnum.INPROGRESS.getValue());
            taskService.addComment(taskId, task.getBpmData().getProcessInstanceId(), TaskCommentTypeEnum.TJ.name(), remark);
        }else {
            taskService.addComment(taskId, task.getBpmData().getProcessInstanceId(), TaskCommentTypeEnum.SP.name(), remark);
        }

        // 意见附件
        String remarkAttachment = task.getBpmData().getRemarkAttachment();
        if(StringUtils.isNotBlank(remarkAttachment)) {
            taskService.createAttachment(AttachmentTypeEnum.YJFJ.name(), taskId, processInstance.getProcessInstanceId(), AttachmentTypeEnum.YJFJ.getName(), null, remarkAttachment);
        }

        // 手写签名
        if(StringUtils.isNotBlank(task.getBpmData().getSignature())) {
            // String attachmentType, String taskId, String processInstanceId, String attachmentName, String attachmentDescription, String url
            taskService.createAttachment(AttachmentTypeEnum.SXQM.name(), taskId, processInstance.getProcessInstanceId(), AttachmentTypeEnum.SXQM.getName(), null, task.getBpmData().getSignature());
        }

        // 处理表单数据
        if(Objects.nonNull(formDataObj) && !formDataObj.isEmpty()) {
            if(StringUtils.isEmpty(formDataObj.getString("bpmStatus"))) {
                formDataObj.put("bpmStatus", ProcStatusEnum.INPROGRESS.getValue());
            }
            if(StringUtils.isEmpty(formDataObj.getString("bpmProcessKey"))) {
                formDataObj.put("bpmProcessKey", processInstance.getProcessDefinitionKey());
            }
            if(StringUtils.isEmpty(formDataObj.getString("bpmInstanceId"))) {
                formDataObj.put("bpmInstanceId", task.getBpmData().getProcessInstanceId());
            }

            // 处理流程标题自定义属性
            String title = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_BACKLOG_SUBJECT_RULE);
            variables.put(ProcessConstants.BPM_FORM_TITLE, flowFormHandleFactory.handleSubjectRule(formDataObj, title));

            if(StringUtils.isEmpty(formDataObj.getString("bpmSubject"))) {
                formDataObj.put("bpmSubject", variables.get(ProcessConstants.BPM_FORM_TITLE));
            }

            String review = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_BACKLOG_DOCUMENT_REVIEW);
            if(StringUtils.isNotEmpty(review) && !variables.containsKey(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW)) {
                variables.put(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, formDataObj.getString(review));
            }

            // 流程缓存处理
            flowFormHandleFactory.handleAddVariables(taskId, processInstance.getProcessDefinitionKey(), variables, formDataObj);

            ResponseInfo responseInfo = formTemplateService.formSubmit(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), formDataObj.toJSONString(), taskId);
            variables.put(ProcessConstants.BPM_FORM_DATA, formDataObj);
        }

        // 预签处理信息
        String preSign = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_PRESIGN);
        if(StringUtils.isNotBlank(preSign)) {
            String preSignAssignee = task.getBpmData().preSignAssigneeFormatToString();
            if(Objects.nonNull(preSignAssignee)) {
                // 预签新的追加相同覆盖
                Map<String, String> preSignAssigneeMap = FlowableUtils.formatPreSignAssignee(preSignAssignee);

                Object preSignAssigneeVar = variables.get(ProcessConstants.BPM_BPM_PRESIGNASSIGNEE);
                if(Objects.nonNull(preSignAssigneeVar)) {
                    Map<String, String> preSignAssigneeVarMap = FlowableUtils.formatPreSignAssignee(preSignAssigneeVar.toString());
                    preSignAssigneeVarMap.putAll(preSignAssigneeMap);
                    preSignAssigneeMap = preSignAssigneeVarMap;
                }

                StringBuilder assigneeMerge = new StringBuilder();
                preSignAssigneeMap.forEach((key, value) -> {
                    assigneeMerge.append(key).append(ElAdminConstant.SEPARATOR_COLON);
                    if(StringUtils.isNotEmpty(value)) {
                        assigneeMerge.append(value);
                    }
                    assigneeMerge.append(ElAdminConstant.SEPARATOR_POUND);
                });
                if(assigneeMerge.length() > 0) {
                    assigneeMerge.deleteCharAt(assigneeMerge.length() - 1);
                }
                variables.put(ProcessConstants.BPM_BPM_PRESIGNASSIGNEE, assigneeMerge.toString());
            }
        }
        String preSignRemark = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_PRESIGNREMARK);
        if(StringUtils.isNotBlank(preSignRemark) && preSignRemark.equals("true") && StringUtils.isNotBlank(task.getBpmData().getRemark())) {
            variables.put(ProcessConstants.BPM_BPM_PRESIGNREMARK, task.getBpmData().getRemark());
        }
        // 预签抄送处理信息
        String preSignCopyTo = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_PRESIGNCOPYTO);
        if(StringUtils.isNotBlank(preSignCopyTo)) {
            String preSignCopyToAssignee = task.getBpmData().preSignCopyToAssigneeFormatToString();
            if(Objects.nonNull(preSignCopyToAssignee)) {
                variables.put(ProcessConstants.BPM_BPM_PRESIGNCOPYTOASSIGNEE, preSignCopyToAssignee);
            }
        }

        // 意见上表单
        boolean remarkExclude = false;
        String remarkExcludeRegex = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKEXCLUDEREGEX);
        if(StringUtils.isNotEmpty(remarkExcludeRegex)) {
            Pattern pattern = Pattern.compile(remarkExcludeRegex);
            if(pattern.matcher(remark).matches()) {
                remarkExclude = true;
            }
        }

        if(!remarkExclude) {
            String remarkToFormField = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
            if(StringUtils.isNotBlank(remarkToFormField)) {
                FlowFormRemarkDto flowFormRemarkDto = FlowFormRemarkDto.builder()
                        .remark(remark)
                        .username(userDto.getUsername())
                        .nickName(userDto.getNickName())
                        .deptId(userDto.getDeptId())
                        .deptName(userDto.getDept().getName())
                        .processedTime(new Date())
                        .fj(task.getBpmData().getRemarkAttachment())
                        .build();

                boolean append = true;
                String remarkCoverd = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKCOVERD);
                if(StringUtils.isNotBlank(remarkCoverd) && Boolean.parseBoolean(remarkCoverd)) {
                    append = false;
                }

                String processRemarkSort = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_REMARK_SORT);
                boolean insertBefore = false;
                if(StringUtils.isNotBlank(processRemarkSort)) {
                    insertBefore = Boolean.parseBoolean(processRemarkSort);
                }
                ResponseInfo responseInfo = formTemplateService.updateFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), processInstance.getProcessInstanceId(), remarkToFormField, flowFormRemarkDto, append, insertBefore);
                JSONObject formData = (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA);
                if(ResultCodeEnum.SUCCESS.getCode() == responseInfo.getCode()) {
                    // 更新意见上表单数据
                    JSONObject jsonObject = (JSONObject) responseInfo.getData();
                    List<String> remarkToFormFieldList = FlowableUtils.findAllRemarkToFormField(process);
                    for(String key : remarkToFormFieldList) {
                        if(jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
                            formData.put(key, jsonObject.get(key));
                        }
                    }
                }else {
                    formTemplateService.updateVariableFormFieldData(formData, remarkToFormField, flowFormRemarkDto, append, insertBefore);
                }
                variables.replace(ProcessConstants.BPM_FORM_DATA, formData);
            }
        }

        // 设置当前任务处理人员
        if(StringUtils.isBlank(myTask.getAssignee())) {
            taskService.setAssignee(taskId, userDto.getUsername());
        }

        // 获取后续节点
        variables.put(ProcessConstants.BPM_TASK_DEF_KEY, taskNodes);

        // 多个多实例节点需汇聚后提交
        if(FlowableUtils.hasMultiInstanceNode(bpmnModel, taskNodes)) {
            variables.put(ProcessConstants.BPM_TASK_ASSIGNEE_CACHE, nextAssignee);
        }

        // 设置后续节点处理人
        if(Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
            Boolean isCompletion = processELService.completionConditionSatisfied(myTask.getExecutionId(), (MultiInstanceActivityBehavior) currentFlowNode.getBehavior());
            if(isCompletion) {
                runtimeService.setVariable(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_ASSIGNEE, nextAssignee);

                // 设置当前节点接收人
                runtimeService.setVariableLocal(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_TO_ASSIGNEES + ElAdminConstant.SEPARATOR_UNDERLINE + myTask.getId(), nextAssignee);
            }else {
                // 多实例任务完成后需要重新调用集合表达式校验，所以需要重新覆盖处理人
                Object currentAssignee = variables.get(ProcessConstants.BPM_TASK_ASSIGNEE_CACHE);
                if(Objects.isNull(currentAssignee) || currentAssignee.toString().indexOf(myTask.getTaskDefinitionKey()) == -1) {
                    currentAssignee = runtimeService.getVariable(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_ASSIGNEE);
                }
                variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, currentAssignee);
            }
        }else {
            variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, nextAssignee);

            // 设置当前节点接收人
            runtimeService.setVariableLocal(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_TO_ASSIGNEES + ElAdminConstant.SEPARATOR_UNDERLINE + myTask.getId(), nextAssignee);
        }

        Date currentDate = new Date();
        taskService.complete(taskId, variables);

        // 当前节点是否还有本人任务，合并办理
        if(StringUtils.isNotEmpty(myTask.getAssignee())) {
            List<Task> otherTaskList = taskService.createTaskQuery().processInstanceId(myTask.getProcessInstanceId()).taskDefinitionKey(myTask.getTaskDefinitionKey()).taskAssignee(myTask.getAssignee()).taskCreatedBefore(currentDate).list();
            if(!CollectionUtils.isEmpty(otherTaskList)) {
                for(Task otherTask : otherTaskList) {
                    if(!otherTask.getId().equals(myTask.getId())) {
                        taskService.complete(otherTask.getId());
                    }
                }
            }
        }
    }

    // 手动新建用户任务，后续节点合并相同任务在表达式中判断
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributeNewTask(Task task, FlowNode currentFlowNode, String assignee) {

        // 执行实例
        ExecutionEntityImpl executionEntity = new ExecutionEntityImpl();
        executionEntity.setId(String.valueOf(IdWorker.generateId()));
        executionEntity.setParentId(task.getProcessInstanceId());
        executionEntity.setProcessDefinitionId(task.getProcessDefinitionId());
        executionEntity.setProcessInstanceId(task.getProcessInstanceId());
        executionEntity.setRootProcessInstanceId(task.getProcessInstanceId());
        executionEntity.setActivityId(currentFlowNode.getId());
        // 激活状态
        executionEntity.setActive(true);
        executionEntity.setCountEnabled(true);
        // 是否暂停
        executionEntity.setSuspensionState(1);
        executionEntity.setStartTime(new Date());
        // 是否该实例正在执行一个作用域活动，主实例1，子实例0
        executionEntity.setScope(false);
        executionEntity.setTaskCount(1);
        // 更新次数
        executionEntity.setRevision(1);
        managementService.executeCommand(new CreateExecutionCmd(executionEntity));

        // 任务实例
        ActivityInstanceEntity activityInstanceEntity = new ActivityInstanceEntityImpl();
        activityInstanceEntity.setId(String.valueOf(IdWorker.generateId()));

        TaskEntityImpl newTask = (TaskEntityImpl) taskService.newTask(String.valueOf(IdWorker.generateId()));
        newTask.setAssignee(assignee);
        newTask.setName(currentFlowNode.getName());
        newTask.setTaskDefinitionKey(currentFlowNode.getId());
        newTask.setProcessInstanceId(task.getProcessInstanceId());
        newTask.setProcessDefinitionId(task.getProcessDefinitionId());
        newTask.setExecutionId(executionEntity.getId());
        taskService.saveTask(newTask);

        // 活动实例
        activityInstanceEntity.setProcessDefinitionId(task.getProcessDefinitionId());
        activityInstanceEntity.setProcessInstanceId(task.getProcessInstanceId());
        activityInstanceEntity.setExecutionId(executionEntity.getId());
        activityInstanceEntity.setActivityId(currentFlowNode.getId());
        activityInstanceEntity.setTaskId(newTask.getId());
        activityInstanceEntity.setActivityName(currentFlowNode.getName());
        activityInstanceEntity.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);
        activityInstanceEntity.setAssignee(assignee);
        activityInstanceEntity.setStartTime(new Date());
        activityInstanceEntity.setTransactionOrder(2);
        managementService.executeCommand(new CreateActivityInstanceCmd(activityInstanceEntity));

        HistoricActivityInstanceEntity hisActivityInstanceEntity = new HistoricActivityInstanceEntityImpl();
        BeanUtils.copyProperties(activityInstanceEntity, hisActivityInstanceEntity);
        managementService.executeCommand(new CreateHistoricActivityInstanceCmd(hisActivityInstanceEntity));
    }

    /**
     * 拒绝任务
     * @param flowTaskVo
     */
    @Override
    @Transactional
    public void taskReject(FlowTaskVo flowTaskVo) {
        String taskId = flowTaskVo.getBpmData().getTaskId();

        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(taskId).includeProcessVariables().singleResult();
        if(Objects.isNull(task) || task.isSuspended()) {
            throw new CustomException("任务已完成或处于锁定状态");
        }

        // 设置拒绝意见
        String remark = flowTaskVo.getBpmData().getRemark();
        if(StringUtils.isEmpty(remark)) {
            remark = "";
        }
        taskService.addComment(taskId, task.getProcessInstanceId(), TaskCommentTypeEnum.JJ.name(), remark);

        // 设置流程状态为拒绝
        Map<String, Object> variables = task.getProcessVariables();
        runtimeService.updateBusinessStatus(task.getProcessInstanceId(), ProcStatusEnum.JJ.getValue());
        // 设置表单状态
        try {
            formTemplateService.updateFormStatus(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), task.getProcessInstanceId(), ProcStatusEnum.JJ);
        }catch(Exception e) {
            throw new FlowableException("更新流程表单状态出错", e);
        }

        // 结束节点
        List<Execution> executions = runtimeService.createExecutionQuery().parentId(task.getProcessInstanceId()).list();
        List<String> executionIds = new ArrayList<>();
        executions.forEach(execution -> executionIds.add(execution.getId()));

        //1、获取终止节点
        List<EndEvent> endNodes = findEndFlowElement(task.getProcessDefinitionId());
        String endId = endNodes.get(0).getId();

        //2、当前任务执行终止
        runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(task.getProcessInstanceId())
                .processVariables(variables)
                .moveExecutionsToSingleActivityId(executionIds, endId)
                .changeState();
    }

    /**
     * 退回任务
     * @param flowTaskVo 请求实体参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void taskReturn(FlowTaskVo flowTaskVo) {
        String taskId = flowTaskVo.getBpmData().getTaskId();

        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(taskId).includeProcessVariables().singleResult();
        if(Objects.isNull(task) || task.isSuspended()) {
            throw new CustomException("任务已完成或处于锁定状态");
        }

        String nextAssignee = flowTaskVo.getBpmData().taskAssigneeFormatToString();
        if(StringUtils.isEmpty(nextAssignee)) {
            throw new CustomException("退回节点处理人没有");
        }

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        // 业务校验
        JSONObject formDataObj = null;
        if(Objects.nonNull(flowTaskVo.getFormData())) {
            String formData = JSON.toJSONString(flowTaskVo.getFormData());
            formDataObj = JSONObject.parseObject(formData);
            ResultJson validateResult = workflowCustomExtensionService.validateFormData(task.getProcessInstanceId(), processInstance.getProcessDefinitionKey(), formDataObj);
            if(!validateResult.isSuccess()) {
                throw new CustomException(validateResult.getMessage());
            }
        }

        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        Process process = bpmnModel.getProcess(null);

        // 当前节点
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());

        Map variables = task.getProcessVariables();
        String currentUsername = flowTaskVo.getBpmData().getUsername();
        if(StringUtils.isBlank(currentUsername)) {
            currentUsername = SecurityUtils.getCurrentUsername();
        }
        UserDto userDto = sysUserService.findByName(currentUsername);

        // 保存表单数据
        if(Objects.nonNull(formDataObj) && !formDataObj.isEmpty()) {

            // 处理表单数据
            if(StringUtils.isEmpty(formDataObj.getString("bpmInstanceId"))) {
                formDataObj.put("bpmInstanceId", task.getProcessInstanceId());
            }
            if(StringUtils.isEmpty(formDataObj.getString("bpmProcessKey"))) {
                formDataObj.put("bpmProcessKey", processInstance.getProcessDefinitionKey());
            }

            // 处理流程标题自定义属性
            String title = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_BACKLOG_SUBJECT_RULE);
            if(StringUtils.isNotEmpty(title)) {
                variables.put(ProcessConstants.BPM_FORM_TITLE, flowFormHandleFactory.handleSubjectRule(formDataObj, title));
            }

            if(StringUtils.isEmpty(formDataObj.getString("bpmSubject"))) {
                formDataObj.put("bpmSubject", variables.get(ProcessConstants.BPM_FORM_TITLE));
            }

            try {
                ResponseInfo responseInfo = formTemplateService.formSubmit(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), formDataObj.toJSONString(), taskId);
                variables.put(ProcessConstants.BPM_FORM_DATA, formDataObj);
            }catch(Exception e) {
                throw new CustomException("更新业务表单数据错误");
            }
        }

        // 设置意见
        String remark = flowTaskVo.getBpmData().getRemark();
        if(StringUtils.isEmpty(remark)) {
            remark = "";
        }
        taskService.addComment(taskId, task.getProcessInstanceId(), TaskCommentTypeEnum.TH.name(), remark);

        // 意见上表单
        String remarkToFormField = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
        if(StringUtils.isNotBlank(remarkToFormField)) {
            FlowFormRemarkDto flowFormRemarkDto = FlowFormRemarkDto.builder()
                    .remark(remark)
                    .username(userDto.getUsername())
                    .nickName(userDto.getNickName())
                    .deptId(userDto.getDeptId())
                    .deptName(userDto.getDept().getName())
                    .processedTime(new Date())
                    .fj(flowTaskVo.getBpmData().getRemarkAttachment())
                    .build();

            boolean append = true;
            String remarkCoverd = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKCOVERD);
            if(StringUtils.isNotBlank(remarkCoverd) && Boolean.parseBoolean(remarkCoverd)) {
                append = false;
            }

            String processRemarkSort = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_REMARK_SORT);
            boolean insertBefore = false;
            if(StringUtils.isNotBlank(processRemarkSort)) {
                insertBefore = Boolean.parseBoolean(processRemarkSort);
            }

            try {
                ResponseInfo responseInfo = formTemplateService.updateFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), task.getProcessInstanceId(), remarkToFormField, flowFormRemarkDto, append, insertBefore);
                JSONObject formData = (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA);
                if(ResultCodeEnum.SUCCESS.getCode() == responseInfo.getCode()) {
                    // 更新意见上表单数据
                    JSONObject jsonObject = (JSONObject) responseInfo.getData();
                    List<String> remarkToFormFieldList = FlowableUtils.findAllRemarkToFormField(process);
                    for(String key : remarkToFormFieldList) {
                        if(jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
                            formData.put(key, jsonObject.get(key));
                        }
                    }
                }else {
                    formTemplateService.updateVariableFormFieldData(formData, remarkToFormField, flowFormRemarkDto, append, insertBefore);
                }
                variables.replace(ProcessConstants.BPM_FORM_DATA, formData);
            }catch(Exception e) {
                log.error(e.getMessage());
                throw new CustomException("更新业务表单数据错误");
            }
        }

        if(StringUtils.isEmpty(task.getAssignee())) {
            task.setAssignee(SecurityUtils.getCurrentUsername());
        }
        String taskNodes = flowTaskVo.getBpmData().taskNodesToString();
        List<String> activityIds = Lists.newArrayList(taskNodes.split(ElAdminConstant.SEPARATOR_COMMA).clone());

        // 退回节点
        variables.put(ProcessConstants.BPM_TASK_DEF_KEY, taskNodes);

        // 设置后续节点处理人
        variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, nextAssignee);

        // 设置当前节点接收人
        runtimeService.setVariableLocal(task.getProcessInstanceId(), ProcessConstants.BPM_TASK_TO_ASSIGNEES + ElAdminConstant.SEPARATOR_UNDERLINE + task.getId(), nextAssignee);

        try {
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(task.getProcessInstanceId())
                    .processVariables(variables)
                    .moveSingleActivityIdToActivityIds(task.getTaskDefinitionKey(), activityIds)
                    .changeState();
        }catch(FlowableException e) {
            log.error(e.getMessage());
            throw new CustomException("无法退回，请联系管理员");
        }
    }

    /**
     * 退回首环节
     * @param flowTaskVo 请求实体参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void taskReturnFirst(FlowTaskVo flowTaskVo) {
        String taskId = flowTaskVo.getBpmData().getTaskId();

        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if(Objects.isNull(task)) {
            throw new CustomException("任务已完成或已处于驳回状态");
        }else if(task.isSuspended()) {
            throw new CustomException("任务处于锁定状态");
        }

        List<HistoricActivityInstance> historicActivityInstances = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .activityType(BpmnXMLConstants.ELEMENT_TASK_USER)
                .finished()
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list();

        if(CollectionUtils.isEmpty(historicActivityInstances)) {
            throw new CustomException("该流程还未执行");
        }

        // 判断是否在流程定义中是上个节点
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());

        // 多实例节点若驳回时该环节已有人办理过则提示“无法驳回，可加签或转办”
        if(Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
            MultiInstanceActivityBehavior multiInstanceActivityBehavior = (MultiInstanceActivityBehavior) currentFlowNode.getBehavior();
            Integer nrOfCompletedInstances = managementService.executeCommand(new GetMiExecutionCompletedCmd(task.getExecutionId(), multiInstanceActivityBehavior));
            if(nrOfCompletedInstances > 0) {
                throw new CustomException("该环节已有人办理过，不能驳回");
            }
        }

        HistoricActivityInstance activityInstance = historicActivityInstances.get(0);
        TaskAssigneeVo taskAssigneeVo = new TaskAssigneeVo();
        taskAssigneeVo.setTaskKey(activityInstance.getActivityId());
        taskAssigneeVo.setUsername(activityInstance.getAssignee());

        // 设置回退节点用户
        List<TaskAssigneeVo> taskAssignee = new ArrayList<>();
        taskAssignee.add(taskAssigneeVo);

        FlowBpmVo flowBpmVo = flowTaskVo.getBpmData();
        flowBpmVo.setTaskAssignee(taskAssignee);
        flowBpmVo.setTargetTaskKey(activityInstance.getActivityId());
        this.taskReturn(flowTaskVo);
    }

    /**
     * 退回上一环节
     * @param flowTaskVo 请求实体参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void taskReturnLast(FlowTaskVo flowTaskVo) {
        String taskId = flowTaskVo.getBpmData().getTaskId();

        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if(Objects.isNull(task)) {
            throw new CustomException("任务已完成或已处于驳回状态");
        }else if(task.isSuspended()) {
            throw new CustomException("任务处于锁定状态");
        }

        List<HistoricActivityInstance> historicSequenceFlowList = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .activityType(BpmnXMLConstants.ELEMENT_SEQUENCE_FLOW)
                .finished()
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();

        if(CollectionUtils.isEmpty(historicSequenceFlowList)) {
            throw new CustomException("该流程还未执行");
        }

        // 判断是否在流程定义中是上个节点
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());

        // 多实例节点若驳回时该环节已有人办理过则提示“无法驳回，可加签或转办”
        if(Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
            MultiInstanceActivityBehavior multiInstanceActivityBehavior = (MultiInstanceActivityBehavior) currentFlowNode.getBehavior();
            Integer nrOfCompletedInstances = managementService.executeCommand(new GetMiExecutionCompletedCmd(task.getExecutionId(), multiInstanceActivityBehavior));
            if(nrOfCompletedInstances > 0) {
                throw new CustomException("该环节已有人办理过，不能驳回");
            }
        }

        List<SequenceFlow> roads = FlowableUtils.findIncomingSequenceFlows(currentFlowNode, null, null);
        List<HistoricActivityInstance> activityInstanceList = this.getPrevFlowNode(task.getProcessInstanceId(), roads, historicSequenceFlowList);
        if(CollectionUtils.isEmpty(activityInstanceList)) {
            throw new CustomException("没有上一环节完成的任务，无法退回");
        }

        // 设置回退节点用户
        List<TaskAssigneeVo> taskAssignee = new ArrayList<>();
        for(HistoricActivityInstance activityInstance : activityInstanceList) {
            TaskAssigneeVo taskAssigneeVo = new TaskAssigneeVo();
            taskAssigneeVo.setTaskKey(activityInstance.getActivityId());
            taskAssigneeVo.setUsername(activityInstance.getAssignee());
            taskAssignee.add(taskAssigneeVo);
        }

        FlowBpmVo flowBpmVo = flowTaskVo.getBpmData();
        flowBpmVo.setTaskAssignee(taskAssignee);
        flowBpmVo.setTargetTaskKey(activityInstanceList.get(0).getActivityId());
        this.taskReturn(flowTaskVo);
    }

    // 获取任务前一节点
    private List<HistoricActivityInstance> getPrevFlowNode(String procInstId, List<SequenceFlow> roads, List<HistoricActivityInstance> historicSequenceFlowList) {
        if(CollectionUtils.isEmpty(historicSequenceFlowList) || CollectionUtils.isEmpty(roads)) {
            return null;
        }
        Set<String> hasSequenceFlowNodeSet = new HashSet<>();
        List<String> allPrevNodeIdList = roads.stream().map(SequenceFlow::getId).collect(Collectors.toList());
        for(HistoricActivityInstance historicSequenceFlow : historicSequenceFlowList) {
            if(allPrevNodeIdList.contains(historicSequenceFlow.getActivityId())) {
                List<String> userTaskKeyList = roads.stream().filter(r -> r.getId().equals(historicSequenceFlow.getActivityId())).map(sf -> {
                    return sf.getSourceFlowElement().getId();
                }).collect(Collectors.toList());

                hasSequenceFlowNodeSet.addAll(userTaskKeyList);
            }else if(!CollectionUtils.isEmpty(hasSequenceFlowNodeSet)) {
                break;
            }
        }

        List<HistoricActivityInstance> historicActivityInstances = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(procInstId)
                .activityType(BpmnXMLConstants.ELEMENT_TASK_USER)
                .finished()
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();
        List<HistoricActivityInstance> result = new ArrayList<>();
        for(HistoricActivityInstance historicActivityInstance : historicActivityInstances) {
            // 判断节点是否符合是上一个
            if(hasSequenceFlowNodeSet.contains(historicActivityInstance.getActivityId())) {
                result.add(historicActivityInstance);
            }else if(!CollectionUtils.isEmpty(result)) {
                break;
            }
        }
        return result;
    }

    /**
     * 获取特送所有可选择的节点
     * @param taskId
     * @return
     */
    @Override
    public ResultJson findExpressTaskList(String taskId) {
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if(Objects.isNull(task) || task.isSuspended()) {
            throw new CustomException("任务已完成或处于锁定状态");
        }

        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .includeProcessVariables()
                .singleResult();
        UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));

        // 获取所有用户节点信息
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        Process process = bpmnModel.getProcess(null);
        Collection<UserTask> userTaskList = FlowableUtils.getAllUserTaskElements(process.getFlowElements(), null);

        // 处理单
        FlowTaskProcessDto.FlowTaskProcessDtoBuilder flowTaskProcessDtoBuilder = FlowTaskProcessDto.builder()
                .taskName(task.getName())
                .taskDefKey(task.getTaskDefinitionKey())
                .processInstanceId(task.getProcessInstanceId())
                .procDefKey(historicProcessInstance.getProcessDefinitionKey())
                .procDefId(historicProcessInstance.getProcessDefinitionId());

        if(!CollectionUtils.isEmpty(userTaskList)) {
            List<FlowNextDto> allFlowNextDtos = new ArrayList<>();
            List<FlowSequenceDto> allFlowSequenceList = new ArrayList<>();

            for(UserTask userTask : userTaskList) {

                // 当前节点可选人员信息在流程任务定义里面定义
                FlowNextDto flowNextDto = this.currentUserTaskHandle(bpmnModel, userTask.getId(), startUser, task.getExecutionId());
                if(Objects.nonNull(flowNextDto)) {
                    allFlowNextDtos.add(flowNextDto);
                }

                // 前端显示连线规则
                allFlowSequenceList.addAll(this.nextSequenceFlowNode(bpmnModel, userTask.getId(), task.getExecutionId(), null));
            }

            // 预签值覆盖
            Map variables = historicProcessInstance.getProcessVariables();
            Map<String, String> preSignAssigneeMap = new HashMap();
            Object preSignAssigneeVar = variables.get(ProcessConstants.BPM_BPM_PRESIGNASSIGNEE);
            if(Objects.nonNull(preSignAssigneeVar)) {
                preSignAssigneeMap = FlowableUtils.formatPreSignAssignee(preSignAssigneeVar.toString());
            }
            if(!CollectionUtils.isEmpty(preSignAssigneeMap) && !CollectionUtils.isEmpty(allFlowNextDtos)) {
                for(FlowNextDto flowNextDto : allFlowNextDtos) {
                    // 选择人
                    if(preSignAssigneeMap.containsKey(flowNextDto.getTaskDefKey())) {
                        String preSignAssignee = preSignAssigneeMap.get(flowNextDto.getTaskDefKey());
                        if(StringUtils.isNotBlank(preSignAssignee)) {
                            List<String> userList = processELService.getResolveUsers(preSignAssignee, flowNextDto.getTaskDefKey());
                            List<SimpleUserDto> simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(userList);
                            flowNextDto.setCandidateUser(simpleUserDtoList);
                        }else {
                            flowNextDto.setCandidateUser(Lists.newArrayList());
                        }
                    }
                }
            }

            flowTaskProcessDtoBuilder.userTaskList(allFlowNextDtos);
            flowTaskProcessDtoBuilder.flowSequenceList(allFlowSequenceList);
        }
        return ResultJson.generateResult(flowTaskProcessDtoBuilder.build());
    }

    /**
     * 获取所有可回退的节点
     * @param flowTaskVo
     * @return
     */
    @Deprecated
    @Override
    public ResultJson findReturnTaskList(FlowTaskVo flowTaskVo) {
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(flowTaskVo.getBpmData().getTaskId()).singleResult();

        // 获取所有节点信息，暂不考虑子流程情况
        Process process = repositoryService.getBpmnModel(task.getProcessDefinitionId()).getProcesses().get(0);
        Collection<FlowElement> flowElements = FlowableUtils.getAllElements(process.getFlowElements(), null);
        // 获取当前任务节点元素
        UserTask source = null;
        if(!CollectionUtils.isEmpty(flowElements)) {
            for(FlowElement flowElement : flowElements) {
                // 类型为用户节点
                if(flowElement.getId().equals(task.getTaskDefinitionKey())) {
                    source = (UserTask) flowElement;
                }
            }
        }
        // 获取节点的所有路线
        List<List<UserTask>> roads = FlowableUtils.findRoad(source, null, null, null);
        // 可回退的节点列表
        List<UserTask> userTaskList = new ArrayList<>();
        for(List<UserTask> road : roads) {
            if(userTaskList.size() == 0) {
                // 还没有可回退节点直接添加
                userTaskList = road;
            }else {
                // 如果已有回退节点，则比对取交集部分
                userTaskList.retainAll(road);
            }
        }
        List<FlowTaskDto> flowTaskDtoList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(userTaskList)) {
            userTaskList.forEach(userTask -> {
                FlowTaskUserDto flowTaskUserDto = FlowTaskUserDto.builder()
                        .assigneeName(userTask.getAssignee())
                        .build();
                FlowTaskDto flowTaskDto = FlowTaskDto.builder()
                        .assigneeInfo(flowTaskUserDto)
                        .taskId(userTask.getId())
                        .taskName(userTask.getName())
                        .duration(userTask.getDueDate())
                        .build();
                flowTaskDtoList.add(flowTaskDto);
            });
        }
        return ResultJson.generateResult(flowTaskDtoList);
    }

    /**
     * 删除任务
     * @param flowTaskVo 请求实体参数
     */
    @Override
    public void deleteTask(FlowTaskVo flowTaskVo) {
        // todo 待确认删除任务是物理删除任务 还是逻辑删除，让这个任务直接通过？
    }

    /**
     * 认领/签收任务
     * @param flowTaskVo 请求实体参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claim(FlowTaskVo flowTaskVo) {
        taskService.claim(flowTaskVo.getBpmData().getTaskId(), flowTaskVo.getBpmData().getUsername());
    }

    /**
     * 取消认领/签收任务
     * @param flowTaskVo 请求实体参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unClaim(FlowTaskVo flowTaskVo) {
        taskService.unclaim(flowTaskVo.getBpmData().getTaskId());
    }

    /**
     * 委派任务
     * @param flowTaskVo 请求实体参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delegateTask(FlowTaskVo flowTaskVo) {
        String taskId = flowTaskVo.getBpmData().getTaskId();

        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(taskId).includeProcessVariables().singleResult();
        if(Objects.isNull(task) || task.isSuspended()) {
            throw new CustomException("任务已完成或处于锁定状态");
        }

        String nextAssignee = flowTaskVo.getBpmData().taskAssigneeFormatToString();
        if(StringUtils.isEmpty(nextAssignee)) {
            throw new CustomException("特送节点处理人未选择");
        }

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        // 业务校验
        JSONObject formDataObj = null;
        if(Objects.nonNull(flowTaskVo.getFormData())) {
            String formData = JSON.toJSONString(flowTaskVo.getFormData());
            formDataObj = JSONObject.parseObject(formData);
            ResultJson validateResult = workflowCustomExtensionService.validateFormData(task.getProcessInstanceId(), processInstance.getProcessDefinitionKey(), formDataObj);
            if(!validateResult.isSuccess()) {
                throw new CustomException(validateResult.getMessage());
            }
        }

        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        Process process = bpmnModel.getProcess(null);

        // 当前节点
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());

        Map variables = task.getProcessVariables();
        String currentUsername = flowTaskVo.getBpmData().getUsername();
        if(StringUtils.isBlank(currentUsername)) {
            currentUsername = task.getAssignee();
        }
        if(StringUtils.isBlank(currentUsername)) {
            currentUsername = SecurityUtils.getCurrentUsername();
        }
        UserDto userDto = sysUserService.findByName(currentUsername);

        // 保存表单数据
        if(Objects.nonNull(formDataObj) && !formDataObj.isEmpty()) {

            // 处理表单数据
            if(StringUtils.isEmpty(formDataObj.getString("bpmInstanceId"))) {
                formDataObj.put("bpmInstanceId", task.getProcessInstanceId());
            }
            if(StringUtils.isEmpty(formDataObj.getString("bpmProcessKey"))) {
                formDataObj.put("bpmProcessKey", processInstance.getProcessDefinitionKey());
            }

            // 处理流程标题自定义属性
            String title = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_BACKLOG_SUBJECT_RULE);
            if(StringUtils.isNotEmpty(title)) {
                variables.put(ProcessConstants.BPM_FORM_TITLE, flowFormHandleFactory.handleSubjectRule(formDataObj, title));
            }

            if(StringUtils.isEmpty(formDataObj.getString("bpmSubject"))) {
                formDataObj.put("bpmSubject", variables.get(ProcessConstants.BPM_FORM_TITLE));
            }

            try {
                ResponseInfo responseInfo = formTemplateService.formSubmit(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), formDataObj.toJSONString(), taskId);
                variables.put(ProcessConstants.BPM_FORM_DATA, formDataObj);
            }catch(Exception e) {
                throw new CustomException("更新业务表单数据错误");
            }
        }

        // 设置意见
        String remark = flowTaskVo.getBpmData().getRemark();
        if(StringUtils.isEmpty(remark)) {
            remark = "";
        }
        taskService.addComment(taskId, task.getProcessInstanceId(), TaskCommentTypeEnum.ZB.name(), remark);

        // 意见上表单
        String remarkToFormField = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
        if(StringUtils.isNotBlank(remarkToFormField)) {
            FlowFormRemarkDto flowFormRemarkDto = FlowFormRemarkDto.builder()
                    .remark(remark)
                    .username(userDto.getUsername())
                    .nickName(userDto.getNickName())
                    .deptId(userDto.getDeptId())
                    .deptName(userDto.getDept().getName())
                    .processedTime(new Date())
                    .fj(flowTaskVo.getBpmData().getRemarkAttachment())
                    .build();

            boolean append = true;
            String remarkCoverd = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKCOVERD);
            if(StringUtils.isNotBlank(remarkCoverd) && Boolean.parseBoolean(remarkCoverd)) {
                append = false;
            }

            String processRemarkSort = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_REMARK_SORT);
            boolean insertBefore = false;
            if(StringUtils.isNotBlank(processRemarkSort)) {
                insertBefore = Boolean.parseBoolean(processRemarkSort);
            }

            try {
                ResponseInfo responseInfo = formTemplateService.updateFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), task.getProcessInstanceId(), remarkToFormField, flowFormRemarkDto, append, insertBefore);
                JSONObject formData = (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA);
                if(ResultCodeEnum.SUCCESS.getCode() == responseInfo.getCode()) {
                    // 更新意见上表单数据
                    JSONObject jsonObject = (JSONObject) responseInfo.getData();
                    List<String> remarkToFormFieldList = FlowableUtils.findAllRemarkToFormField(process);
                    for(String key : remarkToFormFieldList) {
                        if(jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
                            formData.put(key, jsonObject.get(key));
                        }
                    }
                }else {
                    formTemplateService.updateVariableFormFieldData(formData, remarkToFormField, flowFormRemarkDto, append, insertBefore);
                }
                variables.replace(ProcessConstants.BPM_FORM_DATA, formData);
            }catch(Exception e) {
                log.error(e.getMessage());
                throw new CustomException("更新业务表单数据错误");
            }
        }

        if(StringUtils.isEmpty(task.getAssignee())) {
            task.setAssignee(SecurityUtils.getCurrentUsername());
        }
        String taskNodes = flowTaskVo.getBpmData().taskNodesToString();
        List<String> activityIds = Lists.newArrayList(taskNodes.split(ElAdminConstant.SEPARATOR_COMMA).clone());

        if(FlowableUtils.hasMultiInstanceNode(bpmnModel, taskNodes)) {
            // 设置多实例处理用户
            StringBuilder taskAssigneeCache = new StringBuilder();
            Object taskAssigneeCacheObj = variables.get(ProcessConstants.BPM_TASK_ASSIGNEE_CACHE);
            if(Objects.nonNull(taskAssigneeCacheObj)) {
                List<TaskAssigneeVo> taskAssigneeVoList = FlowableUtils.parseNextTaskAssigneeVo(taskAssigneeCacheObj.toString())
                        .stream().filter(a -> !(a.getTaskKey().equals(task.getTaskDefinitionKey()))).collect(Collectors.toList());
                taskAssigneeCache.append(FlowableUtils.taskAssigneeFormatToString(taskAssigneeVoList));
            }
            if(taskAssigneeCache.length() > 0) {
                taskAssigneeCache.append(ElAdminConstant.SEPARATOR_COMMA);
            }
            taskAssigneeCache.append(nextAssignee);
            variables.put(ProcessConstants.BPM_TASK_ASSIGNEE_CACHE, taskAssigneeCache);
        }

        // 特送节点
        variables.put(ProcessConstants.BPM_TASK_DEF_KEY, taskNodes);

        // 设置后续节点处理人
        variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, nextAssignee);

        // 设置当前节点接收人
        runtimeService.setVariableLocal(task.getProcessInstanceId(), ProcessConstants.BPM_TASK_TO_ASSIGNEES + ElAdminConstant.SEPARATOR_UNDERLINE + task.getId(), nextAssignee);

        try {
            // 解决多实例特送的的任务为子执行问题
            if(Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
                runtimeService.createChangeActivityStateBuilder()
                        .processInstanceId(task.getProcessInstanceId())
                        .processVariables(variables)
                        .moveSingleActivityIdToActivityIds(task.getTaskDefinitionKey(), activityIds)
                        .changeState();
            }else {
                runtimeService.createChangeActivityStateBuilder()
                        .processInstanceId(task.getProcessInstanceId())
                        .processVariables(variables)
                        .moveSingleExecutionToActivityIds(task.getExecutionId(), activityIds)
                        .changeState();
            }
        }catch(FlowableException e) {
            log.error(e.getMessage());
            throw new CustomException("无法特送，请联系管理员");
        }
    }

    /**
     * 我的流程
     * @param query
     * @return
     */
    @Override
    public Page myProcessList(ProcessQueryVo query) {
        HistoricProcessBO historicProcessBO = HistoricProcessBO.builder()
                .processDefinitionKey(query.getProcDefKey())
                .username(query.getUsername())
                .name(query.getName())
                .timeRange(query.getTimeRange())
                .build();
        Pageable pageable = PageRequest.of(query.getPage(), query.getSize());
        Map<String, Object> historicProcessMap = this.findHistoricProcessList(historicProcessBO, pageable);
        List<HistoricProcessInstance> historicProcessInstanceList = (List<HistoricProcessInstance>) historicProcessMap.get("content");

        List<FlowTaskDto> hisTaskList = new ArrayList();
        for(HistoricProcessInstance histProcess : historicProcessInstanceList) {
            FlowTaskDto flowTask = new FlowTaskDto();

            // 流程定义信息
            flowTask.setDeployId(histProcess.getDeploymentId());
            flowTask.setProcDefName(histProcess.getName());
            if(Objects.nonNull(histProcess.getProcessDefinitionVersion())) {
                flowTask.setProcDefVersion(histProcess.getProcessDefinitionVersion());
            }
            flowTask.setProcInsId(histProcess.getId());
            flowTask.setCreateTime(histProcess.getStartTime());
            flowTask.setFinishTime(histProcess.getEndTime());

            // 流程发起人信息
            UserDto startUser = sysUserService.findById(Long.parseLong(histProcess.getStartUserId()));
            FlowableUtils.setStartUserInfo(flowTask, startUser);

            // 流程处理人信息
            List<Task> tasks = taskService.createTaskQuery().processInstanceId(histProcess.getId()).list();
            if(!CollectionUtils.isEmpty(tasks)) {
                Task task = tasks.get(0);
                UserDto sysUser = sysUserService.findByName(task.getAssignee());
                FlowableUtils.setAssigneeUserInfo(flowTask, sysUser);
            }

            // 流程状态
            if(Objects.nonNull(histProcess.getEndTime())) {
                String procStatus = histProcess.getBusinessStatus();
                if(StringUtils.isNotEmpty(procStatus)) {
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(procStatus);
                    flowTask.setProcStatus(procStatusEnum);
                }else {
                    flowTask.setProcStatus(ProcStatusEnum.TG);
                }
            }else {
                flowTask.setProcStatus(ProcStatusEnum.INPROGRESS);
            }

            hisTaskList.add(flowTask);
        }
        Page<FlowTaskDto> page = new Page<>();
        page.setRecords(hisTaskList);
        page.setTotal(Long.parseLong(historicProcessMap.get("totalElements").toString()));
        return page;
    }

    /**
     * 取消申请(撤销)
     * @param flowTaskVo
     * @return
     */
    @Override
    @Transactional
    public ResultJson stopProcess(FlowTaskVo flowTaskVo) {
        if(StringUtils.isEmpty(flowTaskVo.getBpmData().getProcessInstanceId())) {
            throw new CustomException("参数错误");
        }
        ProcessInstance processInstance =
                runtimeService.createProcessInstanceQuery().processInstanceId(flowTaskVo.getBpmData().getProcessInstanceId()).includeProcessVariables().singleResult();
        if(Objects.isNull(processInstance)) {
            throw new CustomException("流程未启动或已执行完成，撤销申请失败");
        }

        // 流程启动人是自己或者用户是管理员
        if(!workflowCustomExtensionService.isBpmAdmin() && !(processInstance.getStartUserId().equals(SecurityUtils.getCurrentUserId().toString()))) {
            throw new CustomException("没有权限，撤销申请失败");
        }

        // 处理自定义业务数据
        boolean customResult = workflowCustomExtensionService.handleStopProcess(processInstance.getProcessInstanceId());
        if(!customResult) {
            return ResultJson.generateResult("撤销失败，处理业务数据错误，请联系管理员");
        }

        // 设置表单状态
        try {
            Map<String, Object> variables = processInstance.getProcessVariables();
            formTemplateService.updateFormStatus(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), processInstance.getProcessInstanceId(), ProcStatusEnum.CX);
        }catch(Exception e) {
            throw new FlowableException("更新流程表单状态出错", e);
        }

        // 设置流程状态为撤销
        runtimeService.updateBusinessStatus(processInstance.getProcessInstanceId(), ProcStatusEnum.CX.getValue());
        runtimeService.deleteProcessInstance(processInstance.getId(), ProcStatusEnum.CX.getValue());

        return ResultJson.generateResult("撤销成功");
    }

    /**
     * 收回流程
     * @param flowTaskVo
     * @return
     */
    @Override
    public ResultJson revokeProcess(FlowTaskVo flowTaskVo) {
        String processInstanceId = flowTaskVo.getBpmData().getProcessInstanceId();
        if(StringUtils.isEmpty(processInstanceId)) {
            return ResultJson.generateResult(ResultCodeEnum.NULL_ARGUMENT.getMessageCN(), ResultCodeEnum.NULL_ARGUMENT.getCode());
        }

        List<Task> runTaskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        if(CollectionUtils.isEmpty(runTaskList)) {
            return ResultJson.generateResult("流程未启动或已执行完成，无法收回", ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }

        String username = SecurityUtils.getCurrentUsername();
        List<HistoricTaskInstance> htiList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .taskAssignee(username)
                .finished()
                .includeProcessVariables()
                .orderByTaskCreateTime()
                .desc()
                .list();

        // 原任务 task
        HistoricTaskInstance myTask = null;
        if(!CollectionUtils.isEmpty(htiList)) {
            myTask = htiList.get(0);
        }

        // 任务没有该用户提交
        if(Objects.isNull(myTask)) {
            throw new CustomException("该任务非当前用户提交，无法收回");
        }

        // 获取所有节点信息
        Process process = repositoryService.getBpmnModel(myTask.getProcessDefinitionId()).getProcess(null);

        // 获取跳转的节点元素
        FlowNode target = (FlowNode) process.getFlowElement(myTask.getTaskDefinitionKey());
        String taskExecType = target.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_TASKEXECTYPE);
        if(StringUtils.isNotEmpty(taskExecType) && taskExecType.equals(WdTaskExecTypeEnum.TASKEXECTYPE_FORK.getValue())) { // 分发
            return ResultJson.generateResult("会签任务不支持收回", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }

        // 获取所有正常进行的任务节点 Key，这些任务不能直接使用，需要找出其中需要收回的任务
        List<String> runTaskKeyList = new ArrayList<>();
        List<String> runExecutionIdList = new ArrayList<>();
        runTaskList.forEach(item -> {
            runTaskKeyList.add(item.getTaskDefinitionKey());
            runExecutionIdList.add(item.getExecutionId());
        });

        if(Sets.newHashSet(runTaskKeyList).size() > 1 && target.getBehavior() instanceof MultiInstanceActivityBehavior) {
            return ResultJson.generateResult("会签任务暂不支持收回后续有多处理节点任务", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }

        // 通过父级网关的出口连线，结合 runTaskList 比对，获取需要收回的任务
        List<UserTask> currentUserTaskList = FlowableUtils.findOutgoingUserTasks(target, null, null);

        // 需退回任务列表，如果改节点有多人并行处理，收回自己，修改节点状态
        List<String> currentIds = new ArrayList<>();
        currentUserTaskList.forEach(item -> currentIds.add(item.getId()));

        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .taskDefinitionKeys(currentIds)
                .taskCreatedAfter(myTask.getCreateTime())
                .finished().list();
        if(!currentIds.containsAll(runTaskKeyList) || !CollectionUtils.isEmpty(historicTaskInstanceList)) {
            return ResultJson.generateResult("后续节点已有完成审批或已收回，无法收回", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }

        // 循环获取那些需要被收回的节点的ID，用来设置收回原因
        List<String> deleteIds = new ArrayList<>();
        runTaskList.forEach(runTask -> {
            deleteIds.add(runTask.getId());
            taskService.addComment(runTask.getId(), processInstanceId, TaskCommentTypeEnum.SH.name(), TaskCommentTypeEnum.SH.getName());
        });

        Map variables = myTask.getProcessVariables();
        variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, username);

        try {
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(myTask.getProcessInstanceId())
                    .processVariables(variables)
                    .moveExecutionsToSingleActivityId(runExecutionIdList, target.getId())
                    .changeState();
        }catch(FlowableException e) {
            return ResultJson.generateResult("收回失败", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }
        return ResultJson.generateResult("收回成功!");
    }

    /**
     * 我的代办任务列表
     * @param query 查询参数
     * @return
     */
    @Override
    public ResultJson todoList(TaskQueryVo query) throws Exception {
        query.setUsername(SecurityUtils.getCurrentUsername());
        Page<FlowTaskDto> page = this.getApplyingTasks(query);
        return ResultJson.generateResult(page);
    }

    @Override
    public Page<FlowTaskDto> getApplyingTasks(TaskQueryVo query) throws Exception {

        TaskQuery taskQuery = taskService.createTaskQuery()
                .active()
                .includeProcessVariables()
                .orderByTaskCreateTime().desc();
        // userName关联处理人
        if(StringUtils.isNotEmpty(query.getUsername())) {
            taskQuery.taskAssigneeLike(query.getUsername());
        }
        if(StringUtils.isNotEmpty(query.getName())) {
            taskQuery.processDefinitionNameLike(query.getName() + "%");
        }
        if(!CollectionUtils.isEmpty(query.getTimeRange())) {
            taskQuery.taskCreatedAfter(DateUtils.parseDate(query.getTimeRange().get(0), ymdhmFormat));
            taskQuery.taskCreatedBefore(DateUtils.parseDate(query.getTimeRange().get(1), ymdhmFormat));
        }
        if(StringUtils.isNotEmpty(query.getCategory())) {
            //流程类型不为空查询
            taskQuery.processCategoryIn(Collections.singleton(query.getCategory()));
        }

        if(StringUtils.isNotBlank(query.getProcDefKey())) {
            taskQuery.processDefinitionKey(query.getProcDefKey());
        }


        Page<FlowTaskDto> page = new Page<>();
        page.setTotal(taskQuery.count());

        List<Task> taskList = taskQuery.listPage(query.getPage() * query.getSize(), query.getSize());
        List<FlowTaskDto> flowList = new ArrayList<>();
        for(Task task : taskList) {
            FlowTaskDto flowTask = new FlowTaskDto();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            // 审批人员信息
            if(StringUtils.isNotEmpty(task.getAssignee())) {
                UserDto sysUser = sysUserService.findByName(task.getAssignee());
                FlowableUtils.setAssigneeUserInfo(flowTask, sysUser);
            }
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());

            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();
            if(Objects.nonNull(pd)) {
                flowTask.setProcDefName(pd.getName());
                flowTask.setProcDefVersion(pd.getVersion());
                flowTask.setProcInsId(task.getProcessInstanceId());
            }

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            if(StringUtils.isNotEmpty(historicProcessInstance.getStartUserId())) {
                UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
                FlowableUtils.setStartUserInfo(flowTask, startUser);
            }

            Map variables = task.getProcessVariables();
            Object formTitle = variables.get(ProcessConstants.BPM_FORM_TITLE);
            if(Objects.isNull(formTitle)) {
                formTitle = "";
            }
            flowTask.setFormTitle(formTitle.toString());
            flowList.add(flowTask);
        }

        page.setRecords(flowList);
        return page;
    }

    @Override
    public ResultJson finishedList(TaskQueryVo query) throws ParseException {
        Page<FlowTaskDto> page = new Page<>();
        String username = SecurityUtils.getCurrentUsername();
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(username)
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        // userName关联处理人
        if(StringUtils.isNotEmpty(query.getUsername())) {
            taskInstanceQuery.taskAssigneeLike(query.getUsername());
        }
        if(StringUtils.isNotEmpty(query.getName())) {
            taskInstanceQuery.processDefinitionNameLike(query.getName() + "%");
        }
        if(!CollectionUtils.isEmpty(query.getTimeRange())) {
            taskInstanceQuery.taskCreatedAfter(DateUtils.parseDate(query.getTimeRange().get(0), ymdhmFormat));
            taskInstanceQuery.taskCreatedBefore(DateUtils.parseDate(query.getTimeRange().get(1), ymdhmFormat));
        }
        if(StringUtils.isNotEmpty(query.getCategory())) {
            taskInstanceQuery.processCategoryIn(Collections.singleton(query.getCategory()));
        }
        if(query.getStatus() != null && ProcessConstants.QUERY_STATUS_YJYWJ == query.getStatus()) {
            //已完结
            taskInstanceQuery.processFinished();
        }else if(query.getStatus() != null && ProcessConstants.QUERY_STATUS_YJWWJ == query.getStatus()) {
            //未完结
            taskInstanceQuery.processUnfinished();
        }
        if(StringUtils.isNotBlank(query.getProcDefKey())) {
            taskInstanceQuery.processDefinitionKey(query.getProcDefKey());
        }
        page.setTotal(taskInstanceQuery.count());
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.listPage(query.getPage() * query.getSize(), query.getSize());
        this.handleFinishedPageList(page, historicTaskInstanceList);
        return ResultJson.generateResult(page);
    }

    @Override
    public ResultJson finishedList(Integer pageNum, Integer pageSize, String procDefKey, SearchDtoCriteria criteria) {
        return this.finishedList(pageNum, pageSize, Arrays.asList(procDefKey), criteria);
    }

    @Override
    public ResultJson finishedList(Integer pageNum, Integer pageSize, List<String> procDefKeys, SearchDtoCriteria criteria) {
        Page<FlowTaskDto> page = new Page<>();
        String username = SecurityUtils.getCurrentUsername();
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(username)
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        if(!CollectionUtils.isEmpty(procDefKeys)) {
            if(procDefKeys.size() == 1) {
                taskInstanceQuery.processDefinitionKey(procDefKeys.get(0));
            }else {
                taskInstanceQuery.processDefinitionKeyIn(procDefKeys);
            }
        }
        if(criteria.getType() != null && ProcessConstants.QUERY_STATUS_YJYWJ == criteria.getType()) {
            //已完结
            taskInstanceQuery.processFinished();
        }else if(criteria.getType() != null && ProcessConstants.QUERY_STATUS_YJWWJ == criteria.getType()) {
            //未完结
            taskInstanceQuery.processUnfinished();
        }
        if(StringUtils.isNotEmpty(criteria.getBt())) {
            taskInstanceQuery.processVariableValueLikeIgnoreCase(ProcessConstants.BPM_FORM_TITLE, criteria.getBt() + "%");
        }
        if(StringUtils.isNotEmpty(criteria.getSwlx())) {
            taskInstanceQuery.processVariableValueEquals(ProcessConstants.BPM_FORM_DOCUMENT_REVIEW, criteria.getSwlx());
        }
        if(StringUtils.isNotEmpty(criteria.getCategory())) {
            taskInstanceQuery.processCategoryIn(Collections.singleton(criteria.getCategory()));
        }
        page.setTotal(taskInstanceQuery.count());
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.listPage(pageNum * pageSize, pageSize);
        this.handleFinishedPageList(page, historicTaskInstanceList);
        return ResultJson.generateResult(page);
    }

    // 处理已完成记录
    private void handleFinishedPageList(Page<FlowTaskDto> page, List<HistoricTaskInstance> historicTaskInstanceList) {
        List<FlowTaskDto> hisTaskList = Lists.newArrayList();
        for(HistoricTaskInstance histTask : historicTaskInstanceList) {
            FlowTaskDto flowTask = new FlowTaskDto();
            // 当前流程信息
            flowTask.setTaskId(histTask.getId());
            // 审批人员信息
            UserDto sysUser = sysUserService.findByName(histTask.getAssignee());
            FlowableUtils.setAssigneeUserInfo(flowTask, sysUser);
            flowTask.setCreateTime(histTask.getCreateTime());
            flowTask.setFinishTime(histTask.getEndTime());
            flowTask.setDuration(FlowableUtils.getDate(histTask.getDurationInMillis()));
            flowTask.setProcDefId(histTask.getProcessDefinitionId());
            flowTask.setTaskDefKey(histTask.getTaskDefinitionKey());
            flowTask.setTaskName(histTask.getName());

            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(histTask.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(histTask.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(histTask.getProcessInstanceId())
                    .singleResult();
            UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
            FlowableUtils.setStartUserInfo(flowTask, startUser);
            hisTaskList.add(flowTask);
        }

        page.setRecords(hisTaskList);
    }

    /**
     * 获取流程变量
     * @param taskId
     * @return
     */
    @Override
    public ResultJson processVariables(String taskId) {

        // 获取任务扩展属性
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if(Objects.isNull(task)) {
            return ResultJson.generateResult("任务已办理或已撤回", ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }

        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .includeProcessVariables()
                .singleResult();

        Map<String, Object> variables = historicProcessInstance.getProcessVariables();

        // 返回数据结构
        FlowTaskVariablesDto.FlowTaskVariablesDtoBuilder flowTaskVariablesDtoBuilder = FlowTaskVariablesDto.builder();

        // 表单数据
        if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_DATA))) {
            Object bpmFormData = flowFormHandleFactory.handleFormRecord(historicProcessInstance.getProcessDefinitionKey(), task.getProcessInstanceId(), task.getTaskDefinitionKey(), (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA));
            if(Objects.isNull(bpmFormData)) {
                bpmFormData = variables.get(ProcessConstants.BPM_FORM_DATA);
            }
            flowTaskVariablesDtoBuilder.formData(bpmFormData);
        }else { // 初始化默认数据
            Object defaultFormData = flowFormHandleFactory.handleFormRecord(historicProcessInstance.getProcessDefinitionKey(), task.getProcessInstanceId(), task.getTaskDefinitionKey(), null);
            flowTaskVariablesDtoBuilder.formData(defaultFormData);
        }

        // 流程发起人信息
        UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
        FlowTaskApplicantDto flowTaskApplicantDto = FlowTaskApplicantDto.builder()
                .startUserId(startUser.getId())
                .startNickName(startUser.getNickName())
                .startUsername(startUser.getUsername())
                .startDeptName(startUser.getDept().getName())
                .startTime(historicProcessInstance.getStartTime()
                )
                .build();
        flowTaskVariablesDtoBuilder.applicantInfo(flowTaskApplicantDto);

        // 处理单
        FlowTaskProcessDto.FlowTaskProcessDtoBuilder flowTaskProcessDtoBuilder = FlowTaskProcessDto.builder()
                .taskName(task.getName())
                .taskDefKey(task.getTaskDefinitionKey())
                .taskId(task.getId())
                .processInstanceId(task.getProcessInstanceId())
                .procDefKey(historicProcessInstance.getProcessDefinitionKey())
                .procDefId(historicProcessInstance.getProcessDefinitionId());

        // 设置表单样式
        if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_SKIN))) {
            flowTaskVariablesDtoBuilder.formSkin(variables.get(ProcessConstants.BPM_FORM_SKIN).toString());
        }
        if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_PC_SKIN))) {
            flowTaskVariablesDtoBuilder.formPcSkin(variables.get(ProcessConstants.BPM_FORM_PC_SKIN).toString());
        }

        // 获取流程扩展
        GetInfoByKeyBO getInfoByKeyBO = GetInfoByKeyBO.builder().linkKey(task.getTaskDefinitionKey()).flowDefineId(historicProcessInstance.getDeploymentId()).build();
        WorkflowUserTaskExtension workflowUserTaskExtension = workflowUserTaskExtensionService.getInfoByKey(getInfoByKeyBO);

        Map<String, String> formRwhMap = new HashMap<>();
        if(Objects.nonNull(workflowUserTaskExtension)) {

            List<WorkflowUserTaskFormRw> formReadWrite = workflowUserTaskExtension.getReadWrite();
            if(!CollectionUtils.isEmpty(formReadWrite)) {
                formRwhMap = formReadWrite.stream().collect(Collectors.toMap(k -> StringUtils.isNotEmpty(k.getSubFormId()) ? k.getSubFormId() + k.getFormFieldIdentifier() : k.getFormFieldIdentifier(), v -> v.getReadState()));
                FlowTaskFormRwDto flowTaskFormRwDto = FlowTaskFormRwDto.builder().defaultFormStatus(workflowUserTaskExtension.getFormRead()).formWrite(JSON.toJSONString(formRwhMap)).build();
                flowTaskVariablesDtoBuilder.taskFormRw(flowTaskFormRwDto);
            }else {
                FlowTaskFormRwDto flowTaskFormRwDto = FlowTaskFormRwDto.builder().defaultFormStatus(workflowUserTaskExtension.getFormRead()).build();
                flowTaskVariablesDtoBuilder.taskFormRw(flowTaskFormRwDto);
            }

            formRwhMap.put(ProcessConstants.BPM_RWH_DEFAULT, workflowUserTaskExtension.getFormRead());
            flowTaskProcessDtoBuilder.taskButtons(workflowUserTaskExtension.getButtons());

            // 判断节点扩展中是否有值，有覆盖
            if(StringUtils.isNotEmpty(workflowUserTaskExtension.getFormSkin())) {
                flowTaskVariablesDtoBuilder.formPcSkin(workflowUserTaskExtension.getFormSkin());
            }
            if(StringUtils.isNotEmpty(workflowUserTaskExtension.getMobileFormSkin())) {
                flowTaskVariablesDtoBuilder.formSkin(workflowUserTaskExtension.getMobileFormSkin());
            }
        }else {
            formRwhMap.put(ProcessConstants.BPM_RWH_DEFAULT, "R");
            FlowTaskFormRwDto flowTaskFormRwDto = FlowTaskFormRwDto.builder().defaultFormStatus("R").build();
            flowTaskVariablesDtoBuilder.taskFormRw(flowTaskFormRwDto);
        }

        // 获取流转单
        WdFormTemplate template = wdFormTemplateRepository.getOne(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()));
        if(Objects.isNull(variables.get(ProcessConstants.BPM_FORM_JSON)) && Objects.nonNull(template)) {
            variables.put(ProcessConstants.BPM_FORM_JSON, template.getFormJson());
        }
        if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID))) {
            flowTaskVariablesDtoBuilder.formTemplateId(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()));
        }
        if(Objects.nonNull(variables.get(ProcessConstants.BPM_FORM_MOBILE_TEMPLATE_ID))) {
            WdFormTemplate mobileTemplate = wdFormTemplateRepository.getOne(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_MOBILE_TEMPLATE_ID).toString()));
            if(Objects.isNull(variables.get(ProcessConstants.BPM_MOBILE_FORM_JSON)) && Objects.nonNull(mobileTemplate)) {
                variables.put(ProcessConstants.BPM_MOBILE_FORM_JSON, mobileTemplate.getFormJson());
            }
            flowTaskVariablesDtoBuilder.mobileFormTemplateId(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_MOBILE_TEMPLATE_ID).toString()));
        }

        // PC端服务端处理读写状态
        JSONObject formObject = JSON.parseObject(variables.get(ProcessConstants.BPM_FORM_JSON).toString());
        // 处理流转表单读写属性
        formTemplateService.formRwHandle(formObject.getJSONArray("list"), formRwhMap);
        flowTaskVariablesDtoBuilder.formJson(JSON.toJSONString(formObject));
        if(Objects.nonNull(variables.get(ProcessConstants.BPM_MOBILE_FORM_JSON))) {
            JSONObject mobileFormJson = JSON.parseObject(variables.get(ProcessConstants.BPM_MOBILE_FORM_JSON).toString());
            // 处理流转表单读写属性
            formTemplateService.formRwHandle(mobileFormJson.getJSONArray("list"), formRwhMap);
            flowTaskVariablesDtoBuilder.mobileFormJson(JSON.toJSONString(mobileFormJson));
        }

        // 处理流程当前任务自定义属性
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());

        flowTaskProcessDtoBuilder.remindWay(currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMINDWAY));

        String remarkDisable = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKDISABLE);
        if(StringUtils.isNotEmpty(remarkDisable)) {
            flowTaskProcessDtoBuilder.remarkDisable(Boolean.parseBoolean(remarkDisable));
        }

        String remarkBlankAllow = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKBLANKALLOW);
        if(StringUtils.isNotEmpty(remarkBlankAllow)) {
            flowTaskProcessDtoBuilder.remarkBlankAllow(Boolean.parseBoolean(remarkBlankAllow));
        }else {
            flowTaskProcessDtoBuilder.remarkBlankAllow(true);
        }

        String remarkAttachmentAllow = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKATTACHMENTALLOW);
        if(StringUtils.isNotEmpty(remarkAttachmentAllow)) {
            flowTaskProcessDtoBuilder.remarkAttachmentAllow(Boolean.parseBoolean(remarkAttachmentAllow));
        }

        String remarkSeal = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKSEAL);
        if(StringUtils.isNotEmpty(remarkSeal)) {
            flowTaskProcessDtoBuilder.remarkSeal(Boolean.parseBoolean(remarkSeal));
        }

        // 多端支持
        String mutliDeviceSupport = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_MUTLI_DEVICE_SUPPORT);
        if(StringUtils.isBlank(mutliDeviceSupport)) {
            Process process = bpmnModel.getProcess(null);
            mutliDeviceSupport = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_MUTLI_DEVICE_SUPPORT);
        }
        flowTaskProcessDtoBuilder.mutliDeviceSupport(mutliDeviceSupport);

        // 处理单数据结构，返回下个节点可选人员信息
        // 后续节点可选人员信息在流程任务定义里面定义
        List<FlowNextDto> flowNextDtos = this.nextUserTaskHandle(bpmnModel, task.getTaskDefinitionKey(), task.getExecutionId(), startUser);
        List<FlowSequenceDto> flowSequenceDtoList = this.nextSequenceFlowNode(bpmnModel, task.getTaskDefinitionKey(), task.getExecutionId(), null);

        // 会签最后一个审批人显示后续节点
        boolean isLastAssignee = processELService.isLastAssignee(bpmnModel, task);
        if(isLastAssignee) {
            flowTaskProcessDtoBuilder.message(currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_MESSAGE));

            if(!CollectionUtils.isEmpty(flowNextDtos)) {
                flowTaskProcessDtoBuilder.userTaskList(flowNextDtos);
            }

            // 前端显示连线规则
            flowTaskProcessDtoBuilder.flowSequenceList(flowSequenceDtoList);
        }

        // 预签
        Map<String, String> preSignAssigneeMap = new HashMap();
        Object preSignAssigneeVar = variables.get(ProcessConstants.BPM_BPM_PRESIGNASSIGNEE);
        if(Objects.nonNull(preSignAssigneeVar)) {
            preSignAssigneeMap = FlowableUtils.formatPreSignAssignee(preSignAssigneeVar.toString());
        }

        String taskPreSign = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_PRESIGN);
        if(StringUtils.isNotBlank(taskPreSign)) {
            // 获取被该节点预签的节点
            List<String> preSignTaskDefKeys = Arrays.asList(taskPreSign.split(ElAdminConstant.SEPARATOR_COMMA));
            List<FlowTaskProcessDto> preSigns = new ArrayList<>();
            for(String taskDefKey : preSignTaskDefKeys) {
                FlowElement flowElement = bpmnModel.getFlowElement(taskDefKey);
                FlowNextDto flowNextDto = this.currentUserTaskHandle(bpmnModel, flowElement.getId(), startUser, task.getExecutionId());
                List<FlowNextDto> preSignFlowNextDtoList = new ArrayList();
                if(Objects.nonNull(flowNextDto)) {
                    preSignFlowNextDtoList.add(flowNextDto);
                }
                FlowTaskProcessDto.FlowTaskProcessDtoBuilder flowTaskProcessDto = FlowTaskProcessDto.builder()
                        .taskName(flowElement.getName())
                        .taskDefKey(flowElement.getId())
                        .userTaskList(preSignFlowNextDtoList);

                // 原先被预签过的节点值覆盖默认值
                if(preSignAssigneeMap.containsKey(flowElement.getId())) {
                    List<FlowNextDto> preSignFlowNextDtos = flowTaskProcessDto.build().getUserTaskList();
                    String preSignAssignee = preSignAssigneeMap.get(flowElement.getId());
                    if(StringUtils.isNotEmpty(preSignAssignee)) {
                        for(FlowNextDto preSignFlowNextDto : preSignFlowNextDtos) {
                            List<String> userList = processELService.getResolveUsers(preSignAssignee, preSignFlowNextDto.getTaskDefKey());
                            List<SimpleUserDto> simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(userList);
                            preSignFlowNextDto.setCandidateUser(simpleUserDtoList);
                        }
                    }else {
                        for(FlowNextDto preSignFlowNextDto : preSignFlowNextDtos) {
                            preSignFlowNextDto.setCandidateUser(Lists.newArrayList());
                        }
                    }
                }
                preSigns.add(flowTaskProcessDto.build());
            }
            flowTaskVariablesDtoBuilder.preSign(preSigns);
        }

        // 预签数据回填
        if(!CollectionUtils.isEmpty(preSignAssigneeMap) && !CollectionUtils.isEmpty(flowNextDtos)) {
//                List<FlowSequenceDto> flowSequenceDtoList = flowTaskProcessDtoBuilder.build().getFlowSequenceList();
            for(FlowNextDto flowNextDto : flowNextDtos) {
                // 选择人
                if(preSignAssigneeMap.containsKey(flowNextDto.getTaskDefKey()) && !currentFlowNode.getId().equals(flowNextDto.getTaskDefKey())) {
                    String preSignAssignee = preSignAssigneeMap.get(flowNextDto.getTaskDefKey());
                    if(StringUtils.isNotBlank(preSignAssignee)) {
                        List<String> userList = processELService.getResolveUsers(preSignAssignee, flowNextDto.getTaskDefKey());
                        List<SimpleUserDto> simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(userList);
                        flowNextDto.setCandidateUser(simpleUserDtoList);

                        // 根据选中人，设置连线默认选中
//                        for (FlowSequenceDto item : flowSequenceDtoList) {
//                            if (item.getTargetDefKey().equals(flowNextDto.getTaskDefKey()) && !CollectionUtils.isEmpty(flowNextDto.getCandidateUser())) {
//                                item.setIsDefaultFlow("true");
//                                break;
//                            }
//                        }
                    }else {
                        flowNextDto.setCandidateUser(Lists.newArrayList());
                        // 取消默认选中
//                        for (FlowSequenceDto item : flowSequenceDtoList) {
//                            if (item.getTargetDefKey().equals(flowNextDto.getTaskDefKey())) {
//                                item.setIsDefaultFlow("false");
//                                break;
//                            }
//                        }
                    }
                }
            }
        }

        // 连线是否重写用户任务规则，连线覆盖级别大于任务、预签
        if(isLastAssignee) {
            this.userTaskOverrideHandle(flowNextDtos, flowSequenceDtoList, bpmnModel, task.getExecutionId(), startUser);
        }

        // 意见
        String taskUsePreSignRemark = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_USEPRESIGNREMARK);
        Object remark = variables.get(ProcessConstants.BPM_BPM_PRESIGNREMARK);
        if(StringUtils.isNotBlank(taskUsePreSignRemark) && taskUsePreSignRemark.equals("true") && Objects.nonNull(remark)) {
            flowTaskProcessDtoBuilder.remark(remark.toString());
        }

        // 预签抄送
        Map<String, String> preSignCopyToAssigneeMap = new HashMap();
        Object preSignCopyToAssigneeVar = variables.get(ProcessConstants.BPM_BPM_PRESIGNCOPYTOASSIGNEE);
        if(Objects.nonNull(preSignCopyToAssigneeVar)) {
            preSignCopyToAssigneeMap = FlowableUtils.formatPreSignAssignee(preSignCopyToAssigneeVar.toString());
        }

        String taskPreSignCopyTo = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_PRESIGNCOPYTO);
        if(StringUtils.isNotBlank(taskPreSignCopyTo)) {
            // 获取被该节点预签抄送的节点
            List<String> preSignCopyToTaskDefKeys = Arrays.asList(taskPreSignCopyTo.split(ElAdminConstant.SEPARATOR_COMMA));
            List<FlowNextDto> preSignsCopyTo = new ArrayList<>();
            for(String taskDefKey : preSignCopyToTaskDefKeys) {
                FlowElement flowElement = bpmnModel.getFlowElement(taskDefKey);
                FlowNextDto.FlowNextDtoBuilder flowNextDtoBuilder = FlowNextDto.builder()
                        .taskName(flowElement.getName())
                        .taskDefKey(flowElement.getId());

                // 原先被预签过的节点值覆盖默认值
                if(preSignCopyToAssigneeMap.containsKey(flowElement.getId())) {
                    String preSignAssignee = preSignCopyToAssigneeMap.get(flowElement.getId());
                    List<String> userList = processELService.getResolveUsers(preSignAssignee, flowElement.getId());
                    List<SimpleUserDto> simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(userList);
                    flowNextDtoBuilder.candidateUser(simpleUserDtoList);
                }

                preSignsCopyTo.add(flowNextDtoBuilder.build());
            }
            flowTaskVariablesDtoBuilder.preSignCopyTo(preSignsCopyTo);
        }

        flowTaskVariablesDtoBuilder.processJson(flowTaskProcessDtoBuilder.build());

        // 流程相关信息
        flowTaskVariablesDtoBuilder.processName(historicProcessInstance.getProcessDefinitionName())
                .processVersion(historicProcessInstance.getProcessDefinitionVersion());

        // 业务设置扩展设置默认节点
        FlowTaskVariablesDto flowTaskVariablesDto = flowTaskVariablesDtoBuilder.build();
        workflowCustomExtensionService.handleFlowTaskData(flowTaskVariablesDto);

        return ResultJson.generateResult(flowTaskVariablesDto);
    }

    // 重写用户任务规则
    private void userTaskOverrideHandle(List<FlowNextDto> flowNextDtos, List<FlowSequenceDto> flowSequenceDtoList, BpmnModel bpmnModel, String executionId, UserDto startUser) {
        if(!CollectionUtils.isEmpty(flowSequenceDtoList)) {
            for(FlowSequenceDto flowSequenceDto : flowSequenceDtoList) {
                SequenceFlow sequenceFlow = (SequenceFlow) bpmnModel.getFlowElement(flowSequenceDto.getSequenceDefKey());
                Map<String, List<ExtensionElement>> extensionElements = sequenceFlow.getExtensionElements();

                String userTaskOverride = "false";
                List<ExtensionElement> extensionUserTaskOverrideElementList = extensionElements.get(ProcessConstants.BPM_BPM_USERTASK_OVERRIDE);
                if(!CollectionUtils.isEmpty(extensionUserTaskOverrideElementList)) {
                    userTaskOverride = extensionUserTaskOverrideElementList.get(0).getElementText();
                }
                if(Boolean.parseBoolean(userTaskOverride)) {
                    String taskAssigneeRule = null;
                    List<ExtensionElement> extensionTaskAssigneeRuleElementList = extensionElements.get(ProcessConstants.BPM_FLOW_CANDIDATE_RULE);
                    if(!CollectionUtils.isEmpty(extensionTaskAssigneeRuleElementList)) {
                        taskAssigneeRule = extensionTaskAssigneeRuleElementList.get(0).getElementText();
                    }
                    if(StringUtils.isNotEmpty(taskAssigneeRule) && !CollectionUtils.isEmpty(flowNextDtos)) {
                        for(FlowNextDto flowNextDto : flowNextDtos) {
                            if(flowNextDto.getTaskDefKey().equals(sequenceFlow.getTargetRef())) {
                                List<SimpleUserDto> userTaskAssigneeHandle = this.userTaskAssigneeHandle(taskAssigneeRule, flowNextDto.getTaskDefKey(), startUser, executionId);
                                flowNextDto.setCandidateUser(userTaskAssigneeHandle);

                                List<ExtensionElement> extensionTaskUserSelectorElementList = extensionElements.get(ProcessConstants.BPM_TASK_USERSELECTOR);
                                if(!CollectionUtils.isEmpty(extensionTaskUserSelectorElementList)) {
                                    String userSelector = extensionTaskUserSelectorElementList.get(0).getElementText();
                                    if(StringUtils.isNotBlank(userSelector)) {
                                        flowNextDto.setUserSelector(userSelector);
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理当前任务节点
     * @param bpmnModel bmpn模型
     * @param taskKey   节点Key
     * @param startUser 申请人
     * @return
     */
    @Override
    public FlowNextDto currentUserTaskHandle(BpmnModel bpmnModel, String taskKey, UserDto startUser, String executionId) {
        FlowNextDto flowNextDto = null;
        if(StringUtils.isNotBlank(taskKey)) {
            FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(taskKey);
            flowNextDto = this.userTaskHandle(flowNode, startUser, executionId);
        }
        return flowNextDto;
    }

    /**
     * 处理后续任务节点
     * @param bpmnModel   bmpn模型
     * @param taskKey     节点Key
     * @param executionId 执行编号
     * @param startUser   申请人
     * @return
     */
    private List<FlowNextDto> nextUserTaskHandle(BpmnModel bpmnModel, String taskKey, String executionId, UserDto startUser) {
        List<FlowNextDto> flowNextDtos = new ArrayList<>();
        List<FlowElement> userTaskList = this.nextTaskFlowNode(bpmnModel, taskKey, null, executionId);
        if(!CollectionUtils.isEmpty(userTaskList)) {
            userTaskList.forEach(userTask -> {
                FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(userTask.getId());
                FlowNextDto flowNextDto = this.userTaskHandle(flowNode, startUser, executionId);
                flowNextDtos.add(flowNextDto);
            });
        }
        return flowNextDtos;
    }

    /**
     * 处理任务节点
     * @param flowNode  流程节点
     * @param startUser 申请人
     * @return
     */
    private FlowNextDto userTaskHandle(FlowNode flowNode, UserDto startUser, String executionId) {
        if(Objects.isNull(flowNode)) {
            throw new CustomException("处理任务节点，节点参数不能为空");
        }

        FlowNextDto.FlowNextDtoBuilder flowNextDtoBuilder = FlowNextDto.builder()
                .taskName(flowNode.getName())
                .taskDefKey(flowNode.getId());

        // 子流程获取第一个用户节点
        if(flowNode instanceof SubProcess) {
            Collection<FlowElement> subFlowElements = ((SubProcess) flowNode).getFlowElements();
            List<FlowElement> subFlowUserTaskElements = subFlowElements.stream().filter((FlowElement e) -> e instanceof UserTask).collect(Collectors.toList());
            flowNode = (FlowNode) subFlowUserTaskElements.get(0);
        }

        String assignee = flowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_ASSIGNEE_RULE);
        List<SimpleUserDto> simpleUserDtoList = this.userTaskAssigneeHandle(assignee, flowNode.getId(), startUser, executionId);

        flowNextDtoBuilder.taskExecType(flowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_TASKEXECTYPE))
                .userSelector(flowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_USERSELECTOR))
                .candidateUser(simpleUserDtoList);
        if(flowNode instanceof EndEvent || flowNode instanceof ServiceTask) {
            flowNextDtoBuilder.taskExecType(ProcessConstants.BPM_TASK_TASKEXECTYPE_BLANK);
        }
        return flowNextDtoBuilder.build();
    }

    // 根据自定义规则获取处理用户信息
    private List<SimpleUserDto> userTaskAssigneeHandle(String assignee, String taskDefKey, UserDto startUser, String executionId) {
        List<SimpleUserDto> simpleUserDtoList = new ArrayList<>();
        if(StringUtils.isNotEmpty(assignee)) {
            // 自定义表达式
            if(StringUtils.isNotEmpty(executionId) && FlowableUtils.isExpressionRule(assignee)) {
                simpleUserDtoList = managementService.executeCommand(new SimpleUserExpressionExecuteCmd(executionId, assignee));
            }else {
                switch(assignee) {
                    case ProcessConstants.BPM_EXPRESSION_HEADOFUSER:    // 部门负责人
                        if(Objects.isNull(startUser)) {
                            break;
                        }
                        List<SysDeptUserPosition> sysDeptUserPositions = sysDeptUserPositionService.findList(PositionListBO.builder().type(PositionEnum.HEAD.getValue()).deptId(startUser.getDept().getId()).build());
                        simpleUserDtoList = sysDeptUserPositions.stream().map(p -> {
                            UserDto userDto = sysUserService.findById(p.getUserId());
                            return SimpleUserDto.builder()
                                    .id(p.getUserId())
                                    .userName(p.getName())
                                    .nickName(p.getUserName())
                                    .extId(userDto.getDept().getName() + p.getUserId())
                                    .build();
                        }).collect(Collectors.toList());
                        break;
                    case ProcessConstants.BPM_EXPRESSION_LEADERSHIPOFCURRENTUSER:   // 分管领导
                        if(Objects.isNull(startUser)) {
                            break;
                        }
                        sysDeptUserPositions = sysDeptUserPositionService.findList(PositionListBO.builder().type(PositionEnum.LEADERSHIP.getValue()).deptId(startUser.getDept().getId()).build());
                        simpleUserDtoList = sysDeptUserPositions.stream().map(p -> {
                            UserDto userDto = sysUserService.findById(p.getUserId());
                            return SimpleUserDto.builder()
                                    .id(p.getUserId())
                                    .userName(p.getName())
                                    .nickName(p.getUserName())
                                    .extId(userDto.getDept().getName() + p.getUserId())
                                    .build();
                        }).collect(Collectors.toList());
                        break;
                    case ProcessConstants.BPM_EXPRESSION_HALLLEADERSHIP: // 厅领导
                        sysDeptUserPositions = sysDeptUserPositionService.findListByType(PositionEnum.HALL_LEADERSHIP);
                        simpleUserDtoList = sysDeptUserPositions.stream().map(p -> {
                            UserDto userDto = sysUserService.findById(p.getUserId());
                            return SimpleUserDto.builder()
                                    .id(p.getUserId())
                                    .userName(p.getName())
                                    .nickName(p.getUserName())
                                    .extId(userDto.getDept().getName() + p.getUserId())
                                    .build();
                        }).collect(Collectors.toList());
                        break;
                    case ProcessConstants.BPM_EXPRESSION_HEADOFUNIT: // 单位领导
                        // TODO: 写死，后续优化
                        SimpleUserDto simpleUserDto = SimpleUserDto.builder()
                                .id(1213L)
                                .userName("cgf")
                                .nickName("陈根芳")
                                .build();
                        simpleUserDtoList.add(simpleUserDto);
                        break;
                    case ProcessConstants.BPM_EXPRESSION_STARTEROFPROCESS: // 流程发起人
                        simpleUserDto = SimpleUserDto.builder()
                                .id(startUser.getId())
                                .userName(startUser.getUsername())
                                .nickName(startUser.getNickName())
                                .extId(startUser.getDept().getName() + startUser.getId())
                                .build();
                        simpleUserDtoList.add(simpleUserDto);
                        break;
                    case ProcessConstants.BPM_EXPRESSION_APPROVEOFPROCESS: // 节点审批人
                        List simpleUsers = this.findNodeApproveUsers(executionId, taskDefKey);
                        simpleUserDtoList.addAll(simpleUsers);
                        break;
                    case ProcessConstants.BPM_EXPRESSION_CUSTOMBIZUSER: // 自定义业务人员
                        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().executionId(executionId).list();
                        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(historicTaskInstanceList.get(0).getProcessInstanceId()).singleResult();
                        simpleUsers = workflowCustomExtensionService.handleCustomBizUser(processInstance.getProcessDefinitionKey(), taskDefKey, processInstance.getId());
                        if(!CollectionUtils.isEmpty(simpleUsers)) {
                            simpleUserDtoList.addAll(simpleUsers);
                        }
                        break;
                    default:
                        List<String> userList = Arrays.asList(assignee.split(ElAdminConstant.SEPARATOR_COMMA));
                        simpleUserDtoList = userMapperUtil.toConvertToSimpleUserDto(userList);
                }
            }
        }
        return simpleUserDtoList;
    }

    /**
     * 查询流程节点审批用户
     * @param executionId
     * @param userTaskKey
     * @return
     */
    private List findNodeApproveUsers(String executionId, String userTaskKey) {
        List<SimpleUserDto> simpleUserDtoList = new ArrayList();
        if(StringUtils.isBlank(executionId) || StringUtils.isBlank(userTaskKey)) {
            return simpleUserDtoList;
        }
        List<HistoricActivityInstance> historicActivityInstance = historyService.createHistoricActivityInstanceQuery().executionId(executionId).list();
        List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery().processInstanceId(historicActivityInstance.get(0).getProcessInstanceId()).taskDefinitionKey(userTaskKey).list();
        for(HistoricTaskInstance historicTaskInstance : historicTaskInstances) {
            UserDto userDto = sysUserService.findByName(historicTaskInstance.getAssignee());
            SimpleUserDto simpleUserDto = SimpleUserDto.builder()
                    .id(userDto.getId())
                    .userName(userDto.getUsername())
                    .nickName(userDto.getNickName())
                    .extId(userDto.getDept().getName() + userDto.getId())
                    .build();
            simpleUserDtoList.add(simpleUserDto);
        }
        return simpleUserDtoList;
    }

    @Override
    public Boolean sendBacklogList(String title, String formData, List<BacklogUserDto> backlogUserList, ProcessInstance processInstance, String username, String url, String pcUrl, List<AlreadyUserListBO> alreadyUserListBOList) {
        String moduleCode = processInstance.getProcessDefinitionKey();
        return this.sendBacklogHandle(title, formData, backlogUserList, processInstance.getProcessInstanceId(), moduleCode, username, url, pcUrl, alreadyUserListBOList);
    }

    @Override
    public Boolean sendBacklogList(String title, String formData, List<BacklogUserDto> backlogUserList, String procInstId, String username, String url, String pcUrl, List<AlreadyUserListBO> alreadyUserListBOList) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstId).singleResult();
        String moduleCode = historicProcessInstance.getProcessDefinitionKey();
        return this.sendBacklogHandle(title, formData, backlogUserList, procInstId, moduleCode, username, url, pcUrl, alreadyUserListBOList);
    }

    private Boolean sendBacklogHandle(String title, String formData, List<BacklogUserDto> backlogUserList, String procInstId, String moduleCode, String username, String url, String pcUrl, List<AlreadyUserListBO> alreadyUserListBOList) {
        String workflowType = workflowCustomExtensionService.getWorkTypeNameByValue(moduleCode);
        Integer hj = 0;
        JSONObject jsonObject = JSONObject.parseObject(formData);
        if(jsonObject != null && jsonObject.get("hj") != null) {
            if("急".equals(jsonObject.get("hj").toString())) {
                hj = 1;
            }else {
                hj = 0;
            }
        }

        String icon = workflowCustomExtensionService.getWorkTypeIconByProcDefKey(moduleCode);

        BacklogDto.BacklogDtoBuilder backlogDtoBuilder = BacklogDto.builder()
                .appId(restUrlComponent.getBacklogAppId())
                .bizId(procInstId)
                .moduleCode(moduleCode)
                .moduleName(workflowType)
                .title(title)
                .extensionJson(formData)
                .urgent(hj)  // 是否全部紧急(0:否,1:是,2:部分紧急) 默认不紧急
                .url(url)
                .pcUrl(pcUrl)
                .userId(username)
                .logo(icon);

        if(!CollectionUtils.isEmpty(backlogUserList)) {
            backlogDtoBuilder.backlogUserList(backlogUserList);
        }
        if(!CollectionUtils.isEmpty(alreadyUserListBOList)) {
            backlogDtoBuilder.alreadyUserList(alreadyUserListBOList);
        }

        // 事务后发送
        afterTransactionOpt.execute(new DelegatingSecurityContextRunnable(() -> {
                    workflowCustomExtensionService.pushToBacklog(backlogDtoBuilder.build());
                })
        );
        return true;
    }

    private List findEndFlowElement(String processDefId) {
        Process mainProcess = repositoryService.getBpmnModel(processDefId).getMainProcess();
        Collection<FlowElement> list = mainProcess.getFlowElements();
        if(CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        return list.stream().filter(f -> f instanceof EndEvent).collect(Collectors.toList());
    }

    /**
     * 表达式是否匹配
     * @param preExpression
     * @return boolean
     */
    private boolean expressionIsMatch(String preExpression, String executionId) {
        if(StringUtils.isNotEmpty(preExpression) && ProcessConstants.BPM_EXPRESSION_CURRENTUSERISHEAD.equals(preExpression)) {
            SysDeptUserPosition sysDeptUserPosition = sysDeptUserPositionService.findUserByUserId(PositionEnum.HEAD.getValue(), SecurityUtils.getCurrentUserId());
            if(Objects.isNull(sysDeptUserPosition)) {
                return false;
            }
        }else if(StringUtils.isNotEmpty(preExpression) && ProcessConstants.BPM_EXPRESSION_CURRENTUSERISLEADERSHIP.equals(preExpression)) {
            SysDeptUserPosition sysDeptUserPosition = sysDeptUserPositionService.findUserByUserId(PositionEnum.LEADERSHIP.getValue(), SecurityUtils.getCurrentUserId());
            if(Objects.isNull(sysDeptUserPosition)) {
                return false;
            }
        }else if(StringUtils.isNotEmpty(preExpression) && ProcessConstants.BPM_EXPRESSION_CURRENTUSERISNORMAL.equals(preExpression)) {
            SysDeptUserPosition sysDeptUserPosition = sysDeptUserPositionService.findUserByUserIdAndTypeIn(SecurityUtils.getCurrentUserId(), Lists.newArrayList(PositionEnum.HEAD.getValue(), PositionEnum.LEADERSHIP.getValue(), PositionEnum.HALL_LEADERSHIP.getValue()));
            if(Objects.nonNull(sysDeptUserPosition)) {
                return false;
            }
        }else if(StringUtils.isNotEmpty(preExpression) && ("true".equals(preExpression) || "false".equals(preExpression))) {
            return Boolean.parseBoolean(preExpression);
        }else if(StringUtils.isNotEmpty(preExpression) && StringUtils.isNotEmpty(executionId) && FlowableUtils.isExpressionRule(preExpression)) {
            Boolean result = managementService.executeCommand(new ExpressionExecuteCmd(executionId, preExpression));
            if(Objects.isNull(result)) {
                log.warn("未识别的表达式>>>>>>" + preExpression);
                return false;
            }
            return result;
        }else if(StringUtils.isNotEmpty(preExpression)) {
            log.warn("未识别的表达式>>>>>>" + preExpression);
        }
        return true;
    }

    /**
     * 是否要传后续处理人
     * @param bpmnModel
     * @param taskKey
     * @param userTaskList
     * @param executionId
     * @return
     */
    @Override
    public boolean hasNextTaskFlowNode(BpmnModel bpmnModel, String taskKey, List<FlowElement> userTaskList, String executionId) {
        List<FlowElement> flowElementList = this.nextTaskFlowNode(bpmnModel, taskKey, userTaskList, executionId);
        if(CollectionUtils.isEmpty(flowElementList)) {
            return false;
        }

        // 节点属性taskExecType是否有blank
        for(FlowElement flowElement : flowElementList) {
            String taskExecType = flowElement.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_TASKEXECTYPE);
            if(StringUtils.isNotBlank(taskExecType) && taskExecType.equals(ProcessConstants.BPM_TASK_TASKEXECTYPE_BLANK)) {
                return false;
            }else if(flowElement instanceof ServiceTask || flowElement instanceof EndEvent) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取下个任务节点
     * @param bpmnModel    流程定义模型
     * @param taskKey      任务key
     * @param userTaskList 任务节点
     * @param executionId  执行Id
     */
    private List<FlowElement> nextTaskFlowNode(BpmnModel bpmnModel, String taskKey, List<FlowElement> userTaskList, String executionId) {
        if(Objects.isNull(userTaskList)) {
            userTaskList = new ArrayList<>();
        }

        // 当前审批节点
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(taskKey);
        // 输出连线
        List<SequenceFlow> outFlows = flowNode.getOutgoingFlows();
        for(SequenceFlow sequenceFlow : outFlows) {
            String preExpression = null;
            Map<String, List<ExtensionElement>> extensionElements = sequenceFlow.getExtensionElements();
            if(!CollectionUtils.isEmpty(extensionElements)) {
                List<ExtensionElement> extensionElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_PREEXPRESSION);
                if(!CollectionUtils.isEmpty(extensionElementList)) {
                    preExpression = extensionElementList.get(0).getElementText();
                }
            }

            // 如果不匹配不显示后续节点
            if(!expressionIsMatch(preExpression, executionId)) {
                continue;
            }
            // 如果隐藏不显示后续节点
            String flowHidden = "false";
            List<ExtensionElement> extensionTaskUserHiddenElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_FLOWHIDDEN);
            if(!CollectionUtils.isEmpty(extensionTaskUserHiddenElementList)) {
                flowHidden = extensionTaskUserHiddenElementList.get(0).getElementText();
            }
            if(Boolean.parseBoolean(flowHidden)) {
                continue;
            }

            // 下一个审批节点
            FlowElement targetFlow = sequenceFlow.getTargetFlowElement();
            if(targetFlow instanceof UserTask) {
                userTaskList.add(targetFlow);
            }else if(targetFlow instanceof Gateway) {
                // 下个节点为网关
                this.nextTaskFlowNode(bpmnModel, targetFlow.getId(), userTaskList, executionId);
            }else if(targetFlow instanceof ServiceTask || targetFlow instanceof SubProcess) {
                userTaskList.add(targetFlow);
            }else if(targetFlow instanceof EndEvent) {
                // 当前为子流程结束节点，获取父流程后续节点
                FlowElementsContainer parentContainer = targetFlow.getParentContainer();
                if(Objects.nonNull(parentContainer) && parentContainer instanceof SubProcess) {
                    this.nextTaskFlowNode(bpmnModel, parentContainer.getId(), userTaskList, executionId);
                }else {
                    userTaskList.add(targetFlow);
                }
            }
        }
        if(userTaskList.size() == SystemConstant.NUMBER_ONE) {
            FlowElement flowElement = userTaskList.get(0);
            if(flowElement instanceof ServiceTask || flowElement instanceof EndEvent) {
                userTaskList.clear();
            }
        }
        return userTaskList;
    }

    /**
     * 获取下个连接线
     * @param bpmnModel   模型
     * @param taskKey     任务Key
     * @param executionId 执行Id
     */
    private List<FlowSequenceDto> nextSequenceFlowNode(BpmnModel bpmnModel, String taskKey, String executionId, List<FlowSequenceDto> flowSequenceList) {
        if(Objects.isNull(flowSequenceList)) {
            flowSequenceList = new ArrayList<>();
        }

        // 当前审批节点
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(taskKey);

        // 输出连线
        List<SequenceFlow> outFlows = flowNode.getOutgoingFlows();
        for(SequenceFlow sequenceFlow : outFlows) {
            String preExpression = null;
            Map<String, List<ExtensionElement>> extensionElements = sequenceFlow.getExtensionElements();
            if(!CollectionUtils.isEmpty(extensionElements)) {
                List<ExtensionElement> extensionElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_PREEXPRESSION);
                if(!CollectionUtils.isEmpty(extensionElementList)) {
                    preExpression = extensionElementList.get(0).getElementText();
                }
            }
            if(!expressionIsMatch(preExpression, executionId)) {
                continue;
            }
            // 如果隐藏不显示后续
            String flowHidden = "false";
            List<ExtensionElement> extensionTaskUserHiddenElements = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_FLOWHIDDEN);
            if(!CollectionUtils.isEmpty(extensionTaskUserHiddenElements)) {
                flowHidden = extensionTaskUserHiddenElements.get(0).getElementText();
            }
            if(Boolean.parseBoolean(flowHidden)) {
                continue;
            }

            // 下一个审批节点
            FlowElement targetFlow = sequenceFlow.getTargetFlowElement();
            // 下个节点为网关
            if(targetFlow instanceof Gateway) {
                this.nextSequenceFlowNode(bpmnModel, targetFlow.getId(), executionId, flowSequenceList);
            }else if(targetFlow instanceof EndEvent && Objects.nonNull(targetFlow.getParentContainer()) && targetFlow.getParentContainer() instanceof SubProcess) {
                // 当前为子流程结束节点，获取父流程后续节点
                this.nextSequenceFlowNode(bpmnModel, targetFlow.getParentContainer().getId(), executionId, flowSequenceList);
            }else {
                FlowSequenceDto.FlowSequenceDtoBuilder flowSequenceDto = FlowSequenceDto.builder()
                        .sequenceDefKey(sequenceFlow.getId())
                        .sequenceName(sequenceFlow.getName())
                        .sourceDefKey(sequenceFlow.getSourceRef())
                        .targetDefKey(sequenceFlow.getTargetRef());
                if(!CollectionUtils.isEmpty(extensionElements)) {
                    List<ExtensionElement> extensionFrontElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_FORNTPREEXPRESSION);
                    if(!CollectionUtils.isEmpty(extensionFrontElementList)) {
                        flowSequenceDto.preRule(extensionFrontElementList.get(0).getElementText());
                    }

                    List<ExtensionElement> extensionFlowTypeElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_FLOWTYPE);
                    if(!CollectionUtils.isEmpty(extensionFlowTypeElementList)) {
                        flowSequenceDto.flowType(extensionFlowTypeElementList.get(0).getElementText());
                    }

                    List<ExtensionElement> extensionIsDefaultElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_ISDEFAULTFLOW);
                    if(!CollectionUtils.isEmpty(extensionIsDefaultElementList)) {
                        flowSequenceDto.isDefaultFlow(extensionIsDefaultElementList.get(0).getElementText());
                    }

                    List<ExtensionElement> extensionFlowGroupElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_FLOWGROUP);
                    if(!CollectionUtils.isEmpty(extensionFlowGroupElementList)) {
                        flowSequenceDto.flowGroup(extensionFlowGroupElementList.get(0).getElementText());
                    }

                    List<ExtensionElement> extensionTaskUserHiddenElementList = extensionElements.get(ProcessConstants.BPM_SEQUENCEFLOW_EXPRESSION_FLOWHIDDEN);
                    if(!CollectionUtils.isEmpty(extensionTaskUserHiddenElementList)) {
                        flowSequenceDto.flowHidden(extensionTaskUserHiddenElementList.get(0).getElementText());
                    }
                }

                flowSequenceList.add(flowSequenceDto.build());
            }
        }
        return flowSequenceList;
    }

    @Override
    public ResultJson addSignTask(AddSignTaskVo addSignTaskVo) {
        List<TaskAssigneeVo> assigneeVos = addSignTaskVo.getTaskAssignee();
        for(TaskAssigneeVo assigneeVo : assigneeVos) {
            runtimeService.addMultiInstanceExecution(assigneeVo.getTaskKey(), addSignTaskVo.getProcessInstanceId(), Collections.singletonMap(ProcessConstants.BPM_TASK_ASSIGNEE, assigneeVo.getUsername()));
        }
        return ResultJson.generateResult("加签成功");
    }

    @Override
    public ResultJson reduceSignTask(ReduceSignTaskVo reduceSignTaskVo) {
        List<FlowNodeVo> flowNodeVos = reduceSignTaskVo.getFlowNodeVos();
        if(CollectionUtils.isEmpty(flowNodeVos)) {
            return ResultJson.generateResult("减签节点用户不能为空", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }
        flowNodeVos.forEach(flowNodeVo -> {
            Task task = taskService.createTaskQuery().processInstanceId(reduceSignTaskVo.getProcessInstanceId()).taskId(flowNodeVo.getTaskId()).singleResult();
            runtimeService.deleteMultiInstanceExecution(task.getExecutionId(), false);
        });
        return ResultJson.generateResult("减签成功");
    }

    @Override
    @Transactional
    public ResultJson signTask(SignTaskVo signTaskVo) {
        List<SignTaskAssigneeVo> signTaskAssigneeVos = signTaskVo.getTaskAssignee();
        ResultJson resultJson = flowInstanceService.getTaskAssignees(signTaskVo.getProcessInstanceId());
        if(CollectionUtils.isEmpty(signTaskAssigneeVos) || Objects.isNull(resultJson.getData())) {
            return ResultJson.generateResult("加减签节点用户不能为空", ResultCodeEnum.ILLEGAL_ARGUMENT.getCode());
        }

        List<FlowNextDto> flowNextDtos = (List<FlowNextDto>) resultJson.getData();
        List<SignTaskAssigneeVo> sourceSignTaskAssigneeVos = new ArrayList<>();
        for(FlowNextDto flowNextDto : flowNextDtos) {
            for(SimpleUserDto simpleUserDto : flowNextDto.getCandidateUser()) {
                SignTaskAssigneeVo signTaskAssigneeVo = new SignTaskAssigneeVo();
                signTaskAssigneeVo.setTaskKey(flowNextDto.getTaskDefKey());
                signTaskAssigneeVo.setUsername(simpleUserDto.getUserName());
                signTaskAssigneeVo.setTaskId(simpleUserDto.getTaskId());
                sourceSignTaskAssigneeVos.add(signTaskAssigneeVo);
            }
        }

        List<SignTaskAssigneeVo> addSignTaskAssigneeVos = new ArrayList<>(signTaskAssigneeVos);
        addSignTaskAssigneeVos.removeAll(sourceSignTaskAssigneeVos);
        List<SignTaskAssigneeVo> reduceSignTaskAssigneeVos = new ArrayList<>(sourceSignTaskAssigneeVos);
        reduceSignTaskAssigneeVos.removeAll(signTaskAssigneeVos);

        String taskId = sourceSignTaskAssigneeVos.get(0).getTaskId();
        HistoricTaskInstance hisTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
        // bpm定义模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(hisTaskInstance.getProcessDefinitionId());

        // 减签
        for(SignTaskAssigneeVo assigneeVo : reduceSignTaskAssigneeVos) {
            Task task = taskService.createTaskQuery().processInstanceId(signTaskVo.getProcessInstanceId()).taskId(assigneeVo.getTaskId()).singleResult();
            if(task.isSuspended()) {
                throw new CustomException(task.getName() + "在锁定状态不支持加减签");
            }
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());
            FlowElementsContainer parentContainer = currentFlowNode.getParentContainer();
            String taskExecType = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_TASKEXECTYPE);
            if(currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
                runtimeService.deleteMultiInstanceExecution(task.getExecutionId(), false);
            }else if(Objects.nonNull(parentContainer) && parentContainer instanceof SubProcess && ((SubProcess) parentContainer).getBehavior() instanceof MultiInstanceActivityBehavior) {
                // 如果是子流程节点的话需要删除子流程
                Execution execution = runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult();
                runtimeService.deleteMultiInstanceExecution(execution.getParentId(), false);
            }else if(StringUtils.isNotEmpty(taskExecType) && taskExecType.equals(WdTaskExecTypeEnum.TASKEXECTYPE_FORK.getValue())) { // 分发
                throw new CustomException(task.getName() + "分发节点不支持减签");
            }
        }

        // 加签
        for(SignTaskAssigneeVo assigneeVo : addSignTaskAssigneeVos) {
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(assigneeVo.getTaskKey());
            String taskExecType = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_TASKEXECTYPE);
            if((Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior)) {
                runtimeService.addMultiInstanceExecution(assigneeVo.getTaskKey(), signTaskVo.getProcessInstanceId(), Collections.singletonMap(ProcessConstants.BPM_TASK_ASSIGNEE, assigneeVo.getUsername()));
            }else if(StringUtils.isNotEmpty(taskExecType) && taskExecType.equals(WdTaskExecTypeEnum.TASKEXECTYPE_FORK.getValue())) { // 分发
                Task task = taskService.createTaskQuery().processInstanceId(signTaskVo.getProcessInstanceId()).taskDefinitionKey(assigneeVo.getTaskKey()).list().get(0);
                this.distributeNewTask(task, currentFlowNode, assigneeVo.getUsername());
            }else {
                Task task = taskService.createTaskQuery().processInstanceId(signTaskVo.getProcessInstanceId()).taskDefinitionKey(assigneeVo.getTaskKey()).singleResult();
                task.setAssignee(assigneeVo.getUsername());
                taskService.setAssignee(task.getId(), assigneeVo.getUsername());
                runtimeService.dispatchEvent(FlowableEventBuilder.createEntityEvent(FlowableEngineEventType.TASK_CREATED, task));
            }
        }
        return ResultJson.generateResult("加减签成功");
    }

    @Override
    public boolean taskLock(FlowTaskVo flowTaskVo) {
        String taskId = flowTaskVo.getBpmData().getTaskId();
        if(StringUtils.isBlank(taskId)) {
            return false;
        }
        TaskEntityImpl currentTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).active().singleResult();
        if(currentTask.isSuspended()) {
            return true;
        }
        currentTask.setSuspensionState(SuspensionState.SUSPENDED.getStateCode());
        taskService.saveTask(currentTask);
        return true;
    }

    @Override
    public boolean taskUnLock(FlowTaskVo flowTaskVo) {
        String taskId = flowTaskVo.getBpmData().getTaskId();
        if(StringUtils.isBlank(taskId)) {
            return false;
        }
        TaskEntityImpl currentTask = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();
        if(!currentTask.isSuspended()) {
            return true;
        }
        currentTask.setSuspensionState(SuspensionState.ACTIVE.getStateCode());
        taskService.saveTask(currentTask);
        return true;
    }

    @Override
    public Map<String, Object> findHistoricProcessList(HistoricProcessBO bo, Pageable pageable) {
        // 获取当前用户的权限 bpm:admin 流程管理员可以查看所有流程
        if(!workflowCustomExtensionService.isBpmAdmin() && StringUtils.isBlank(bo.getUsername())) {
            bo.setUsername(SecurityUtils.getCurrentUsername());
        }

        StringBuilder countSql = new StringBuilder("SELECT count(DISTINCT hpi.ID_) ");
        StringBuilder querySql = new StringBuilder("SELECT DISTINCT hpi.* ");
        StringBuilder sql = new StringBuilder("FROM " + managementService.getTableName(HistoricProcessInstance.class) + " hpi ");
        sql.append("LEFT JOIN " + managementService.getTableName(ProcessDefinition.class) + " pd ON hpi.PROC_DEF_ID_ = pd.ID_ ");
        sql.append("WHERE 1 = 1 ");

        //查询对应公文类型数据
        if(StringUtils.isNotEmpty(bo.getProcessDefinitionKey())) {
            sql.append("AND pd.KEY_ = #{procDefKey} ");
        }else if(bo.getProcessDefinitionType() != null) {
            if(bo.getProcessDefinitionType() == 1) {
                sql.append("AND pd.KEY_ in ( ");
                sql.append(com.wangda.oa.utils.StringUtils.formatInCondition(workflowCustomExtensionService.getProcessDefinitionKeyList(CategoryTypeEnum.CATEGORY_GWGL.getValue())));
                sql.append(") ");
            }else if(bo.getProcessDefinitionType() == 2) {
                sql.append("AND pd.KEY_ in ( ");
                sql.append(com.wangda.oa.utils.StringUtils.formatInCondition(workflowCustomExtensionService.getProcessDefinitionKeyList(CategoryTypeEnum.CATEGORY_XZFW.getValue())));
                sql.append(") ");
            }
        }

        //流程名称
        if(StringUtils.isNotBlank(bo.getName())) {
            sql.append("AND hpi.NAME_ like #{name} ");
        }

        // 时间范围
        if(!CollectionUtils.isEmpty(bo.getTimeRange())) {
            sql.append("AND hpi.START_TIME_ between #{startTime} and #{endTime} ");
        }

        //查询对应年份
        if(bo.getYear() != null) {
            sql.append("AND YEAR(hpi.START_TIME_) = #{year} ");
        }

        //标题查询
        if(StringUtils.isNotBlank(bo.getBt())) {
            sql.append("AND EXISTS (select ID_ from " + managementService.getTableName(HistoricVariableInstance.class) + " where hpi.PROC_INST_ID_ = PROC_INST_ID_ and NAME_ = '" + ProcessConstants.BPM_FORM_TITLE + "' and TEXT_ like #{formTitle}) ");
        }

        // 已提交的
        sql.append("AND EXISTS (select ID_ from " + managementService.getTableName(HistoricVariableInstance.class) + " where hpi.PROC_INST_ID_ = PROC_INST_ID_ and NAME_ = #{varinstName} ) ");

        // 状态
        if(bo.getStatus() != null) {
            if(ProcessConstants.QUERY_STATUS_DB == bo.getStatus()) {
                //待办
                sql.append("AND EXISTS (select ID_ from " + managementService.getTableName(HistoricTaskInstance.class) + " where hpi.PROC_INST_ID_ = PROC_INST_ID_ and END_TIME_ is NULL and (DELETE_REASON_ is NULL or DELETE_REASON_ like #{deleteReason}) ");
            }else if(ProcessConstants.QUERY_STATUS_YJWWJ == bo.getStatus()) {
                //已办
                sql.append("AND EXISTS (select ID_ from " + managementService.getTableName(HistoricTaskInstance.class) + " where hpi.PROC_INST_ID_ = PROC_INST_ID_ and END_TIME_ is not NULL and (DELETE_REASON_ is NULL or DELETE_REASON_ like #{deleteReason}) ");
            }
            if(StringUtils.isNotBlank(bo.getUsername())) {
                sql.append("AND ASSIGNEE_ = #{assignee}");
            }
            sql.append(") ");
        }else if(StringUtils.isNotBlank(bo.getUsername())) {
            // 我处理的
            sql.append("AND (EXISTS (select ID_ from " + managementService.getTableName(HistoricTaskInstance.class) + " where hpi.PROC_INST_ID_ = PROC_INST_ID_ and ASSIGNEE_ = #{assignee} and (DELETE_REASON_ is NULL or DELETE_REASON_ like #{deleteReason})) ");

            // 或抄送给我的
            sql.append("OR EXISTS (select ID_ from " + managementService.getTableName(HistoricVariableInstance.class) + " where hpi.PROC_INST_ID_ = PROC_INST_ID_ and NAME_ like #{copyToPrefix} and TEXT_ like #{copyToAssignee})) ");
        }

        sql.append(" ORDER BY hpi.ID_ DESC");

        NativeQuery nativeHistoricProcessInstanceQuery = historyService.createNativeHistoricProcessInstanceQuery()
                .parameter("varinstName", ProcessConstants.BPM_BPM_STARTEDBY);

        // 设置抄送查询
        nativeHistoricProcessInstanceQuery.parameter("copyToPrefix", ProcessConstants.BPM_BPM_COPYTO_PREFIX + "%");
        nativeHistoricProcessInstanceQuery.parameter("copyToAssignee", "%" + bo.getUsername() + "%");

        //标题查询
        if(StringUtils.isNotBlank(bo.getBt())) {
            nativeHistoricProcessInstanceQuery.parameter("formTitle", "%" + bo.getBt() + "%");
        }

        //流程名称查询
        if(StringUtils.isNotBlank(bo.getName())) {
            nativeHistoricProcessInstanceQuery.parameter("name", "%" + bo.getName() + "%");
        }

        if(!CollectionUtils.isEmpty(bo.getTimeRange())) {
            try {
                Date startDate = DateUtils.parseDate(bo.getTimeRange().get(0), new String[]{"yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm"});
                Date endDate = DateUtils.parseDate(bo.getTimeRange().get(1), new String[]{"yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm"});
                nativeHistoricProcessInstanceQuery.parameter("startTime", startDate);
                nativeHistoricProcessInstanceQuery.parameter("endTime", endDate);
            }catch(ParseException e) {
                e.printStackTrace();
            }
        }

        //查询对应年份
        if(bo.getYear() != null) {
            nativeHistoricProcessInstanceQuery.parameter("year", DateUtil.parse(bo.getYear() + "", "yyyy"));
        }

        //查询对应公文类型数据
        if(StringUtils.isNotEmpty(bo.getProcessDefinitionKey())) {
            nativeHistoricProcessInstanceQuery.parameter("procDefKey", bo.getProcessDefinitionKey());
        }

        if(bo.getStatus() != null || StringUtils.isNotBlank(bo.getUsername())) {
            nativeHistoricProcessInstanceQuery.parameter(ProcessConstants.BPM_TASK_ASSIGNEE, bo.getUsername()).parameter("deleteReason", "%" + ProcessConstants.DELETE_REASON_CHANGE + "%");
        }

        //查询流程相关信息
        List<HistoricProcessInstance> historicProcessInstanceList = nativeHistoricProcessInstanceQuery.sql(querySql.append(sql).toString()).listPage(pageable.getPageNumber() * pageable.getPageSize(), pageable.getPageSize());
        Map map = Maps.newHashMap();
        map.put("content", historicProcessInstanceList);
        map.put("totalElements", nativeHistoricProcessInstanceQuery.sql(countSql.append(sql).toString()).count());
        return map;
    }

    @Override
    public Map<String, Object> queryAdministrative(HistoricProcessBO bo, Pageable pageable) {
        Map<String, Object> map = this.findHistoricProcessList(bo, pageable);
        List<HistoricProcessInstance> historicProcessInstanceList = (List<HistoricProcessInstance>) map.get("content");
        List<FlowTaskDto> flowList = new ArrayList<>();
        //获取流程实例id
        List<String> bpmInstanceId = historicProcessInstanceList.stream().map(HistoricProcessInstance::getId).distinct().collect(Collectors.toList());
        Map<String, Task> myTaskList = new HashMap<>();
        Map<String, List<Task>> taskMap = new HashMap<>();
        Map<String, List<HistoricVariableInstance>> variablesMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(bpmInstanceId)) {
            myTaskList = taskService.createTaskQuery().processInstanceIdIn(bpmInstanceId).taskAssignee(SecurityUtils.getCurrentUsername()).list().stream().collect(Collectors.toMap(Task::getProcessInstanceId, a -> a, (k1, k2) -> k1));
            variablesMap.putAll(historyService.createHistoricVariableInstanceQuery().variableName(ProcessConstants.BPM_FORM_TITLE).executionIds(Sets.newHashSet(bpmInstanceId)).list().stream().collect(Collectors.groupingBy(HistoricVariableInstance::getProcessInstanceId)));

            // 任务
            taskMap = taskService.createTaskQuery().processInstanceIdIn(bpmInstanceId).list().stream().collect(Collectors.groupingBy(Task::getProcessInstanceId));
        }

        // 流程定义信息
        Map<String, ProcessDefinition> processDefinitionMap = new HashMap<>();
        Set<String> processDefIds = historicProcessInstanceList.stream().map(HistoricProcessInstance::getProcessDefinitionId).collect(Collectors.toSet());
        if(!CollectionUtils.isEmpty(processDefIds)) {
            Map<String, ProcessDefinition> definitionMap = repositoryService.createProcessDefinitionQuery().processDefinitionIds(processDefIds).list().stream().collect(Collectors.toMap(ProcessDefinition::getId, v -> v, (k1, k2) -> k1));
            processDefinitionMap.putAll(definitionMap);
        }

        for(HistoricProcessInstance hisIns : historicProcessInstanceList) {
            FlowTaskDto flowTask = new FlowTaskDto();
            flowTask.setCreateTime(hisIns.getStartTime());
            flowTask.setFinishTime(hisIns.getEndTime());
            flowTask.setProcInsId(hisIns.getId());

            // 流程状态
            if(Objects.nonNull(hisIns.getEndTime())) {
                String procStatus = hisIns.getBusinessStatus();
                if(StringUtils.isNotEmpty(procStatus)) {
                    ProcStatusEnum procStatusEnum = ProcStatusEnum.getProcStatusEnumByValue(procStatus);
                    flowTask.setProcStatus(procStatusEnum);
                }else {
                    flowTask.setProcStatus(ProcStatusEnum.TG);
                }
            }else {
                flowTask.setProcStatus(ProcStatusEnum.INPROGRESS);
            }

            if(StringUtils.isNotEmpty(hisIns.getStartUserId())) {
                UserDto startUser = sysUserService.findById(Long.parseLong(hisIns.getStartUserId()));
                FlowableUtils.setStartUserInfo(flowTask, startUser);
            }

            // 计算耗时
            if(Objects.nonNull(hisIns.getEndTime())) {
                long time = hisIns.getEndTime().getTime() - hisIns.getStartTime().getTime();
                flowTask.setDuration(FlowableUtils.getDate(time));
            }else {
                long time = System.currentTimeMillis() - hisIns.getStartTime().getTime();
                flowTask.setDuration(FlowableUtils.getDate(time));
            }
            // 流程定义信息
            ProcessDefinition pd = processDefinitionMap.get(hisIns.getProcessDefinitionId());
            if(Objects.nonNull(pd)) {
                flowTask.setDeployId(pd.getDeploymentId());
                flowTask.setProcDefName(pd.getName());
                flowTask.setProcDefVersion(pd.getVersion());
                flowTask.setCategory(pd.getCategory());
                flowTask.setProcDefVersion(pd.getVersion());
            }
            flowTask.setProcDefId(hisIns.getProcessDefinitionId());

            // 设置标题
            List<HistoricVariableInstance> variables = variablesMap.get(hisIns.getId());
            if(!CollectionUtils.isEmpty(variables)) {
                Object title = variables.get(0).getValue();
                if(Objects.nonNull(title)) {
                    flowTask.setFormTitle(title.toString());
                }
            }

            //办理状态
            if(myTaskList.containsKey(hisIns.getId())) {
                flowTask.setBpmStatus("待办");
            }else {
                flowTask.setBpmStatus("已办");
            }

            // 当前所处流程
            List<Task> tasks = taskMap.get(hisIns.getId());
            if(!CollectionUtils.isEmpty(tasks)) {
                Task task = tasks.get(0);
                flowTask.setTaskName(task.getName());
                flowTask.setTaskId(task.getId());
                // 指定审批人员信息
                if(StringUtils.isNotBlank(task.getAssignee())) {
                    UserDto sysUser = sysUserService.findByName(task.getAssignee());
                    FlowableUtils.setAssigneeUserInfo(flowTask, sysUser);
                }else {
                    // 候选审批人员信息
                    List<IdentityLink> linksForTask = taskService.getIdentityLinksForTask(task.getId());
                    StringBuilder stringBuilder = new StringBuilder();
                    for(IdentityLink identityLink : linksForTask) {
                        if(ProcessConstants.BPM_TASK_ASSIGNEE.equals(identityLink.getType())) {
                            if(StringUtils.isNotBlank(identityLink.getUserId())) {
                                UserDto sysUser = sysUserService.findByName((identityLink.getUserId()));
                                stringBuilder.append(sysUser.getNickName()).append(",");
                            }
                            if(StringUtils.isNotBlank(identityLink.getGroupId())) {
                                RoleDto sysRole = sysRoleService.findById(Long.parseLong(identityLink.getGroupId()));
                                stringBuilder.append(sysRole.getName()).append(",");
                            }
                        }
                    }
                    if(stringBuilder.length() > 0) {
                        flowTask.setCandidate(stringBuilder.substring(0, stringBuilder.length() - 1));
                    }
                }
            }else {
                List<HistoricTaskInstance> historicTaskInstance = historyService.createHistoricTaskInstanceQuery().processInstanceId(hisIns.getId()).orderByHistoricTaskInstanceEndTime().desc().list();
                flowTask.setTaskId(historicTaskInstance.get(0).getId());
            }
            flowList.add(flowTask);
        }
        Map<String, Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", flowList);
        returnMap.put("totalElements", map.get("totalElements"));
        return returnMap;
    }

    @Override
    public void saveToBeRead(TaskUserReadVo taskUserReadVo) {
        BpmTaskUserRead bpmTaskUserRead = bpmTaskUserReadRepository.findFirstByProcessInstanceIdAndAssignee(taskUserReadVo.getProcessInstanceId(), taskUserReadVo.getAssignee());
        if(Objects.nonNull(bpmTaskUserRead)) {
            bpmTaskUserRead.setFirstReadTime(null);
            bpmTaskUserRead.setLastReadTime(null);
        }else {
            bpmTaskUserRead = new BpmTaskUserRead();
            if(StringUtils.isBlank(taskUserReadVo.getAssignee())) {
                taskUserReadVo.setAssignee(SecurityUtils.getCurrentUsername());
            }
            bpmTaskUserRead.setProcessDefKey(taskUserReadVo.getProcessDefKey());
            bpmTaskUserRead.setProcessInstanceId(taskUserReadVo.getProcessInstanceId());
            bpmTaskUserRead.setAssignee(taskUserReadVo.getAssignee());
        }
        bpmTaskUserRead.setBt(taskUserReadVo.getBt());
        bpmTaskUserRead.setTaskId(taskUserReadVo.getTaskId());
        bpmTaskUserReadRepository.save(bpmTaskUserRead);
    }

    @Override
    public org.springframework.data.domain.Page<BpmTaskUserRead> toBeReadList(TaskToBeReadQueryCriteria taskToBeReadQueryCriteria, Pageable pageable) {
        if(StringUtils.isBlank(taskToBeReadQueryCriteria.getAssignee())) {
            taskToBeReadQueryCriteria.setAssignee(SecurityUtils.getCurrentUsername());
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        CriteriaQuery<BpmTaskUserRead> cbQuery = cb.createQuery(BpmTaskUserRead.class);
        Root<BpmTaskUserRead> root = cbQuery.from(BpmTaskUserRead.class);
        cbQuery = cbQuery.select(root)
                .distinct(true)
                .where(QueryHelp.getPredicate(root, taskToBeReadQueryCriteria, cb))
                .groupBy(root.get("processInstanceId"))
                .orderBy(new OrderImpl(root.get("createDate"), false));

        Query query = entityManager.createQuery(cbQuery);
        if(Objects.nonNull(pageable)) { //分页
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
            query.setMaxResults(pageable.getPageSize());
        }
        List<BpmTaskUserRead> content = query.getResultList();

        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<BpmTaskUserRead> countRoot = countQuery.from(BpmTaskUserRead.class);
        countQuery = countQuery.select(cb.countDistinct(countRoot.get("processInstanceId")))
                .where(QueryHelp.getPredicate(countRoot, taskToBeReadQueryCriteria, cb));
        Long totalElements = entityManager.createQuery(countQuery).getSingleResult();

        org.springframework.data.domain.Page<BpmTaskUserRead> result = new PageImpl(content, pageable, totalElements);
        return result;
    }

    @Override
    public org.springframework.data.domain.Page<BpmTaskUserRead> hasReadList(TaskHasReadQueryCriteria taskHasReadQueryCriteria, Pageable pageable) {
        if(StringUtils.isBlank(taskHasReadQueryCriteria.getAssignee())) {
            taskHasReadQueryCriteria.setAssignee(SecurityUtils.getCurrentUsername());
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        CriteriaQuery<BpmTaskUserRead> cbQuery = cb.createQuery(BpmTaskUserRead.class);
        Root<BpmTaskUserRead> root = cbQuery.from(BpmTaskUserRead.class);
        cbQuery = cbQuery.select(root)
                .distinct(true)
                .where(QueryHelp.getPredicate(root, taskHasReadQueryCriteria, cb))
                .groupBy(root.get("processInstanceId"))
                .orderBy(new OrderImpl(root.get("firstReadTime"), false));

        Query query = entityManager.createQuery(cbQuery);
        if(Objects.nonNull(pageable)) { //分页
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
            query.setMaxResults(pageable.getPageSize());
        }
        List<BpmTaskUserRead> content = query.getResultList();

        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<BpmTaskUserRead> countRoot = countQuery.from(BpmTaskUserRead.class);
        countQuery = countQuery.select(cb.countDistinct(countRoot.get("processInstanceId")))
                .where(QueryHelp.getPredicate(countRoot, taskHasReadQueryCriteria, cb));
        Long totalElements = entityManager.createQuery(countQuery).getSingleResult();

        org.springframework.data.domain.Page<BpmTaskUserRead> result = new PageImpl(content, pageable, totalElements);
        return result;
    }

    @Override
    public boolean checkBatchHandleTask(String procInstanceId) {
        // 是否可以批量处理，处理意见可以为空，后续节点有明确处理人
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(procInstanceId).singleResult();
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(procInstanceId).taskAssignee(SecurityUtils.getCurrentUsername()).active().list();
        Task task = null;
        if(!CollectionUtils.isEmpty(taskList)) {
            task = taskList.get(0);
        }else {
            log.warn("checkBatchHandleTask 当前处理人没有该流程任务 username:" + SecurityUtils.getCurrentUsername() + " procInstanceId:" + procInstanceId);
            return false;
        }
        // 锁定状态
        if(task.isSuspended()) {
            return false;
        }

        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(task.getTaskDefinitionKey());
        if(Objects.isNull(currentFlowNode)) {
            return false;
        }

        List<FlowSequenceDto> flowSequenceDtoList = this.nextSequenceFlowNode(bpmnModel, task.getTaskDefinitionKey(), task.getExecutionId(), null);
        List<String> isDefaultTaskList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(flowSequenceDtoList)) {
            // 如果有前置表达式不能批量
            for(FlowSequenceDto flowSequenceDto : flowSequenceDtoList) {
                if(StringUtils.isNotEmpty(flowSequenceDto.getPreRule())) {
                    return false;
                }
            }

            // 后续多条中有唯一选择的且没有默认选中的
            if(flowSequenceDtoList.size() > 1) {
                boolean hasDefaultFlow = false;
                for(FlowSequenceDto flowSequenceDto : flowSequenceDtoList) {
                    if(Boolean.TRUE.toString().equals(flowSequenceDto.getIsDefaultFlow())) {
                        isDefaultTaskList.add(flowSequenceDto.getTargetDefKey());
                        hasDefaultFlow = true;
                    }
                }
                if(!hasDefaultFlow) {
                    return false;
                }
            }
        }

        // 后续节点是否有默认处理人
        UserDto startUser = sysUserService.findById(Long.parseLong(historicProcessInstance.getStartUserId()));
        if(Objects.isNull(startUser)) {
            return false;
        }
        List<FlowNextDto> flowNextDtos = this.nextUserTaskHandle(bpmnModel, task.getTaskDefinitionKey(), task.getExecutionId(), startUser);
        if(!CollectionUtils.isEmpty(flowNextDtos)) {
            // 如果有一个节点
            if(flowNextDtos.size() == 1) {
                if(CollectionUtils.isEmpty(flowNextDtos.get(0).getCandidateUser())) {
                    return false;
                }
            }else {
                // 如果后续有多个节点，选中节点没有处理人
                for(FlowNextDto flowNextDto : flowNextDtos) {
                    if(CollectionUtils.isEmpty(flowNextDto.getCandidateUser()) && isDefaultTaskList.contains(flowNextDto.getTaskDefKey())) {
                        return false;
                    }
                }
            }
        }

        // 后续多个节点中如果有单任务节点的且没有默认选中的
        return true;
    }

    @Override
    @Transactional
    public void batchCompleteTask(List<String> procInstanceIds, String remark, String nextTaskKey) throws Exception {
        String currentUsername = SecurityUtils.getCurrentUsername();
        UserDto userDto = sysUserService.findByName(currentUsername);

        for(String procInstanceId : procInstanceIds) {
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(procInstanceId).taskAssignee(currentUsername).active().list();
            Task myTask = null;
            if(!CollectionUtils.isEmpty(taskList)) {
                myTask = taskList.get(0);
            }else {
                log.error("batchCompleteTask 当前处理人没有该流程任务 username:" + currentUsername + " procInstanceId:" + procInstanceId);
                continue;
            }

            ProcessInstance processInstance =
                    runtimeService.createProcessInstanceQuery().processInstanceId(procInstanceId).includeProcessVariables().singleResult();

            if(Objects.isNull(processInstance) || Objects.isNull(myTask)) {
                throw new CustomException("任务已办理或已撤回");
            }
            if(myTask.isSuspended()) {
                throw new CustomException("该任务已锁定，暂无法办理");
            }

            if(!workflowCustomExtensionService.isBpmAdmin() && StringUtils.isNotEmpty(myTask.getAssignee()) && !(myTask.getAssignee().equals(currentUsername))) {
                log.warn("batchCompleteTask 用户任务不匹配：taskId: " + myTask.getId() + " assignee:" + currentUsername);
                continue;
            }

            // 变量信息
            Map<String, Object> variables = processInstance.getProcessVariables();

            // bpm定义模型
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
            Process process = bpmnModel.getProcess(null);

            // 当前节点
            FlowNode currentFlowNode = (FlowNode) bpmnModel.getFlowElement(myTask.getTaskDefinitionKey());

            // 获取选中节点
            List<FlowSequenceDto> flowSequenceDtoList = this.nextSequenceFlowNode(bpmnModel, myTask.getTaskDefinitionKey(), myTask.getExecutionId(), null);
            List<String> isDefaultTaskList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(flowSequenceDtoList)) {
                if(StringUtils.isNotEmpty(nextTaskKey)) {
                    List<String> nextTargetDefKeyList = flowSequenceDtoList.stream().map(FlowSequenceDto::getTargetDefKey).collect(Collectors.toList());
                    if(!nextTargetDefKeyList.contains(nextTaskKey)) {
                        throw new CustomException("参数错误，后续节点没有该Key");
                    }
                    isDefaultTaskList.add(nextTaskKey);
                }else {
                    // 后续多条中有唯一选择的且没有默认选中的
                    if(flowSequenceDtoList.size() > 1) {
                        for(FlowSequenceDto flowSequenceDto : flowSequenceDtoList) {
                            if(Boolean.TRUE.toString().equals(flowSequenceDto.getIsDefaultFlow())) {
                                isDefaultTaskList.add(flowSequenceDto.getTargetDefKey());
                            }
                        }
                    }else {
                        isDefaultTaskList.add(flowSequenceDtoList.get(0).getTargetDefKey());
                    }
                }
            }

            UserDto startUser = sysUserService.findById(Long.parseLong(processInstance.getStartUserId()));
            List<FlowNextDto> flowNextDtos = this.nextUserTaskHandle(bpmnModel, myTask.getTaskDefinitionKey(), myTask.getExecutionId(), startUser);
            StringBuilder nextAssigneeBuilder = new StringBuilder();
            StringBuilder nextTaskNodesBuilder = new StringBuilder();
            if(!CollectionUtils.isEmpty(flowNextDtos)) {
                for(FlowNextDto flowNextDto : flowNextDtos) {
                    if(isDefaultTaskList.contains(flowNextDto.getTaskDefKey())) {
                        for(SimpleUserDto simpleUserDto : flowNextDto.getCandidateUser()) {
                            nextAssigneeBuilder.append(simpleUserDto.getUserName()).append(ElAdminConstant.SEPARATOR_DOLLAR)
                                    .append(flowNextDto.getTaskDefKey()).append(ElAdminConstant.SEPARATOR_COMMA);
                        }
                        if(!CollectionUtils.isEmpty(flowNextDto.getCandidateUser()) && nextTaskNodesBuilder.indexOf(flowNextDto.getTaskDefKey()) == -1) {
                            nextTaskNodesBuilder.append(flowNextDto.getTaskDefKey()).append(ElAdminConstant.SEPARATOR_COMMA);
                        }
                    }
                }
            }
            if(nextAssigneeBuilder.length() > 0) {
                nextAssigneeBuilder = nextAssigneeBuilder.deleteCharAt(nextAssigneeBuilder.length() - 1);
            }
            if(nextTaskNodesBuilder.length() > 0) {
                nextTaskNodesBuilder = nextTaskNodesBuilder.deleteCharAt(nextTaskNodesBuilder.length() - 1);
            }

            String taskNodes = nextTaskNodesBuilder.toString();
            String nextAssignee = nextAssigneeBuilder.toString();
            if(StringUtils.isBlank(nextAssignee)) {
                boolean hasNextTaskFlowNode = this.hasNextTaskFlowNode(bpmnModel, myTask.getTaskDefinitionKey(), null, myTask.getExecutionId());
                if(hasNextTaskFlowNode && processELService.isLastAssignee(bpmnModel, myTask)) {
                    log.error("该流程后续办理人没有，暂不能提交，请刷新页面后再次提交");
                    continue;
                }
            }

            // 意见
            if(StringUtils.isEmpty(remark)) {
                remark = "已阅";
            }
            variables.put(ProcessConstants.BPM_BPM_APPROVALREMARK, remark);
            taskService.addComment(myTask.getId(), procInstanceId, TaskCommentTypeEnum.SP.name(), remark);

            // 意见上表单
            String remarkToFormField = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKTOFORMFIELD);
            if(StringUtils.isNotBlank(remarkToFormField)) {
                FlowFormRemarkDto flowFormRemarkDto = FlowFormRemarkDto.builder()
                        .remark(remark)
                        .username(userDto.getUsername())
                        .nickName(userDto.getNickName())
                        .deptId(userDto.getDeptId())
                        .deptName(userDto.getDept().getName())
                        .processedTime(new Date())
                        .build();

                boolean append = true;
                String remarkCoverd = currentFlowNode.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_TASK_REMARKCOVERD);
                if(StringUtils.isNotBlank(remarkCoverd) && Boolean.parseBoolean(remarkCoverd)) {
                    append = false;
                }

                String processRemarkSort = process.getAttributeValue(ProcessConstants.BPM_WD_NAMESPACE, ProcessConstants.BPM_PROCESS_REMARK_SORT);
                boolean insertBefore = false;
                if(StringUtils.isNotBlank(processRemarkSort)) {
                    insertBefore = Boolean.parseBoolean(processRemarkSort);
                }
                ResponseInfo responseInfo = formTemplateService.updateFormFieldData(Long.parseLong(variables.get(ProcessConstants.BPM_FORM_TEMPLATE_ID).toString()), processInstance.getProcessInstanceId(), remarkToFormField, flowFormRemarkDto, append, insertBefore);
                JSONObject formData = (JSONObject) variables.get(ProcessConstants.BPM_FORM_DATA);
                if(ResultCodeEnum.SUCCESS.getCode() == responseInfo.getCode()) {
                    // 更新意见上表单数据
                    JSONObject jsonObject = (JSONObject) responseInfo.getData();
                    List<String> remarkToFormFieldList = FlowableUtils.findAllRemarkToFormField(process);
                    for(String key : remarkToFormFieldList) {
                        if(jsonObject.containsKey(key) && Objects.nonNull(jsonObject.get(key))) {
                            formData.put(key, jsonObject.get(key));
                        }
                    }
                }else {
                    formTemplateService.updateVariableFormFieldData(formData, remarkToFormField, flowFormRemarkDto, append, insertBefore);
                }
                variables.replace(ProcessConstants.BPM_FORM_DATA, formData);
            }

            // 设置当前任务处理人员
            if(StringUtils.isBlank(myTask.getAssignee())) {
                taskService.setAssignee(myTask.getId(), userDto.getUsername());
            }

            // 获取后续节点
            variables.put(ProcessConstants.BPM_TASK_DEF_KEY, taskNodes);

            // 多个多实例节点需汇聚后提交
            if(FlowableUtils.hasMultiInstanceNode(bpmnModel, taskNodes)) {
                variables.put(ProcessConstants.BPM_TASK_ASSIGNEE_CACHE, nextAssignee);
            }

            // 设置后续节点处理人
            if(Objects.nonNull(currentFlowNode.getBehavior()) && currentFlowNode.getBehavior() instanceof MultiInstanceActivityBehavior) {
                Boolean isCompletion = processELService.completionConditionSatisfied(myTask.getExecutionId(), (MultiInstanceActivityBehavior) currentFlowNode.getBehavior());
                if(isCompletion) {
                    runtimeService.setVariable(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_ASSIGNEE, nextAssignee);

                    // 设置当前节点接收人
                    runtimeService.setVariableLocal(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_TO_ASSIGNEES + ElAdminConstant.SEPARATOR_UNDERLINE + myTask.getId(), nextAssignee);
                }else {
                    // 多实例任务完成后需要重新调用集合表达式校验，所以需要重新覆盖处理人
                    Object currentAssignee = variables.get(ProcessConstants.BPM_TASK_ASSIGNEE_CACHE);
                    if(Objects.isNull(currentAssignee) || currentAssignee.toString().indexOf(myTask.getTaskDefinitionKey()) == -1) {
                        currentAssignee = runtimeService.getVariable(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_ASSIGNEE);
                    }
                    variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, currentAssignee);
                }
            }else {
                variables.put(ProcessConstants.BPM_TASK_ASSIGNEE, nextAssignee);

                // 设置当前节点接收人
                runtimeService.setVariableLocal(myTask.getProcessInstanceId(), ProcessConstants.BPM_TASK_TO_ASSIGNEES + ElAdminConstant.SEPARATOR_UNDERLINE + myTask.getId(), nextAssignee);
            }

            taskService.complete(myTask.getId(), variables);
        }
    }

    @Override
    @Transactional
    public void batchCompleteTask(List<String> procInstanceIds, String remark) throws Exception {
        this.batchCompleteTask(procInstanceIds, remark, null);
    }

}
