package com.wangda.oa.modules.workflow.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description
 */
@Slf4j
public class TaskDeleteListener implements TaskListener {
    @Override
    public void notify(DelegateTask delegateTask) {
//        delegateTask.setAssignee("老板");
        log.info("TaskDeleteListener", delegateTask);
    }
}
