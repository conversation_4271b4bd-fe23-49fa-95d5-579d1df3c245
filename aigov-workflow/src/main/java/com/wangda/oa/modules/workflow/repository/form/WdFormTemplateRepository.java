package com.wangda.oa.modules.workflow.repository.form;

import com.wangda.oa.modules.workflow.domain.form.WdFormTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/22 15:10
 */
public interface WdFormTemplateRepository extends JpaRepository<WdFormTemplate,Long>, JpaSpecificationExecutor<WdFormTemplate> {

    List<WdFormTemplate> findByClassName(String className);

    List<WdFormTemplate> findByCreatorId(Long creatorId);

    List<WdFormTemplate> findByClassNameLike(String className);

}
