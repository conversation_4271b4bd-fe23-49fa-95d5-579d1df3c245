package com.wangda.oa.modules.workflow.listener;

import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.workflow.constant.ProcessConstants;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowCustomExtensionService;
import com.wangda.oa.utils.ElAdminConstant;
import com.wangda.oa.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.Expression;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/29
 * @description  需流程定义配置任务执行器，现在发起流程时需要抄送当前流程管理员，于是需要写当前结点完成时的监听器    by----caiyy time:2021/10/28
 */
@Slf4j
@Component
public class TaskCompleteListener implements TaskListener {

    private static TaskCompleteListener taskCompleteListener;

    @Autowired
    private UserService userService;

    @Autowired
    private WorkflowCustomExtensionService workflowCustomExtensionService;

    @Autowired
    private RepositoryService repositoryService;
    /**
     * 抄送的用户的表达式 如${username}
     */
    private Expression userList;

    // 解决监听器中Bean获取不到问题
    @PostConstruct
    public void init() {
        taskCompleteListener = this;
        taskCompleteListener.userService = this.userService;
        taskCompleteListener.workflowCustomExtensionService = this.workflowCustomExtensionService;
        taskCompleteListener.repositoryService = this.repositoryService;
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        String usernames = (String) this.userList.getValue(delegateTask);
        if (StringUtils.isEmpty(usernames)){
            return;
        }

        List<String> userList = new ArrayList<>();

        //这里可以获取当前流程
        ProcessDefinition processDefinition = taskCompleteListener.repositoryService.createProcessDefinitionQuery().processDefinitionId(delegateTask.getProcessDefinitionId()).singleResult();

        List<String> usernameList = Arrays.asList(usernames.split(ElAdminConstant.SEPARATOR_COMMA));
        // 处理自定义表达式
        for (String username: usernameList) {
            if(username.equals(ProcessConstants.BPM_EXPRESSION_POSITIONBPMADMIN)) {
                //寻找当前流程管理员
                List<User> users = taskCompleteListener.userService.findByRoleAuthorityKey(processDefinition.getKey());
                if(CollectionUtils.isEmpty(users)){
                    return;
                }
                for (User user:users) {
                    userList.add(user.getUsername());
                }
            }else {
                userList.add(username);
            }
        }

        // 设置抄送人
        delegateTask.setVariable(ProcessConstants.BPM_BPM_COPYTO_PREFIX + delegateTask.getId(), usernames);

        // 发送通知
        taskCompleteListener.workflowCustomExtensionService.sendCopyToMessage(delegateTask.getProcessInstanceId(), userList);
        log.info("TaskCompleteListener", delegateTask);
    }

}
