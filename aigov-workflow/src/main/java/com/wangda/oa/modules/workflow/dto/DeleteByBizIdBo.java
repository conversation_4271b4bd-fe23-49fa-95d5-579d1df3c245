package com.wangda.oa.modules.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:根据instanceId删除待办
 * @Author:cyy
 * @Date: 2021/09/27 下午 16.40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteByBizIdBo {
    @ApiModelProperty(value = "业务id")
    private String bizId;
}
