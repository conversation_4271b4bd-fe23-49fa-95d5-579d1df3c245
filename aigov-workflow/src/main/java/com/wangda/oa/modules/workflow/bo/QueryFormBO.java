package com.wangda.oa.modules.workflow.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询表单
 * <AUTHOR>
 * @date 2021/11/11 9:05
 */
@Data
public class QueryFormBO {

    @ApiModelProperty("表单模板ID")
    @NotNull
    private Long templateId;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("表单流程实例ID")
    private String processInstanceId;
}
