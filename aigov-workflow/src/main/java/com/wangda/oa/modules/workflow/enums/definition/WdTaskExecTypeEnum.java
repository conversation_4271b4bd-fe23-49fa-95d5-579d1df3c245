package com.wangda.oa.modules.workflow.enums.definition;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/2
 * @description 流程处理方式
 */
@Getter
@AllArgsConstructor
public enum WdTaskExecTypeEnum {

    TASKEXECTYPE_SINGLE("single", "单用户实例任务"),
    TASKEXECTYPE_MULTI_PARALLEL_ANY("multi-parallel-any", "多用户实例或签"),
    TASKEXECTYPE_MULTI_PARALLEL_ALL("multi-parallel-all", "多用户实例并行会签"),
    TASKEXECTYPE_MULTI_SEQUENTIAL("multi-sequential", "多用户实例串行会签"),
    TASKEXECTYPE_FORK("fork", "分发实例"),
    TASKEXECTYPE_BLANK("blank","允许不传用户或非用户任务");

    private String value;
    private String name;

    public static WdTaskExecTypeEnum getByValue(String value) {
        for (WdTaskExecTypeEnum wdTaskExecType : WdTaskExecTypeEnum.values()) {
            if (wdTaskExecType.value.equals(value)) {
                return wdTaskExecType;
            }
        }
        return null;
    }
}
