package com.wangda.oa.modules.workflow.factory;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.common.engine.impl.interceptor.Command;
import org.flowable.common.engine.impl.interceptor.CommandContext;
import org.flowable.common.engine.impl.util.io.InputStreamSource;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.flowable.engine.impl.persistence.entity.ResourceEntity;
import org.flowable.engine.impl.persistence.entity.ResourceEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.stream.Collectors;

/**
 * flowable 引擎注入封装
 * <AUTHOR>
 * @date 2021-04-03
 */
@Component
@Slf4j
@NoArgsConstructor
public class DeploymentChangedCmd implements Command<String> {

    private static BpmnXMLConverter bpmnXMLConverter = new BpmnXMLConverter();

    /**
     * 用于替换的流程定义
     */
    private String processDefinitionId;

    /**
     * 用于替换的流程定义
     */
    private InputStream in;

    public DeploymentChangedCmd(String processDefinitionId, InputStream in) {
        this.processDefinitionId = processDefinitionId;
        this.in = in;
    }

    @Override
    public String execute(CommandContext commandContext) {

        ProcessDefinitionEntity processDefinitionEntity = CommandContextUtil.getProcessDefinitionEntityManager(commandContext).findById(processDefinitionId);

        BpmnModel newBpmnModel = bpmnXMLConverter.convertToBpmnModel(new InputStreamSource(in), true, true);

        // 替换流程定义
        byte[] newBpmnBytes = bpmnXMLConverter.convertToXML(newBpmnModel);

        ResourceEntityManager resourceEntityManager = CommandContextUtil.getResourceEntityManager(commandContext);

        ResourceEntity resourceEntity = resourceEntityManager
                .findResourcesByDeploymentId(processDefinitionEntity.getDeploymentId())
                .stream()
                .filter(r -> !r.isGenerated())
                .collect(Collectors.toList()).get(0);

        resourceEntity.setBytes(newBpmnBytes);
        resourceEntityManager.update(resourceEntity, true);

        // 清除缓存
        CommandContextUtil.getProcessEngineConfiguration(commandContext).getProcessDefinitionCache().remove(processDefinitionId);
        CommandContextUtil.getProcessEngineConfiguration(commandContext).getProcessDefinitionInfoCache().remove(processDefinitionId);
        log.info("清理缓存：" + processDefinitionId);

        return new String(newBpmnBytes);
    }
}
