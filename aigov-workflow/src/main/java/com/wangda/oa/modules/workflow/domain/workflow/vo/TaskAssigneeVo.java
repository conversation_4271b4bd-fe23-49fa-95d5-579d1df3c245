package com.wangda.oa.modules.workflow.domain.workflow.vo;

import com.wangda.oa.utils.ElAdminConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-05-03
 */
@Data
@NoArgsConstructor
@ApiModel("工作流处理人--请求参数")
public class TaskAssigneeVo implements Serializable {

    private static final long serialVersionUID = 5321677611496718344L;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("任务key")
    private String taskKey;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TaskAssigneeVo that = (TaskAssigneeVo) o;
        return username.equals(that.username) && taskKey.equals(that.taskKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(username, taskKey);
    }

    @Override
    public String toString() {
        return this.username.concat(ElAdminConstant.SEPARATOR_DOLLAR).concat(this.taskKey);
    }

}
