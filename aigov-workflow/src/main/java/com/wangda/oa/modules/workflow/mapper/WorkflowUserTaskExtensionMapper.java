package com.wangda.oa.modules.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wangda.oa.modules.workflow.domain.workflow.WorkflowUserTaskExtension;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface WorkflowUserTaskExtensionMapper extends BaseMapper<WorkflowUserTaskExtension> {

    /**
     * 大于等于传入序号的数据序号都累加1
     * @param sort
     */
    void updateSortBySort(@Param("sort") Integer sort);

    /**
     * 大于等于传入序号的数据序号都累加1
     * @param sort
     */
    void updateButtonSortBySort(@Param("sort") Integer sort);
}
