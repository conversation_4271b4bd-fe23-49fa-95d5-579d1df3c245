<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wangda.oa.modules.workflow.mapper.DynamicTableMapper">
    <update id="createAutoTask" parameterType="map">
        create table ${tableName} (
        "ID" NUMBER NOT NULL ,
        <foreach collection="tableFields" index="key" item="_value">
            <choose>
                <when test=" key == 'number' or key == 'rate' or key == 'switch'">
                    ${_value} NUMBER,
                </when>
                <when test=" key =='fileupload' or key =='imgupload'">
                    ${_value} CLOB,
                </when>
                <when test=" key == 'date' or key == 'time'">
                    ${_value} VARCHAR2(1024 BYTE),
                </when>
                <otherwise>
                    ${_value} VARCHAR2(1024 BYTE),
                </otherwise>
            </choose>
        </foreach>
        processId NUMBER,
        creator_id VARCHAR2(1024 BYTE),
        create_date DATE,
        CONSTRAINT ${tableName} PRIMARY KEY ("ID"))
    </update>
    <select id="checkTableData" parameterType="map" resultType="java.lang.Integer">
    select count(*) from ${tableName}
    </select>
    <delete id="deleteTable">
        drop table ${tableName}
    </delete>
    <insert id="addTableData" parameterType="map">
        insert into ${tableName}
        (
        <foreach collection="tableFields" index="key" item="_value" separator=",">
            ${key}
        </foreach>)
        values (
        <foreach collection="tableFields" index="key" item="_value" separator=",">
            #{_value}
        </foreach>)
    </insert>
    <select id="getMyFormList" resultType="map">
        SELECT
        a.*,
        b.GROUPNAME,
        c.status
        FROM
        ${qry.className} A
        LEFT JOIN PGROUP b ON a.CREATOR_ID = b.id
        LEFT JOIN WD_FORM_INFO c on c.BUSINESS_ID=A.id
        where 1=1
        <if test="qry.creatorId!=null and qry.creatorId!=''">
            and a.CREATOR_ID=#{qry.creatorId}
        </if>
        order by a.CREATE_DATE desc
    </select>
    <select id="getFormInfo" parameterType="map" resultType="map">
        select * from ${qry.className} where id=#{qry.id}
    </select>
    <select id="getFormInfoByPid" parameterType="map" resultType="map">
        select * from ${qry.className} where pid=#{qry.id}
    </select>
    <delete id="deleteFormInfo" parameterType="map">
        delete from ${qry.className} where id=#{qry.id}
    </delete>
    <select id="checkTableExist" parameterType="map" resultType="java.lang.Integer">
        select count(1) from user_tables where table_name = upper(#{tableName})
    </select>
    <select id="checkTableFieldExist" resultType="java.lang.Integer">
        select COUNT(1) from user_tab_cols where table_name= #{tableName} and column_name = #{filed}
    </select>
    <insert id="insertTableField" parameterType="map">
        alter table ${tableName} add
        <choose>
            <when test=" key == 'number' or key == 'rate'">
                ${value} NUMBER
            </when>
            <when test=" key == 'radio' or key == 'switch' ">
                ${value} NUMBER
            </when>
            <when test=" key == 'date' or key == 'time'">
                ${value} DATE
            </when>
            <otherwise>
                ${value} VARCHAR2(1024 BYTE)
            </otherwise>
        </choose>
    </insert>
    <select id="queryAll" resultType="com.wangda.oa.modules.workflow.domain.form.WdFormTemplate">
        select *
        from WD_FORM_TEMPLATE
        where 1=1
        <if test="qry.category!=null and qry.category!=''">
            and CATEGORY=#{qry.category}
        </if>
        <if test="qry.userId!=null and qry.userId!=''">
            and (USER_ID=#{qry.userId}
        </if>
        <if test="qry.type!=null and qry.type!=''">
            or SHARED=1
        </if>
        <if test="qry.userId!=null and qry.userId!=''">
            )
        </if>
    </select>
</mapper>
