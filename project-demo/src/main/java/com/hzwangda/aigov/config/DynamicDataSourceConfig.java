package com.hzwangda.aigov.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/3
 * @description 多数据源配置
 */
@Configuration
public class DynamicDataSourceConfig {

    /**
     * 创建 DataSource Bean
     **/
    @Primary
    @Bean(value = "primaryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.mysql")
    public DataSource primaryDataSource(){
        DataSource dataSource = DruidDataSourceBuilder.create().build();
        return dataSource;
    }

}
