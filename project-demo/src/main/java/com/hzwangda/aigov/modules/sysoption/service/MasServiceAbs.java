package com.hzwangda.aigov.modules.sysoption.service;

import com.hzwangda.aigov.modules.sysoption.service.impl.DebugMasServiceImpl;

/**
 * <AUTHOR>
 */
public abstract class MasServiceAbs implements MasService {

	public static MasServiceAbs getInstance(String version) {
		return new DebugMasServiceImpl();
	}

	/**
	 * 发送短信
	 * @param mobiles 手机号数组
	 * @param content 发送内容
	 * @param smID  smID
	 * @param srcID srcID
	 * @return
	 */
	@Override
	public abstract boolean sendSM(String[] mobiles, String content, long smID, long srcID) throws Exception;

}
