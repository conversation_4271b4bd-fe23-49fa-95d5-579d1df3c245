package com.hzwangda.aigov.modules.workflow.rest;

import com.hzwangda.aigov.modules.workflow.enums.workflow.ProcessAuthorityEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 用于操作管理员权限
 * @Author:caiyy
 * @Date: 2021/09/30
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "管理员权限")
@RequestMapping("/api/bpmrole")
public class BpmRoleController {

    @ApiOperation("查询权限")
    @GetMapping(value = "/authorityKey")
    public ResponseEntity<Object> getAuthorityKeys() {
        return new ResponseEntity<>(ProcessAuthorityEnum.values(), HttpStatus.OK);
    }
}
