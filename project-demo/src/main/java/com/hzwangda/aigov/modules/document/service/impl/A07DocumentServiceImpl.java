package com.hzwangda.aigov.modules.document.service.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.document.domain.A07DocumentGwView;
import com.hzwangda.aigov.modules.document.domain.A07DocumentInfoWwfb;
import com.hzwangda.aigov.modules.document.domain.ReferenceNumber;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwDto;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.A07DocumentRetrieveQueryCriteria;
import com.hzwangda.aigov.modules.document.repository.A07DocumentGwViewRepository;
import com.hzwangda.aigov.modules.document.repository.A07DocumentInfoWwfbRepository;
import com.hzwangda.aigov.modules.document.service.A07DocumentService;
import com.hzwangda.aigov.modules.workflow.dto.BacklogBusinessSearchBO;
import com.hzwangda.aigov.modules.workflow.dto.BacklogListBO;
import com.hzwangda.aigov.modules.workflow.dto.BacklogListPageDto;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.hzwangda.aigov.util.OaUtil;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.*;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公文服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class A07DocumentServiceImpl implements A07DocumentService {

    private final A07DocumentInfoWwfbRepository a07DocumentInfoWwfbRepository;

    private final A07DocumentGwViewRepository a07DocumentGwViewRepository;

    private final UserRepository userRepository;

    private final RestUrlComponent restUrlComponent;

    @Override
    public List<A07DocumentInfoWwfb> getWwfbList() {
        return a07DocumentInfoWwfbRepository.findAll(Sort.by("sort").ascending());
    }

    @Override
    public Map<String, Object> queryAllGwList(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        criteria.setGwglType(3);
        Page<A07DocumentGwView> page = a07DocumentGwViewRepository.findAll((Specification)retrieveFilter(criteria), pageable);
        return PageUtil.toPage(page);
    }

    @Override
    public Map<String, Object> getGwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        //封装待办查询条件
        BacklogListBO backlogListBO = new BacklogListBO();
        backlogListBO.setUserId(SecurityUtils.getCurrentUsername());
        backlogListBO.setAppId(restUrlComponent.getBacklogAppId());
        if(StringUtils.isNotBlank(criteria.getProcessDefinitionKey())){
            //实例定义不为空
            backlogListBO.setModuleCode(criteria.getProcessDefinitionKey());
        }else{
            //实例定义为空则查询公文管理对应定义
            backlogListBO.setModuleCodes(DocumentConstant.PROCESS_DEFINITION_KEY_LIST);
        }
        List<BacklogBusinessSearchBO> businessSearchBOList=new ArrayList<>();
        if(StringUtils.isNotBlank(criteria.getBt())){
            backlogListBO.setBt(criteria.getBt());
        }
        if(criteria.getTimeRange()!=null && criteria.getTimeRange().size()>1){
            backlogListBO.setCreateStartTime(criteria.getTimeRange().get(0));
            backlogListBO.setCreateEndTime(criteria.getTimeRange().get(1));
        }
        backlogListBO.setBusinessSearchBOList(businessSearchBOList);
        backlogListBO.setStatus(criteria.getStatus());
        if(criteria.getStatus()==null){
            //若不传默认查询所有，则为4
            backlogListBO.setStatus(4);
        }
        backlogListBO.setPage(pageable.getPageNumber());
        backlogListBO.setSize(pageable.getPageSize());

        //查询待办服务
        BacklogListPageDto backlogListPageDto= OaUtil.getBacklogList(backlogListBO);
        List<BacklogListDto> backlogListDtoList = backlogListPageDto.getContent();
        if (CollectionUtils.isEmpty(backlogListDtoList)) {
            return PageUtil.toPage(Page.empty());
        }

        //获取流程实例id
        Map<String, Date> bpmInstanceIdMap = new LinkedHashMap<>();
        for (BacklogListDto backlogListDto: backlogListDtoList) {
            bpmInstanceIdMap.put(backlogListDto.getBizId(), backlogListDto.getCreateDate());
        }
        criteria.setBpmInstanceId(Lists.newArrayList(bpmInstanceIdMap.keySet()));
        //查询业务视图表信息
        List<A07DocumentGwView> viewList = a07DocumentGwViewRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder));

        String transactorNames=backlogListDtoList.stream().map(BacklogListDto::getCurrentHandler).collect(Collectors.joining(","));
        //用户名集合放入
        List<String> username=viewList.stream().map(A07DocumentGwView::getCjr).collect(Collectors.toList());
        //办理人放入用户名集合
        username.addAll(Arrays.asList(transactorNames.split(",")));

        //查询用户信息
        List<User> userList=userRepository.findByUsernameIn(username);
        Map<String, User> finalUserMap = userList.stream().collect(Collectors.toMap(User::getUsername, a -> a, (k1, k2) -> k1));

        List<A07DocumentGwDto> returnList = backlogListDtoList.stream().map(p->{
            A07DocumentGwDto documentGwDto=new A07DocumentGwDto();
            for (A07DocumentGwView a07DocumentGwView:viewList) {
                if(a07DocumentGwView.getBpmInstanceId().equals(p.getBizId())){
                    BeanUtils.copyProperties(a07DocumentGwView, documentGwDto);
                    if(StringUtils.isNotBlank(p.getCurrentHandler())){
                        //当前办理人不为空根据username设置中文
                        String[] handlerArr=p.getCurrentHandler().split(",");
                        String handler = "";
                        for (int i = 0; i < handlerArr.length; i++) {
                            if(finalUserMap.get(handlerArr[i])==null){
                                continue;
                            }
                            handler+=finalUserMap.get(handlerArr[i]).getNickName()+",";
                        }
                        if(StringUtils.isNotBlank(handler)){
                            handler=handler.substring(0,handler.length()-1);
                        }
                        documentGwDto.setBlr(handler);
                    }
                    User cjrUser=finalUserMap.get(a07DocumentGwView.getCjr());
                    documentGwDto.setCjr(cjrUser==null?null:cjrUser.getNickName());
                    WorkflowType workflowType=WorkflowType.getByValue(a07DocumentGwView.getModuleType());
                    documentGwDto.setModuleType(workflowType==null?null:workflowType.getName());
                    documentGwDto.setBpmStatus(p.getHandleStatus());
                    if(Objects.nonNull(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()))) {
                        documentGwDto.setCreateTime(new Timestamp(bpmInstanceIdMap.get(documentGwDto.getBpmInstanceId()).getTime()));
                    }
                    return documentGwDto;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String,Object> returnMap = new LinkedHashMap<>(2);
        returnMap.put("content", returnList);
        returnMap.put("totalElements", backlogListPageDto.getTotalElements());
        return returnMap;
    }

    /**
     * 检索条件
     * @param params
     * @return
     */
    private Specification<T> retrieveFilter(A07DocumentRetrieveQueryCriteria params) {
        return (root, query, cb) -> {
            //封装and查询条件
            ArrayList<Predicate> andList = new ArrayList<>();
            if(StringUtils.isNotBlank(params.getBt())){
                //标题不为空
                andList.add(cb.like(root.get("bt"), "%" + params.getBt() + "%"));
            }
            if(StringUtils.isNotBlank(params.getBpmStatus())){
                //流程状态不为空
                andList.add(cb.equal(root.get("bpmStatus"),params.getBpmStatus()));
            }
            if(params.getMyParticipate()!=null && params.getMyParticipate()){
                //我参与的
                andList.add(cb.isMember(cb.literal(SecurityUtils.getCurrentUsername()), root.<Collection<String>>get("participateUser")));//存入条件集合里
            }
            if(params.getTimeType()!=null && params.getTimeType()!=DocumentConstant.RETRIEVE_DATE_BX){
                Date date=new Date();
                if(DocumentConstant.RETRIEVE_DATE_24H==params.getTimeType()){
                    //24小时内
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class),DateUtil.offsetDay(date,-1),date));
                }else if(DocumentConstant.RETRIEVE_DATE_3DAY==params.getTimeType()){
                    //3天内
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class),DateUtil.offsetDay(date,-3),date));
                }else if(DocumentConstant.RETRIEVE_DATE_1M==params.getTimeType()){
                    //1个月内
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class),DateUtil.offsetDay(date,-31),date));
                }else if(DocumentConstant.RETRIEVE_DATE_CUSTOM==params.getTimeType()){
                    //自定义时间
                    andList.add(cb.between(root.get("createTime").as(Timestamp.class),DateUtil.parse(params.getStartTime(),"yyyy-MM-dd"),DateUtil.parse(params.getEndTime(),"yyyy-MM-dd")));
                }
            }
            if(StringUtils.isNotBlank(params.getDz()) || params.getNh()!=null
                    || params.getXh()!=null){
                if(StringUtils.isNotBlank(params.getDz())) {
                    setWh(root,cb,params,andList,"dz");
                }
                if(params.getNh()!=null) {
                    setWh(root,cb,params,andList,"nh");
                }
                if(params.getXh()!=null) {
                    setWh(root,cb,params,andList,"xh");
                }
            }
            return cb.and(andList.toArray(new Predicate[andList.size()]));
        };
    }

    /**
     *
     * @param root
     * @param cb
     * @param params
     * @param andList
     * @param name
     */
    private void setWh(Root<T> root,CriteriaBuilder cb,A07DocumentRetrieveQueryCriteria params,ArrayList<Predicate> andList,String name){
        if(params.getGwglType()==null){
            return;
        }
        ArrayList<Predicate> orList = new ArrayList<>();
        if(0==params.getGwglType() || 1==params.getGwglType() || 2==params.getGwglType()){
            //0:发文 1:会签 2:领导批示
            Join<T, ReferenceNumber> gwwh = root.join("gwwh", JoinType.LEFT);
            if("dz".equals(name)){
                //文号-代字不为空
                orList.add(cb.equal(gwwh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("nh".equals(name)){
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("xh".equals(name)){
                //文号-年号不为空
                orList.add(cb.equal(gwwh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        }else if(3==params.getGwglType()){
            //3:收文
            Join<T, ReferenceNumber> swbh = root.join("swbh", JoinType.LEFT);
            Join<T, ReferenceNumber> lwbh = root.join("lwbh", JoinType.LEFT);
            if("dz".equals(name)){
                //文号-代字不为空
                orList.add(cb.equal(swbh.get("dz"), params.getDz()));
                orList.add(cb.equal(lwbh.get("dz"), params.getDz()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("nh".equals(name)){
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("nh"), params.getNh()));
                orList.add(cb.equal(lwbh.get("nh"), params.getNh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }else if("xh".equals(name)){
                //文号-年号不为空
                orList.add(cb.equal(swbh.get("xh"), params.getXh()));
                orList.add(cb.equal(lwbh.get("xh"), params.getXh()));
                andList.add(cb.or(orList.toArray(new Predicate[orList.size()])));
            }
        }
    }
}
