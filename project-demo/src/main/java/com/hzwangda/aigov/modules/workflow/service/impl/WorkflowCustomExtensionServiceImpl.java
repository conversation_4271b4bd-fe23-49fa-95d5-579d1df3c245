package com.hzwangda.aigov.modules.workflow.service.impl;

import com.hzwangda.aigov.modules.document.constant.DocumentConstant;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.hzwangda.aigov.util.OaUtil;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.modules.workflow.constant.BpmAuthorityConstant;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.BacklogReadDto;
import com.wangda.oa.modules.workflow.dto.DeleteByBizIdBo;
import com.wangda.oa.modules.workflow.enums.workflow.CategoryTypeEnum;
import com.wangda.oa.modules.workflow.service.workflow.WorkflowCustomExtensionService;
import com.wangda.oa.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/29
 * @description 自定义业务实现流程扩展接口
 */
@Service
public class WorkflowCustomExtensionServiceImpl implements WorkflowCustomExtensionService {

    @Override
    public String getWorkTypeIconByProcDefKey(String procDefKey) {
        if(StringUtils.isBlank(procDefKey)) {
            return null;
        }
        WorkflowType workflowType = WorkflowType.getByValue(procDefKey);
        if(Objects.isNull(workflowType)) {
            return null;
        }
        return workflowType.getIcon();
    }

    @Override
    public String getWorkTypeNameByValue(String value) {
        if(StringUtils.isBlank(value)) {
            return null;
        }
        WorkflowType workflowType = WorkflowType.getByValue(value);
        if(Objects.isNull(workflowType)) {
            return null;
        }
        return workflowType.getName();
    }

    @Override
    public Boolean deleteBackLog(String bizId) {
        if(StringUtils.isBlank(bizId)) {
            return false;
        }
        DeleteByBizIdBo deleteByBizIdBo = DeleteByBizIdBo.builder().bizId(bizId).build();
        ResponseInfo result = OaUtil.deleteBackLog(deleteByBizIdBo);
        return ResultCodeEnum.SUCCESS.getCode() == result.getCode();
    }

    @Override
    public Boolean pushToBacklog(BacklogDto backlogDto) {
        if(Objects.isNull(backlogDto)) {
            return false;
        }
        return OaUtil.pushToBacklog(backlogDto);
    }

    @Override
    public Boolean readBacklog(String bizId, String username) {
        if(StringUtils.isEmpty(bizId)) {
           return false;
        }
        BacklogReadDto backlogReadDto = new BacklogReadDto();
        backlogReadDto.setBizId(bizId);
        backlogReadDto.setUserId(username);
        return OaUtil.readBacklog(backlogReadDto);
    }

    @Override
    public List<String> getProcessDefinitionKeyList(String type) {
        if(StringUtils.isBlank(type)) {
            return null;
        }
        List<String> result = new ArrayList<>();
        CategoryTypeEnum categoryTypeEnum = CategoryTypeEnum.getByValue(type);
        switch (categoryTypeEnum) {
            case CATEGORY_GWGL:
                result = DocumentConstant.PROCESS_DEFINITION_KEY_LIST;
                break;
            case CATEGORY_XZFW:
                result = DocumentConstant.PROCESS_DEFINITION_XZFW_KEY_LIST;
                break;
        }
        return result;
    }

    @Override
    public List<String> getBpmAdminKeys() {
        List<String> elPermissions = SecurityUtils.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        List<String> workflowTypeValues = new ArrayList<>();
        List<WorkflowType> workflowTypeList = Arrays.asList(WorkflowType.values());
        for (WorkflowType workflowType: workflowTypeList) {
            workflowTypeValues.add(workflowType.getValue());
        }
        workflowTypeValues.retainAll(elPermissions);
        return workflowTypeValues;
    }

    @Override
    public Boolean isBpmAdmin() {
        List<String> elPermissions = SecurityUtils.getCurrentUser().getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList());
        if(Arrays.stream(new String[]{ BpmAuthorityConstant.AUTHORITY_BPM_ADMIN, BpmAuthorityConstant.AUTHORITY_ADMIN }).anyMatch(elPermissions::contains)) {
            return true;
        }else {
            List<String> workflowTypeValues = new ArrayList<>();
            List<WorkflowType> workflowTypeList = Arrays.asList(WorkflowType.values());
            for (WorkflowType workflowType: workflowTypeList) {
                workflowTypeValues.add(workflowType.getValue());
            }
            if(workflowTypeValues.stream().anyMatch(elPermissions::contains)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String updateBpmReadTask(Long bpmInstanceId, Long readId, int flag) {
        return null;
    }

    @Override
    public void sendCopyToMessage(String procInstanceId, List<String> userList) {

    }
}
