package com.hzwangda.aigov.modules.document.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DocumentConstant {

    /**
     * 查询状态-待办
     */
    public static final int QUERY_STATUS_DB = 0;

    /**
     * 查询状态-待阅
     */
    public static final int QUERY_STATUS_DY = 3;

    /**
     * 检索-日期-不限
     */
    public static final int RETRIEVE_DATE_BX = 0;

    /**
     * 检索-日期-24小时内
     */
    public static final int RETRIEVE_DATE_24H = 1;

    /**
     * 检索-日期-近3天
     */
    public static final int RETRIEVE_DATE_3DAY = 2;

    /**
     * 检索-日期-近1个月
     */
    public static final int RETRIEVE_DATE_1M = 3;

    /**
     * 检索-日期-自定义
     */
    public static final int RETRIEVE_DATE_CUSTOM = 4;

    /**
     * 公文管理
     */
    public static final List<String> PROCESS_DEFINITION_KEY_LIST=new ArrayList(){{
        add("documentAddressee");
        add("documentPost");
    }};

    /**
     * 行政服务
     */
    public static final List<String> PROCESS_DEFINITION_XZFW_KEY_LIST=new ArrayList(){{
        add("sealApproval"); //用印审批
    }};

}
