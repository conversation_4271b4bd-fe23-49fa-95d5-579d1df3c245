package com.hzwangda.aigov.modules.document.dto;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 公文
 * <AUTHOR>
 * @date 2021/7/28下午7:29
 */
@Data
public class A07DocumentGwDto extends BaseDomain {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "流程实例id")
    private String bpmInstanceId;

    @ApiModelProperty(name = "状态")
    private String bpmStatus;

    @ApiModelProperty(name = "创建人")
    private String cjr;

    @ApiModelProperty(value = "类型")
    private String moduleType;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @ApiModelProperty(value = "办理人")
    private String blr;

    @ApiModelProperty(value = "任务id")
    private String taskId;
}
