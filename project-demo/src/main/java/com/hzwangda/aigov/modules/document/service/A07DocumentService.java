package com.hzwangda.aigov.modules.document.service;

import com.hzwangda.aigov.modules.document.domain.A07DocumentInfoWwfb;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.A07DocumentRetrieveQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface A07DocumentService {

    /**
     * 外网发布数据集合
     * @return
     */
    List<A07DocumentInfoWwfb> getWwfbList();

    /**
     * 收文检索列表
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String, Object> queryAllGwList(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable);

    /**
     * 公文管理列表
     * @param criteria
     * @param pageable
     * @return
     */
    Map<String, Object> getGwList(A07DocumentGwQueryCriteria criteria, Pageable pageable);
}
