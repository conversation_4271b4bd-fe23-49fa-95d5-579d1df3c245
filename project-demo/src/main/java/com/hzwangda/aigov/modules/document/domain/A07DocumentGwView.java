package com.hzwangda.aigov.modules.document.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Immutable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * 公文视图
 */
@Immutable
@Table(name = "gw_view")
@Entity
@Data
public class A07DocumentGwView {
    @Id
    private Long id;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "bpm_status")
    private String bpmStatus;

    @Column(name = "cjr")
    @ApiModelProperty(value = "创建人")
    private String cjr;

    @ApiModelProperty(value = "类型")
    private String moduleType;

    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @Column(name = "bpm_instance_id")
    private String bpmInstanceId;
}
