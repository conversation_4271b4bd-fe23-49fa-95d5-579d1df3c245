package com.hzwangda.aigov.modules.workflow.domain;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "sys_storage_biz")
@Data
public class StorageBiz implements Serializable {

    @Id
    @Column(name = "storage_id")
    private String storageId;

    @Column(name = "biz_id")
    private String bizId;

    private String bizType;

    private Integer sorted;

}
