package com.hzwangda.aigov.modules.document.controller;

import com.hzwangda.aigov.modules.document.domain.A07DocumentInfoWwfb;
import com.hzwangda.aigov.modules.document.dto.A07DocumentGwQueryCriteria;
import com.hzwangda.aigov.modules.document.dto.A07DocumentRetrieveQueryCriteria;
import com.hzwangda.aigov.modules.document.service.A07DocumentService;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "公文管理")
@RequestMapping("/api/aigov/a07")
@CrossOrigin
public class A07DocumentController {

    private final A07DocumentService a07DocumentService;

    @ApiOperation("公文管理列表")
    @GetMapping(value = "/getGwList")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> getGwList(A07DocumentGwQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.getGwList(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("外网发布数据集合")
    @AnonymousPostMapping(value = "/getWwfbList")
    public ResponseEntity<List<A07DocumentInfoWwfb>> getWwfbList() {
        return new ResponseEntity<>(a07DocumentService.getWwfbList(), HttpStatus.OK);
    }

    @ApiOperation("收文检索")
    @GetMapping(value = "/queryAllGwList")
    @PreAuthorize("@el.check('a07:list')")
    public ResponseEntity<Object> queryAllGwList(A07DocumentRetrieveQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(a07DocumentService.queryAllGwList(criteria, pageable), HttpStatus.OK);
    }

}
