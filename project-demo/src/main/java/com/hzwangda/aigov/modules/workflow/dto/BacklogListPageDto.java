package com.hzwangda.aigov.modules.workflow.dto;

import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/15 上午9:26
 */
@Data
@ApiModel(description="待办列表返回实体")
public class BacklogListPageDto {
    @ApiModelProperty(value = "待办集合")
    private List<BacklogListDto> content;
    @ApiModelProperty(value = "总数")
    private Long totalElements;
}
