package com.hzwangda.aigov.modules.workflow.dto;

import com.wangda.oa.service.dto.LocalStorageDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class StorageBizDto {

    @ApiModelProperty(value = "附件")
    private LocalStorageDto storage;

    @ApiModelProperty(value = "业务id")
    private String bizId;

    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @ApiModelProperty(value = "排序")
    private Integer sorted;

    @ApiModelProperty(value = "转换后的附件集合")
    List<LocalStorageDto> conversionList;
}
