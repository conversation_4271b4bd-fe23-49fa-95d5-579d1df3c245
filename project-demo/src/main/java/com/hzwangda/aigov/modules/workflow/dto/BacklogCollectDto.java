package com.hzwangda.aigov.modules.workflow.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;

/**
 * @author: leec
 * @create: 2021-08-30
 * @description: 统一待办收藏请求对象
 **/
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BacklogCollectDto {

    @ApiModelProperty(value = "外部业务id(同个任务相同)")
    @NotBlank
    private String bizId;

    @ApiModelProperty(value = "用户名")
    @NotBlank
    private String userId;

    @ApiModelProperty(value = "用户名")
    @NotBlank
    private String username;

    @ApiModelProperty(value = "标签")
    private String tag;

    @ApiModelProperty(value = "收藏类型(0:收藏,1:取消收藏)")
    @NotBlank
    private Integer collectType;
}
