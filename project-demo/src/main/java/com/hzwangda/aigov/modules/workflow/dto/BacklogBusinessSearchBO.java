package com.hzwangda.aigov.modules.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/14 上午10:00
 */
@Data
@ApiModel(description="模块搜索实体")
public class BacklogBusinessSearchBO {
    @ApiModelProperty(value = "类型(等于 equal 包含 contains)")
    private String type;
    @ApiModelProperty(value = "字段key")
    private String key;
    @ApiModelProperty(value = "字段value")
    private String value;

    public BacklogBusinessSearchBO(String type, String key,String value) {
        this.type = type;
        this.key=key;
        this.value = value;
    }
}
