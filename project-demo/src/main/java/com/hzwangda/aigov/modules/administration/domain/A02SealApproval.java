package com.hzwangda.aigov.modules.administration.domain;

import com.hzwangda.aigov.modules.workflow.domain.BaseBpmDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.List;

/**
 * 用印审批
 */
@Entity
@Getter
@Setter
@Table(name = "a02_seal_approval")
@NoArgsConstructor
public class A02SealApproval extends BaseBpmDomain {

    @ApiModelProperty(value = "用印内容")
    private String content;

    @ApiModelProperty(value = "发送范围")
    private String scope;

    @ApiModelProperty(value = "文件份数")
    private Integer copies;

    @ApiModelProperty(value = "加盖何种印章")
    private String sealType;

    @ApiModelProperty(value = "相关文件")
    @ElementCollection
    @CollectionTable(name="sys_storage_biz", joinColumns = {
            @JoinColumn(name ="biz_id"),
    }, foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @Column(name="storage_id")
    private List<String> attachments;

    @ApiModelProperty(value = "备注")
    private String memo;

}
