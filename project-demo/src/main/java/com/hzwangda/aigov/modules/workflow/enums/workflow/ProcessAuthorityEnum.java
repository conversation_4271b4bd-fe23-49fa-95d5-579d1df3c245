package com.hzwangda.aigov.modules.workflow.enums.workflow;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@Getter
@AllArgsConstructor
@ToString
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@JSONType(serializeEnumAsJavaBean = true)
public enum ProcessAuthorityEnum {
    /**
     * 细分各个流程  用于区分管理员权限 name对应页面上显示的流程名称
     */
    DOCUMENT_ADDRESSEE("documentAddressee","收文办件"),
    DOCUMENT_POST("documentPost","发文办件"),
    SEAL_APPROVAL("sealApproval","用印申请");


    private String value;
    private String name;

    public static ProcessAuthorityEnum getByValue(String value) {
        for (ProcessAuthorityEnum processAuthorityEnum : ProcessAuthorityEnum.values()) {
            if (processAuthorityEnum.value.equals(value)) {
                return processAuthorityEnum;
            }
        }
        return null;
    }
}
