package com.hzwangda.aigov.modules.workflow.dto;

import com.wangda.boot.platform.base.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/14 上午10:00
 */
@Data
@ApiModel(description="模块列表实体")
public class BacklogListBO extends Pagination {
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "系统appId")
    private String appId;
    @ApiModelProperty(value = "模块id")
    private Long moduleId;
    @ApiModelProperty(value = "模块code")
    private String moduleCode;
    @ApiModelProperty(value = "收藏(0:未收藏,1:已收藏,不传默认查询所有)")
    private Integer collect;
    @ApiModelProperty(value = "搁置-非已收藏且状态为待办生效(0:未搁置,1:已搁置,不传默认查询所有)")
    private Integer shelve;
    @ApiModelProperty(value = "急件-非已收藏且状态为待办生效(0:未急件,1:急件,不传默认查询所有)")
    private Integer urgent;
    @ApiModelProperty(value = "状态(0:我的待办,1:我的已办,2:我的申请,3:抄送给我,4:全部)")
    @NotNull(message = "状态不能为空")
    private Integer status;
    @ApiModelProperty(value = "办理时限开始时间-非已收藏且状态为待办生效（办理开始时间和结束时间都有值才查询）")
    private String abortStartTime;
    @ApiModelProperty(value = "办理时限结束时间-非已收藏且状态为待办生效（办理开始时间和结束时间都有值才查询）")
    private String abortEndTime;
    @ApiModelProperty(value = "是否阅读-不是已收藏且状态为待办生效(0:未阅读,1:已阅读,不传默认查询所有)")
    private Integer readType;
    @ApiModelProperty(value = "模块类别判断(空根据模块查询,不为空根据不包含的查询)")
    private Integer categoryType;
    @ApiModelProperty(value = "categoryType为空查询对应模块集合，不为空查询非模块集合")
    private List<String> moduleCodes;
    @ApiModelProperty(value = "标题模糊查询")
    private String bt;
    @ApiModelProperty(value = "待办创建开始时间  yyyy-MM-dd")
    private String createStartTime;
    @ApiModelProperty(value = "待办创建结束时间  yyyy-MM-dd")
    private String createEndTime;
    @ApiModelProperty(value = "业务字段查询集合")
    private List<BacklogBusinessSearchBO> businessSearchBOList;
}
