package com.hzwangda.aigov.modules.document.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.BaseBpmDomain;
import com.hzwangda.aigov.modules.workflow.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 公文管理-发文
 * <AUTHOR>
 * @date 2021/6/15 上午10:18
 */
@Data
@Entity
@Table(name="a07_document_fw")
public class A07DocumentFw extends BaseBpmDomain {

    @ApiModelProperty(value = "文号")
    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name="wh_id", referencedColumnName = "id")
    private ReferenceNumber gwwh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急(普通,急)")
    private String hj;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Lob
    @Column(name = "yj_qf")
    @ApiModelProperty(value = "签发-json格式")
    private String yjQf;

    @Lob
    @Column(name = "yj_hq")
    @ApiModelProperty(value = "会签-json格式")
    private String yjHq;

    @Column(name = "yj_fgzrsh",length = 2000)
    @ApiModelProperty(value = "分管主任审核意见-json格式")
    private String yjFgzrsh;

    @Column(name = "yj_msksh",length = 2000)
    @ApiModelProperty(value = "秘书科审核意见-json格式")
    private String yjMsksh;

    @Column(name = "yj_cssh",length = 2000)
    @ApiModelProperty(value = "处室审核意见-json格式")
    private String yjCssh;

    @ApiModelProperty(value = "正文")
    @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name="zw", referencedColumnName = "storage_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private StorageBiz zw;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class,cascade = CascadeType.ALL,orphanRemoval = true)
    @JoinColumn(name="biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentFw.fj'")
    private List<StorageBiz> fj;

    @ApiModelProperty(value = "主送单位")
    @OneToOne(cascade = CascadeType.ALL,orphanRemoval = true)
    @JoinColumn(name="zsdw", referencedColumnName = "id")
    private SysDeptUserMain zsdw;

    @ApiModelProperty(value = "抄送单位")
    @OneToOne(cascade = CascadeType.ALL,orphanRemoval = true)
    @JoinColumn(name="csdw", referencedColumnName = "id")
    private SysDeptUserMain csdw;

    @Column(name = "zbbm")
    @ApiModelProperty(value = "主办部门")
    private String zbbm;

    @Column(name = "ngr")
    @ApiModelProperty(value = "拟稿人")
    private String ngr;

    @Column(name = "ngdw")
    @ApiModelProperty(value = "拟稿单位")
    private String ngdw;

    @Column(name = "yffs")
    @ApiModelProperty(value = "印发份数")
    private String yffs;

    @Column(name = "yfrq")
    @ApiModelProperty(value = "印发日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date yfrq;

    @Column(name = "nwfb")
    @ApiModelProperty(value = "内网发布(是,否)")
    private String nwfb;

    @Column(name = "jdr")
    @ApiModelProperty(value = "校对人")
    private String jdr;

    @Column(name = "wwfblm_code")
    @ApiModelProperty(value = "外网发布栏目code")
    private String wwfblmCode;

    @Column(name = "gklx")
    @ApiModelProperty(value = "公开类型(主动公开,依申请公开,不予公开)")
    private String gklx;

    @Column(name = "ly",length = 2000)
    @ApiModelProperty(value = "理由")
    private String ly;

    @Column(name = "bz",length = 2000)
    @ApiModelProperty(value = "备注")
    private String bz;

    @Column(name = "hqdw")
    @ApiModelProperty(value = "会签单位")
    private String hqdw;

    @Column(name = "zcwj")
    @ApiModelProperty(value = "政策文件是否允许公开")
    private String zcwj;

    @ApiModelProperty(value = "会签附件")
    @OneToMany(targetEntity = StorageBiz.class,cascade = CascadeType.ALL,orphanRemoval = true)
    @JoinColumn(name="biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentFw.hqfj'")
    private List<StorageBiz> hqfj;

    public void setZw(StorageBiz zw) {
        if(Objects.nonNull(zw)) {
            this.zw = zw;
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())){
            if(this.id!=null){
                this.zw.setBizId(this.id.toString());
            }
        }else {
            this.zw = null;
        }
    }

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if (this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    public void setHqFj(List<StorageBiz> hqfj) {
        if(hqfj != null) {
            if (this.hqfj == null) {
                this.hqfj = new ArrayList<>();
            }
            this.hqfj.clear();
            this.hqfj.addAll(hqfj);
        }
    }

    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for (StorageBiz storageBiz: this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(!CollectionUtils.isEmpty(this.hqfj)) {
            for (StorageBiz storageBiz: this.hqfj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
        if(Objects.nonNull(this.zw) && StringUtils.isNotBlank(this.zw.getStorageId())){
            this.zw.setBizId(this.id.toString());
        }else {
            this.zw = null;
        }
    }
}
