package com.hzwangda.aigov.modules.document.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.BaseBpmDomain;
import com.hzwangda.aigov.modules.workflow.domain.StorageBiz;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Where;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 公文管理-收文
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
@Entity
@Table(name = "a07_document_sw")
public class A07DocumentSw extends BaseBpmDomain {

    @ApiModelProperty(value = "收文编号")
    @OneToOne(cascade = CascadeType.ALL,orphanRemoval = true)
    @JoinColumn(name = "swbh_id", referencedColumnName = "id")
    private ReferenceNumber swbh;

    @ApiModelProperty(value = "来文编号")
    @OneToOne(cascade = CascadeType.ALL,orphanRemoval = true)
    @JoinColumn(name = "lwbh_id", referencedColumnName = "id")
    private ReferenceNumber lwbh;

    @Column(name = "hj")
    @ApiModelProperty(value = "缓急")
    private String hj;

    @Column(name = "swrq")
    @ApiModelProperty(value = "收文日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date swrq;

    @Column(name = "lwdw")
    @ApiModelProperty(value = "来文单位")
    private String lwdw;

    @Column(name = "swlx")
    @ApiModelProperty(value = "收文类型")
    private String swlx;

    @Column(name = "bt")
    @ApiModelProperty(value = "标题")
    private String bt;

    @Column(name = "lxr")
    @ApiModelProperty(value = "联系人")
    private String lxr;

    @Column(name = "lxrdh")
    @ApiModelProperty(value = "联系人电话")
    private String lxrdh;

    @Column(name = "fkrq")
    @ApiModelProperty(value = "反馈日期")
    @JSONField(format = "yyyy-MM-dd")
    private Date fkrq;

    @ApiModelProperty(value = "附件")
    @OneToMany(targetEntity = StorageBiz.class, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name="biz_id", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @Where(clause = "biz_type='A07DocumentSw.fj'")
    private List<StorageBiz> fj;


    @ApiModelProperty(value = "拟办意见")
    @Lob
    private String yjNb;

    @ApiModelProperty(value = "领导签批")
    @Lob
    private String yjLdqp;

    @ApiModelProperty(value = "办理情况")
    @Lob
    private String yjBlqk;

    @ApiModelProperty(value = "办理结果")
    @Lob
    private String yjBljg;

    public void setFj(List<StorageBiz> fj) {
        if(fj != null) {
            if (this.fj == null) {
                this.fj = new ArrayList<>();
            }
            this.fj.clear();
            this.fj.addAll(fj);
        }
    }

    @PrePersist
    @PreUpdate
    public void preCreateEntity() {
        super.preCreateEntity();
        if(!CollectionUtils.isEmpty(this.fj)) {
            for (StorageBiz storageBiz: this.fj) {
                if(Objects.nonNull(storageBiz) && StringUtils.isBlank(storageBiz.getBizId())) {
                    storageBiz.setBizId(this.id.toString());
                }
            }
        }
    }
}
