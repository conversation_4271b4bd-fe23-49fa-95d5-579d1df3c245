package com.hzwangda.aigov.modules.document.dto;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
* 公文查询
* <AUTHOR>
* @date 2021-07-28
**/
@Data
public class A07DocumentGwQueryCriteria {

    @ApiModelProperty(value = "状态(0:待办,1:已办,4或不传:查询我的待办、已办、申请)")
    private Integer status;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "公文类型key")
    private String processDefinitionKey;

    @Query(type = Query.Type.IN)
    private List<String> bpmInstanceId;

    @ApiModelProperty(value = "待办创建开始结束时间")
    private List<String> timeRange;
}
