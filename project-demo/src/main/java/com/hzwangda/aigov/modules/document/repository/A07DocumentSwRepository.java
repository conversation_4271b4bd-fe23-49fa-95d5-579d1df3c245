/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.hzwangda.aigov.modules.document.repository;

import com.hzwangda.aigov.modules.document.domain.A07DocumentSw;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.sql.Timestamp;
import java.util.List;

/**
* <AUTHOR>
* @date 2021-06-04
**/
public interface A07DocumentSwRepository extends JpaRepository<A07DocumentSw, Long>, JpaSpecificationExecutor<A07DocumentSw> {

    A07DocumentSw findFirstByBpmInstanceId(String procInstanceId);

    @Modifying
    @Query("update A07DocumentSw m set m.createBy=?1,m.createTime=?2 where m.id=?3")
    void updateCreateByAndCreateTimeById(String createBy, Timestamp createTime, Long id);

    @Modifying
    void deleteByBpmInstanceId(String instanceId);

    List<A07DocumentSw> findByBpmInstanceIdIn(List bpmInstanceIds);

    List<A07DocumentSw> findByLwdwContains(String lwdw);

}
