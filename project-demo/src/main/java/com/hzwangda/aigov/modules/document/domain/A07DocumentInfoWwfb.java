package com.hzwangda.aigov.modules.document.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 外网发布
 * <AUTHOR>
 * @date 2021/6/28 下午2:18
 */
@Data
@Entity
@Table(name="a07_document_info_wwfb")
public class A07DocumentInfoWwfb extends BaseDomain {

    @Column(name = "wwfb_code")
    @ApiModelProperty(value = "code")
    private String wwfbCode;

    @Column(name = "wwfb_name")
    @ApiModelProperty(value = "名称")
    private String wwfbName;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;
}
