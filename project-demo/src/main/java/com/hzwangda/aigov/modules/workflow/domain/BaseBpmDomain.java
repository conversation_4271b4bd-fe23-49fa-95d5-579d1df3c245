package com.hzwangda.aigov.modules.workflow.domain;

import com.hzwangda.openserch.annotation.WdSearchId;
import com.wangda.boot.platform.idWorker.IdWorker;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseBpmDomain implements Serializable {

    private static final long serialVersionUID = 8594528325740678114L;

    /**
     * 唯一主键
     */
    @WdSearchId
    @Id
    @NotNull(groups = Update.class)
    @Column(name = "id", updatable = false)
    protected Long id;

    /**
     * 乐观锁
     */
    @Version()
    @Column(name = "version")
    protected Integer version;

    @CreatedBy
    @Column(name = "create_by", updatable = false)
    @ApiModelProperty(value = "创建人", hidden = true)
    private String createBy;

    @LastModifiedBy
    @Column(name = "update_by")
    @ApiModelProperty(value = "更新人", hidden = true)
    private String updateBy;

    @CreationTimestamp
    @Column(name = "create_time", updatable = false)
    @ApiModelProperty(value = "创建时间", hidden = true)
    private Timestamp createTime;

    @UpdateTimestamp
    @Column(name = "update_time")
    @ApiModelProperty(value = "更新时间", hidden = true)
    private Timestamp updateTime;

    @Column(name = "bpm_instance_id")
    @ApiModelProperty(value = "流程实例ID", hidden = true)
    private String bpmInstanceId;

    @Column(name = "bpm_process_key")
    @ApiModelProperty(value = "流程定义Key", hidden = true)
    private String bpmProcessKey;

    @Column(name = "bpm_status")
    @ApiModelProperty(value = "流程状态", hidden = true)
    private String bpmStatus;

    @Column(name = "bpm_subject")
    @ApiModelProperty(value = "标题", hidden = true)
    private String bpmSubject;

    @ElementCollection
    @CollectionTable(name="bpm_user_biz", joinColumns = {
            @JoinColumn(name ="biz_id"),
    }, foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @Column(name="username")
    @ApiModelProperty(value = "参与人集合")
    private List<String> participateUser;

    /* 分组校验 */
    public @interface Create {}

    /* 分组校验 */
    public @interface Update {}

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
