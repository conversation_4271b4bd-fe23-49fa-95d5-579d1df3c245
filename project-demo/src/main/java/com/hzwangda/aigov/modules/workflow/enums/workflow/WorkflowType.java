package com.hzwangda.aigov.modules.workflow.enums.workflow;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30
 * @description 流程类型
 */
@Getter
@AllArgsConstructor
public enum WorkflowType {
    DOCUMENT_ADDRESSEE("documentAddressee","收文办件", "documentAddressee"),
    DOCUMENT_POST("documentPost","发文办件", "documentPost"),
    SEAL_APPROVAL("sealApproval","用印申请", "sealApproval");

    private String value;
    private String name;
    private String icon;

    public static WorkflowType getByValue(String value) {
        for (WorkflowType workflowType : WorkflowType.values()) {
            if (workflowType.value.equals(value)) {
                return workflowType;
            }
        }
        return null;
    }

    /**
     * 根据name获取code
     * @param name
     * @return
     */
    public static String getBy(String name) {
        for (WorkflowType templateModuleEnums : WorkflowType.values()) {
            if (templateModuleEnums.getName().equals(name)) {
                return templateModuleEnums.getValue();
            }
        }
        return null;
    }
}
