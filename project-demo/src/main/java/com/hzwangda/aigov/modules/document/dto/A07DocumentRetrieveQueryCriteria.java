package com.hzwangda.aigov.modules.document.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 全部公文查询
* <AUTHOR>
* @date 2021-07-06
**/
@Data
public class A07DocumentRetrieveQueryCriteria {

    @ApiModelProperty(value = "状态(0:不限,1:24小时内,2:近3天,3:近1个月,10:自定义时间)")
    private Integer timeType;

    @ApiModelProperty(value = "标题")
    private String bt;

    @ApiModelProperty(value = "代字")
    private String dz;

    @ApiModelProperty(value = "年号")
    private Integer nh;

    @ApiModelProperty(value = "序号")
    private Integer xh;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "流程状态(INPROGRESS:流转中,TG:办结)")
    private String bpmStatus;

    @ApiModelProperty(value = "true:本人参与")
    private Boolean myParticipate;

    @ApiModelProperty(value = "公文管理使用。类型。前端无需传值。0:发文,3:收文,其他类型可不传，因为无文号")
    private Integer gwglType;

    @ApiModelProperty(value = "服务事项使用。类型。businessTrip:公差备案,jobEntry:入职管理,jobLeave:调离管理,leaveApproval:因私请假,officeSupplies:物品领用,rentalCars:用车审批,sealApproval:用印审批,travelExpenses:出差报销,workOvertime:加班备案")
    private String fwsxType;
}
