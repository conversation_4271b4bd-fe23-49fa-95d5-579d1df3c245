package com.hzwangda.aigov.modules.sysoption.repository;

import com.wangda.oa.modules.extension.domain.WdSysOption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR>
 * @date 2021/3/29 下午4:10
 */
public interface WdSysOptionRepository extends JpaRepository<WdSysOption, Long>, JpaSpecificationExecutor<WdSysOption> {

    /**
     * 根据key查询数据
     * @param key
     * @return
     */
    WdSysOption getFirstByKey(String key);

    void deleteAllByIdIn(Long[] ids);
}
