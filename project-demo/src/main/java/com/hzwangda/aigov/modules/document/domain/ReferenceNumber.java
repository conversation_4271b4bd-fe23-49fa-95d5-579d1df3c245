package com.hzwangda.aigov.modules.document.domain;

import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/11
 * @description 文号
 * 注意：不要随意改公共组件字段名称，改动后前端表单组件也需要修改相应变动
 */
@Data
@Entity
public class ReferenceNumber extends BaseEntity {

    @Id
    @Column(name = "id")
    @NotNull(groups = {Update.class})
    @ApiModelProperty(value = "id")
    private Long id;

    @Column(name = "dz") // 文种
    @ApiModelProperty(value = "代字")
    private String dz;

    @Column(name = "nh")
    @ApiModelProperty(value = "年号")
    private Integer nh;

    @Column(name = "xh")
    @ApiModelProperty(value = "序号")
    private Integer xh;

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }
}
