package com.hzwangda.aigov.modules.workflow.factory;

import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.workflow.enums.workflow.WorkflowType;
import com.wangda.oa.modules.workflow.dto.BacklogListDto;
import com.wangda.oa.modules.workflow.dto.MyWorkDto;
import com.wangda.oa.modules.workflow.factory.IFlowFormHandleFactory;
import com.wangda.oa.modules.workflow.utils.FlowableUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Objects;

/**
 * 流程表单处理工厂
 * <AUTHOR>
 * @date 2021-07-03
 */
@Component
@AllArgsConstructor
public class FlowFormHandleFactory implements IFlowFormHandleFactory {

    private final HistoryService historyService;

    @Override
    public void handleFormForMyWork(BacklogListDto myWork, MyWorkDto myWorkDto) {

    }

    /**
     * 根据流程定义中标题规则解析
     * @param formDataObj
     * @param subjectRule
     */
    @Override
    public String handleSubjectRule(JSONObject formDataObj, String subjectRule) {
        String bpmInstanceId = formDataObj.getString("bpmInstanceId");
        if(StringUtils.isBlank(bpmInstanceId)) {
            return null;
        }
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(bpmInstanceId).singleResult();

        WorkflowType workflowType = WorkflowType.getByValue(historicProcessInstance.getProcessDefinitionKey());
        String subject = "";
        switch (workflowType) {
            case DOCUMENT_ADDRESSEE:
                subject = FlowableUtils.parseSubjectRule(formDataObj, subjectRule);
                break;
            default:
                if(StringUtils.isNotEmpty(subjectRule)) {
                    subject = FlowableUtils.parseSubjectRule(formDataObj, subjectRule);
                }
        }
        return subject;
    }

    /**
     * 获取表单数据
     * @param procDefKey
     * @param procInstanceId
     * @return
     */
    @Override
    public Object handleFormRecord(String procDefKey, String procInstanceId, String taskDefKey, JSONObject bpmFormData) {
        WorkflowType workflowType = WorkflowType.getByValue(procDefKey);
        if(Objects.isNull(workflowType)) {
            return null;
        }
        Object formData = null;
        switch (workflowType) {
//            case DOCUMENT_SW:
//                formData = a30DocumentSwFormHandle.handleFormRecord(procInstanceId);
//                break;

        }
        return formData;
    }

    @Override
    public void handleAddVariables(String taskId, String procDefKey, Map<String, Object> variables, JSONObject formDataObj) {
        if(StringUtils.isNotBlank(taskId) && !CollectionUtils.isEmpty(variables) && Objects.nonNull(formDataObj)) {
//            HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().processDefinitionKey(procDefKey).taskId(taskId).singleResult();
//
//            //发文高级查询处理
//            if ("document_zrbghjy".equals(procDefKey) || "document_xzfw".equals(procDefKey)
//                    || "document_dwfw".equals(procDefKey) || "document_dwbghjy".equals(procDefKey)||"honest_opinion".equals(procDefKey)) {
//                if ("usertask1".equals(historicTaskInstance.getTaskDefinitionKey())) {
//                    variables.put(DocumentProcessConstants.FWWZ, ObjectUtil.isNotEmpty(formDataObj.getJSONObject("gwwh").get("dz")) ? formDataObj.getJSONObject("gwwh").get("dz") : "");
//                    variables.put(DocumentProcessConstants.NGR, formDataObj.getString("ngr"));
//                    variables.put(DocumentProcessConstants.NGRQ, formDataObj.getString("yfrq"));
//                    variables.put(DocumentProcessConstants.ZBBM, formDataObj.getString("zbbm"));
//                }
//            } else if ("document_sw".equals(procDefKey)) {
//                if ("usertask1".equals(historicTaskInstance.getTaskDefinitionKey())) {
//                    variables.put(DocumentProcessConstants.LWDW, formDataObj.getString("lwdw"));
//                    variables.put(DocumentProcessConstants.NGRQ, formDataObj.getString("swrq"));
//                    variables.put(DocumentProcessConstants.SWDJR, sysUserService.findByName(SecurityUtils.getCurrentUsername()).getNickName());
//                    variables.put(DocumentProcessConstants.SWBM,sysUserService.findByName(SecurityUtils.getCurrentUsername()).getDept().getName());
//                }
//
//            }
        }
    }

    /**
     * 删除表单
     * @param procDefKey
     * @param instanceId
     * @return
     */
    @Override
    public void deleteFormRecord(String procDefKey, String instanceId) {

        WorkflowType workflowType = WorkflowType.getByValue(procDefKey);
        if(Objects.isNull(workflowType)) {
            return ;
        }

        switch (workflowType) {
//            case DOCUMENT_SW:
//                a30DocumentSwFormHandle.deleteFormRecord(instanceId);
//                break;
//            default:
        }
    }
}
