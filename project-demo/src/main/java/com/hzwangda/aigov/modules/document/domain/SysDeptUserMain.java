package com.hzwangda.aigov.modules.document.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * 公文人员关系主表
 * <AUTHOR>
 * @Date 2021/9/18 下午3:18
 **/
@Data
@Entity
@Table(name="sys_dept_user_main")
public class SysDeptUserMain extends BaseDomain implements Serializable {

    @ApiModelProperty(value = "单位集合")
    @OneToMany(targetEntity = SysDeptUserBiz.class, cascade = CascadeType.ALL,orphanRemoval = true)
    @JoinColumn(name="biz_id",foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private List<SysDeptUserBiz> data;

    @ApiModelProperty(value = "描述")
    private String ms;

}
