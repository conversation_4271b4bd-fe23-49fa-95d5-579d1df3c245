package com.hzwangda.aigov.modules.workflow.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzwangda.aigov.modules.workflow.domain.BaseBpmDomain;
import com.wangda.oa.modules.workflow.dto.workflow.FlowTaskDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.flowable.task.api.Task;

import java.util.Date;
import java.util.Objects;

/**
 * 公文任务
 * <AUTHOR>
 * @date 2021/6/21下午8:29
 */
@Data
@Builder
public class FlowTaskInfoDto extends BaseBpmDomain {

    @ApiModelProperty(value = "流程实例编号")
    private String procInsId;

    @ApiModelProperty(value = "任务编号")
    private String taskId;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务处理人名称")
    private String assigneeName;

    @ApiModelProperty("办理时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private Date dealDate;

    @Tolerate
    public FlowTaskInfoDto(){}

    @Tolerate
    public FlowTaskInfoDto(Task t) {
        this.procInsId=t.getProcessInstanceId();
        this.taskId=t.getId();
        this.taskName=t.getName();
        this.dealDate=t.getCreateTime();
        this.assigneeName=t.getAssignee();
    }

    @Tolerate
    public FlowTaskInfoDto(FlowTaskDto t) {
        this.procInsId=t.getProcInsId();
        this.taskId=t.getTaskId();
        this.taskName=t.getTaskName();
        this.dealDate=t.getFinishTime();
        if(Objects.nonNull(t.getAssigneeInfo())) {
            this.assigneeName=t.getAssigneeInfo().getAssigneeName();
        }
    }
}
