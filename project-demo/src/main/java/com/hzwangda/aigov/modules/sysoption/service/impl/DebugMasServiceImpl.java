package com.hzwangda.aigov.modules.sysoption.service.impl;

import com.hzwangda.aigov.modules.sysoption.service.MasService;
import com.hzwangda.aigov.modules.sysoption.service.MasServiceAbs;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DebugMasServiceImpl extends MasServiceAbs implements MasService {

	@Override
	public boolean sendSM(String[] mobiles, String content, long smID, long srcID) throws Exception {
		log.info("sendSM srcID=" + srcID + ",smID=" + smID + "mobiles" + StringUtil.join(mobiles) + "content" + content);
		return true;
	}
}
