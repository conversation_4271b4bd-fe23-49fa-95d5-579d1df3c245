package com.hzwangda.aigov.util;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hzwangda.aigov.modules.sysoption.repository.WdSysOptionRepository;
import com.hzwangda.aigov.modules.sysoption.service.MasServiceAbs;
import com.hzwangda.aigov.modules.workflow.dto.BacklogListBO;
import com.hzwangda.aigov.modules.workflow.dto.BacklogListPageDto;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.boot.platform.utils.JwtTokenUtil;
import com.wangda.boot.response.ResponseInfo;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.security.config.bean.SecurityProperties;
import com.wangda.oa.modules.security.security.CASAuthToken;
import com.wangda.oa.modules.security.security.TokenProvider;
import com.wangda.oa.modules.security.service.OnlineUserService;
import com.wangda.oa.modules.security.service.dto.JwtUserDto;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.modules.workflow.dto.BacklogDto;
import com.wangda.oa.modules.workflow.dto.BacklogReadDto;
import com.wangda.oa.modules.workflow.dto.DeleteByBizIdBo;
import com.wangda.oa.modules.workflow.factory.RestUrlComponent;
import com.wangda.oa.utils.RedisUtils;
import com.wangda.oa.utils.RequestHolder;
import com.wangda.oa.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/3/29 下午2:20
 */
@Slf4j
@Component
public class OaUtil {

    @Resource
    private WdSysOptionRepository wdSysOptionRepository;
    @Resource
    private RedisUtils redisUtilsRes;
    @Resource
    private TokenProvider tokenProviderRes;
    @Resource
    private AuthenticationManagerBuilder authenticationManagerBuilderRes;
    @Resource
    @Qualifier("restTemplate")
    private RestTemplate restTemplateRes;
    @Resource
    private RestUrlComponent restUrlComponentRes;
    @Resource
    private  OnlineUserService onlineUserServiceRes;

    private static String version;
    public static MasServiceAbs masServiceAbs;
    private static RedisUtils redisUtils;
    private static TokenProvider tokenProvider;
    private static AuthenticationManagerBuilder authenticationManagerBuilder;
    private static RestTemplate restTemplate;
    private static RestUrlComponent restUrlComponent;
    private static OnlineUserService onlineUserService;
    /**
     * 默认接口token有效期限，30天
     */
    private static final long dataTokenOverdueTime = 1000 * 60 * 60 * 24 * 30L;

    @PostConstruct
    public void init() {
        WdSysOption wdSysOption=wdSysOptionRepository.getFirstByKey("SMS.Verison");
        redisUtils=redisUtilsRes;
        tokenProvider=tokenProviderRes;
        authenticationManagerBuilder=authenticationManagerBuilderRes;
        restTemplate=restTemplateRes;
        restUrlComponent=restUrlComponentRes;
        onlineUserService = onlineUserServiceRes;
        if(wdSysOption!=null){
            version = wdSysOption.getValue();
            masServiceAbs = MasServiceAbs.getInstance(version);
        }
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.info("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 根据数字转换字母（i=1对应到字母a）
     * @param i
     * @return
     */
    public static String alphanumericArray(int i){
        return String.valueOf((char)(i+96));
    }

    /**
     * 获取最后一段/前面内容
     */
    public static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('/');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(0,dot);
            }
        }
        return filename;
    }

    /**
     * Description: 发送到统一待办
     * @param backlogDto
     * @return: java.lang.Boolean
     * @Date: 2021/7/23 19:09
     * @Author: maogy
     * @throws:
     */
    public static Boolean pushToBacklog(BacklogDto backlogDto) {
        String token = generateAccessToken();
        // build http headers
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.add(JwtTokenUtil.TOKEN_HEADER, JwtTokenUtil.TOKEN_PREFIX + token);
        }catch (Exception e) {
            e.printStackTrace();
        }
        HttpEntity requestEntity = new HttpEntity<>(backlogDto, headers);
        ResponseInfo result = restTemplate.postForObject(restUrlComponent.getBacklogServiceUrl()+"/api/backlog/addOrUpdateBacklog", requestEntity, ResponseInfo.class);
        log.info("发送oa待办:" + result.getCode() + result.getMessage());
        Boolean bool = (Boolean) result.getData();
        return bool;
    }

    /**
     * 发送待办已读
     * @param backlogReadDto
     * @return
     */
    public static Boolean readBacklog(BacklogReadDto backlogReadDto) {
        String token = generateAccessToken();
        // build http headers
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.add(JwtTokenUtil.TOKEN_HEADER, JwtTokenUtil.TOKEN_PREFIX + token);
        }catch (Exception e) {
            e.printStackTrace();
        }
        HttpEntity requestEntity = new HttpEntity<>(backlogReadDto, headers);
        ResponseInfo result = restTemplate.postForObject(restUrlComponent.getBacklogServiceUrl()+"/api/backlog/read", requestEntity, ResponseInfo.class);
        log.info("发送oa待办已读:" + result.getCode() + result.getMessage());
        Boolean bool = (Boolean) result.getData();
        return bool;
    }

    /**
     * Description: 查询待办列表
     * @param backlogDto
     * @return: java.lang.Boolean
     * @Date: 2021/8/30 15:09
     * @Author: dfr
     * @throws:
     */
    public static BacklogListPageDto getBacklogList(BacklogListBO backlogDto) {
        long time=new Date().getTime();
        String json = JSON.toJSONString(backlogDto);
        log.info("查询待办列表参数："+json);

        // build http headers
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.add(JwtTokenUtil.TOKEN_HEADER, JwtTokenUtil.TOKEN_PREFIX + generateAccessToken());
        }catch (Exception e) {
            e.printStackTrace();
        }
        HttpEntity requestEntity = new HttpEntity<>(backlogDto, headers);
        ResponseInfo result = restTemplate.postForObject(restUrlComponent.getBacklogServiceUrl()+"/api/backlog/getBacklogList", requestEntity, ResponseInfo.class);
        log.info("获取oa待办:" + result.getCode() + result.getMessage());
        log.info("耗时:" + (new Date().getTime()-time));
        if(result.getCode()!= ResultCodeEnum.SUCCESS.getCode()){
            throw new BadRequestException(result.getMessage());
        }
        Map map = (Map) result.getData();
        BacklogListPageDto backlogListPageDto=JSON.parseObject(JSONObject.toJSONString(map),BacklogListPageDto.class);
        return backlogListPageDto;
    }

    /**
     * 删除待办
     * @param bizId
     */
    public static ResponseInfo deleteBackLog(DeleteByBizIdBo bizId){
        long time=new Date().getTime();
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.add(JwtTokenUtil.TOKEN_HEADER, JwtTokenUtil.TOKEN_PREFIX + generateAccessToken());
        }catch (Exception e) {
            e.printStackTrace();
        }
        HttpEntity requestEntity = new HttpEntity<>(bizId, headers);
        ResponseInfo result = restTemplate.postForObject(restUrlComponent.getBacklogServiceUrl()+"/api/backlog/deleteBacklog", requestEntity, ResponseInfo.class);
        log.info("获取oa删除待办:" + result.getCode() + result.getMessage());
        log.info("耗时:" + (new Date().getTime()-time));
        if(result.getCode()!= ResultCodeEnum.SUCCESS.getCode()){
            log.error(result.getMessage());
            throw new CustomException(result.getMessage());
        }
        return result;
    }

    /**
     * 生成内部token，解决服务间调用需要token
     * @return
     *
     */
    public static String generateAccessToken() {
        String bearerToken = null;
        HttpServletRequest request = RequestHolder.getHttpServletRequest();
        SecurityProperties properties = SpringContextHolder.getBean(SecurityProperties.class);
        if(request != null){
            bearerToken = request.getHeader(properties.getHeader());
        }
        if(StringUtils.isEmpty(bearerToken)){
            Object accessToekn = redisUtils.get(SystemConstant.DEFAULT_KEY);
            if(Objects.isNull(accessToekn)){
                // 生成令牌
                CASAuthToken authenticationToken = new CASAuthToken("superAdmin");
                Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                String oaToken = tokenProvider.createToken(authentication);
                final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
                // 保存在线信息
                onlineUserService.saveAndSetTime(jwtUserDto, oaToken, request, dataTokenOverdueTime);
                redisUtils.set(SystemConstant.DEFAULT_KEY, oaToken, dataTokenOverdueTime/1000);
                accessToekn = oaToken;
            }
            return accessToekn.toString();
        }
        String replace = bearerToken.replace(properties.getTokenStartWith(), "");
        return replace;

    }

    /**
     * base64转Multipart
     * @param base64
     * @return
     */
    public static MultipartFile base64ToMultipart(String base64) {
        String[] baseStrs = base64.split(",");
        byte[] b = Base64.decode(baseStrs[1]);

        for(int i = 0; i < b.length; ++i) {
            if (b[i] < 0) {
                b[i] += 256;
            }
        }

        return new BASE64DecodedMultipartFile(b, baseStrs[0]);
    }

}
