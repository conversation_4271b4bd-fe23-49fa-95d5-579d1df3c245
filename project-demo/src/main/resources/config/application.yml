server:
  port: 8000
  servlet:
    context-path: /aigov-service
spring:
  main:
    allow-bean-definition-overriding: true
  freemarker:
    check-template-location: false
  profiles:
    active: dev
  jackson:
    time-zone: GMT+8
  data:
    redis:
      repositories:
        enabled: false

  #配置 Jpa
  jpa:
    hibernate:
      ddl-auto: update
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    show-sql: true
    properties:
      hibernate:
        format_sql: true
    open-in-view: true

task:
  pool:
    # 核心线程池大小
    core-pool-size: 5
    # 最大线程数
    max-pool-size: 10
    # 活跃时间
    keep-alive-seconds: 360
    # 队列容量
    queue-capacity: 2048
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml,classpath*:mapper/*/*.xml
  configuration:
    # 打印sql 生产环境请勿使用
    log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/秒
code:
  expiration: 300

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

ip:
  whiteList:
flowable:
  #关闭定时任务JOB
  async-executor-activate: false
  #  将databaseSchemaUpdate设置为true。当Flowable发现库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。
  database-schema-update: false
  #  database-schema: DEV_SJYT_AIGOV

  common:
    app:
      idm-url: http://localhost:${server.port}/${spring.application.name}/idm

  # 核心（流程）
  # 是否需要自动部署流程定义。
  check-process-definitions: false
  send-zwdd: false
  send-backlog: true
  send-zhejianglab: false

management:
  endpoint:
    flowable:
      enabled: true

wps:
  deployType: ${WPS_DEPLOY_TYPE:cloud}
  domain: ${WPS_DOMAIN:https://wwo.wps.cn/office/}
# ureport配置
ureport:
  # 暂不支持ureport.fileStoreDir配置，需采用ureport.resource-location
  # fileStoreDir: ${FILE_PATH_BASE:/usr/wangda/data/}ureport-template/
  # resource-location: ${UREPORT-RESOURCE_LOCATION:file:/usr/wangda/config/ureport/ureportContext.xml}
  resource-location: classpath:ureport/ureportContext.xml
  appId: ${WPS_APP_ID}
  appSecret: ${WPS_APP_SECRET}
  downloadHost: ${url.serverUrl}
autonavi:
  url: https://restapi.amap.com/v3/config/district?parameters
  key: 59c003c5e630d9df710e7d21892585dc
  subDistrict: 3

# 设置id生成器规则，数据中心序号-服务序号，(0~3)-(0~3)
idworker:
  datacenter-worker-ids: 0-0
