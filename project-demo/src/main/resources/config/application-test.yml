#配置数据源
spring:
  datasource:
    druid:
      db-type: com.alibaba.druid.pool.DruidDataSource
      mysql:
        name: DataSource-system-MS
        driverClassName: com.mysql.cj.jdbc.Driver
        url: *************************************************************************************************************************
        username: root
        password: wd@MCC2#1009
        # 初始连接数
        initial-size: 5
        # 最小连接数
        min-idle: 10
        # 最大连接数
        max-active: 20
        # 获取连接超时时间
        max-wait: 5000
        # 连接有效性检测时间
        time-between-eviction-runs-millis: 60000
        # 连接在池中最小生存的时间
        min-evictable-idle-time-millis: 300000
        # 连接在池中最大生存的时间
        max-evictable-idle-time-millis: 900000
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        # 检测连接是否有效oracle
        validation-query: select 1 from dual
      # 配置监控统计
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
      filter:
        stat:
          enabled: true
          # 记录慢SQL
          log-slow-sql: true
          slow-sql-millis: 2000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  redis:
    host: common.redis.ec.hzwangda.com
    #数据库索引
    database: 15
    port: 31110
    password: RApubone95

# 登录相关配置
login:
  # 登录缓存
  cache-enable: true
  #  是否限制单用户登录
  single: false
  #  验证码
  login-code:
    #  验证码类型配置 查看 LoginProperties 类
    code-type: spec
    #  登录图形验证码有效时间/分钟
    expiration: 2
    #  验证码高度
    width: 111
    #  验证码宽度
    heigth: 36
    # 内容长度
    length: 2
    # 字体名称，为空则使用默认字体
    font-name:
    # 字体大小
    font-size: 25
  simple-sso:
    secret: aigov-1fhlkawhsnlv
    services:
      oldOa:
        serviceSecret: aigov-1fhlkawhsnlv
        serviceUrl: https://oa.zjedu.gov.cn/SimpleSso.jsp
      pjxt:
        serviceSecret: aigov-1fhlkawhsnlv
        serviceUrl: https://oa.zjedu.gov.cn/pjxt/simulateLogin

#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  token-validity-in-seconds: 14400000
  # 在线用户key
  online-key: online-token-
  # 验证码
  code-key: code-key-
  # token 续期检查时间范围（默认30分钟，单位毫秒），在token即将过期的一段时间内用户操作了，则给用户的token续期
  detect: 1800000
  # 续期时间范围，默认1小时，单位毫秒
  renew: 3600000

#是否允许生成代码，生产环境设置为false
generator:
  enabled: true

#是否开启 swagger-ui
swagger:
  enabled: true

# IP 本地解析
ip:
  local-parsing: true

# 文件存储路径
file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
  linux:
    path: /usr/wangda/zjjytoa-mgt-server/file/
    avatar: /usr/wangda/zjjytoa-mgt-server/avatar/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5
  url: file
  storageMode: localStorageServiceImpl

  # wps edit
wps:
  domain: https://wwo.wps.cn/office/
  appid: 400f74bb9a81473ca83120fc90120a87
  appsecret: c307cddda24740eb922eb77d916fc4b3
  # wps convert
  convert:
    appid: 400f74bb9a81473ca83120fc90120a87
    appsecret: c307cddda24740eb922eb77d916fc4b3
    convert: https://dhs.open.wps.cn/pre/v1/convert
    query: https://dhs.open.wps.cn/pre/v1/query

  # wps redirect
redirect:
  key: _w_redirectkey
  value: 123456

url:
  #待办服务地址
  backlogServiceUrl: http://zjjytoa-backlog-service.ec.hzwangda.com/backlog-service
  backlogAppId: UNI-OA

  #服务地址
  serverUrl: http://zjjytoa-mgt-api.ec.hzwangda.com
  oldOaServerUrl: http://dingtalk.zjedu.gov.cn
  oldOaFechMainFileUrl: https://dingtalk.zjedu.gov.cn

zwdd:
  domainName: openplatform.dg-work.cn
  protocal: https
  accessKey: ce1-Rykypj5948OxxSN8Aw1aQviyaa
  secretKey: fiz2731XhgBKc47mz6a6qU17FFMK4W4oOZUaL5iX
  applicationId: ce1
  tenantId: 16100
  webUrl: http://zjjytoa-mgt.ec.hzwangda.com
  appUrl: http://zjjytoa-mgt-dingtalk.ec.hzwangda.com
  type: zydd

wd:
  search:
    open-search-url: http://wd-search-service.ec.hzwangda.com
    enable: ESClient

docconvert:
  version: aspose-word
  aspose:
    fontsDirPath: /usr/wangda/zjjytoa-mgt-server/file/

multi-data-source:
  oracle:
    enable: false
  mysql:
    enable: true
  shengtong:
    enable: false

oss:
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  accessKeyId: LTAI4GAJ6ihoryuySkYHDmii
  accessKeySecret: ******************************
  bucketName: dev-qmjs
