<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <!--<properties resource="application.properties"/>-->
    <!-- JDBC Driver 本地位置-->
   <!-- <classPathEntry location="C:\\Users\\<USER>\\.m2\\repository\\mysql\\mysql-connector-java\\5.1.46\\mysql-connector-java-5.1.46.jar"/>-->
    <!-- JD<PERSON> Driver 本地实际位置-->
    <classPathEntry location="/Users/<USER>/work/repo/com/oracle/ojdbc/14/ojdbc-14.jar"/>

    <context id="gysuit"  targetRuntime="MyBatis3">
        <!-- 指定生成的java文件的编码,没有直接生成到项目时中文可能会乱码 -->
        <property name="javaFileEncoding" value="UTF-8"/>
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin" />
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin" />
        <plugin type="org.mybatis.generator.plugins.CaseInsensitiveLikePlugin" />

        <!-- <plugin type="org.mybatis.generator.plugins.MapperConfigPlugin"> <property
            name="targetPackage" value="mybatis" /> <property name="targetProject" value="../rtms-orm/src/main/resources"
            /> </plugin> -->
        <!-- 这里的type里写的是你的实现类的类全路径 -->

        <commentGenerator >
            <property name="javaFileEncoding" value="UTF-8" />
            <property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>


        <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver" connectionURL="***************************************************"
                        userId="DEV_OA_SJYT_MGT" password="DEV_OA_SJYT_MGT">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="true"/>
            <!-- This property is used to specify whether MyBatis Generator should force the use of JSR-310 data types for DATE, TIME,
            and TIMESTAMP fields, rather than using java.util.Date -->
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="com.wangda.domain" targetProject="./src/main/java"><!-- model -->
            <property name="enableSubPackages" value="true" />
            <property name="trimStrings" value="true" />
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="./src/main/resources"><!-- xml -->
            <property name="enableSubPackages" value="true" />
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.wangda.oa.modules.oa.mapper" targetProject="./src/main/java"><!-- mapper -->
            <property name="enableSubPackages" value="true" />
        </javaClientGenerator>
        <!-- 数据库表名 -->
        <!-- 实体类名 -->
        <table tableName="WD_GX_TABLE_2" domainObjectName="WdGxTable2"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>
        <table tableName="WD_GX_TABLE_3" domainObjectName="WdGxTable3"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>
        <table tableName="WD_GX_TABLE_4" domainObjectName="WdGxTable4"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>
    </context>
</generatorConfiguration>