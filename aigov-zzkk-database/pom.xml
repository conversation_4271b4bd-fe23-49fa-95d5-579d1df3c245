<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hzwangda.aigov</groupId>
    <artifactId>aigov-zzkk-database</artifactId>
    <version>1.2.0-SNAPSHOT</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <flowable.version>6.7.2</flowable.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
            <version>2.1.18.RELEASE</version>
        </dependency>

        <!-- 工作流引擎 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <!--      <artifactId>flowable-spring-boot-starter-basic</artifactId>-->
            <version>${flowable.version}</version>
            <exclusions>
                <!-- 去除dmn -->
                <exclusion>
                    <groupId>org.flowable</groupId>
                    <artifactId>flowable-spring-boot-starter-dmn</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.flowable</groupId>
                    <artifactId>flowable-dmn-spring-configurator</artifactId>
                </exclusion>
                <!-- 去除cmmn -->
                <exclusion>
                    <groupId>org.flowable</groupId>
                    <artifactId>flowable-spring-boot-starter-cmmn</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.flowable</groupId>
                    <artifactId>flowable-cmmn-spring-configurator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
