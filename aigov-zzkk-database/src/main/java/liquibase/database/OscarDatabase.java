package liquibase.database;

import liquibase.database.core.OracleDatabase;
import liquibase.exception.DatabaseException;
import org.springframework.stereotype.Component;

/**
 * Encapsulates Oracle database support.
 */
@Component
public class OscarD<PERSON>base extends OracleDatabase {

    public static final String PRODUCT_NAME = "oscar";

    @Override
    public String getShortName() {
        return "oscar";
    }

    @Override
    protected String getDefaultDatabaseProductName() {
        return "Oscar";
    }

    @Override
    public Integer getDefaultPort() {
        return 5236;
    }

    @Override
    public int getPriority() {
        return 6;
    }

    @Override
    public boolean isCorrectDatabaseImplementation(DatabaseConnection conn) throws DatabaseException {
        return PRODUCT_NAME.equalsIgnoreCase(conn.getDatabaseProductName());
    }

    @Override
    public String getDefaultDriver(String url) {
        if (url.startsWith("jdbc:oscar")) {
            return "com.oscar.Driver";
        }
        return null;
    }

}
