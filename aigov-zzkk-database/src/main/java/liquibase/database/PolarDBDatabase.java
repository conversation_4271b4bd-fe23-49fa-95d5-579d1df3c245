package liquibase.database;

import liquibase.database.core.PostgresDatabase;
import liquibase.exception.DatabaseException;
import org.springframework.stereotype.Component;

@Component
public class PolarDBDatabase extends PostgresDatabase {
    public static final String PRODUCT_NAME = "POLARDB Database Compatible with Oracle";
    private static final int POLARDB_DEFAULT_TCP_PORT_NUMBER = 3433;

    @Override
    public String getShortName() {
        return "polardb";
    }

    @Override
    protected String getDefaultDatabaseProductName() {
        return "PolarDB";
    }

    @Override
    public Integer getDefaultPort() {
        return POLARDB_DEFAULT_TCP_PORT_NUMBER;
    }

    @Override
    public int getPriority() {
        return 6;
    }

    @Override
    public boolean isCorrectDatabaseImplementation(DatabaseConnection conn) throws DatabaseException {
        return PRODUCT_NAME.equalsIgnoreCase(conn.getDatabaseProductName());
    }

    @Override
    public String getDefaultDriver(String url) {
        if (url.startsWith("jdbc:polardb:")) {
            return "com.aliyun.polardb.Driver";
        }
        return null;
    }

}
