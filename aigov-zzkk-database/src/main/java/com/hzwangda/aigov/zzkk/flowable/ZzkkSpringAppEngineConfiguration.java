package com.hzwangda.aigov.zzkk.flowable;

import org.flowable.app.spring.SpringAppEngineConfiguration;

import java.util.Properties;

public class ZzkkSpringAppEngineConfiguration extends SpringAppEngineConfiguration {

    public ZzkkSpringAppEngineConfiguration() {
        databaseTypeMappings = getDefaultDatabaseTypeMappings();
    }

    public static Properties getDefaultDatabaseTypeMappings(){
        Properties defaultDatabaseTypeMappings = SpringAppEngineConfiguration.getDefaultDatabaseTypeMappings();
        defaultDatabaseTypeMappings.setProperty("OSCAR", DATABASE_TYPE_ORACLE);
        defaultDatabaseTypeMappings.setProperty("POLARDB Database Compatible with Oracle", DATABASE_TYPE_POSTGRES);
        return defaultDatabaseTypeMappings;
    }

}
