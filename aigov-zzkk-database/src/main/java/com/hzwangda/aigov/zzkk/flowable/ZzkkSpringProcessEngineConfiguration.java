package com.hzwangda.aigov.zzkk.flowable;

import org.flowable.spring.SpringProcessEngineConfiguration;

import java.util.Properties;

public class ZzkkSpringProcessEngineConfiguration extends SpringProcessEngineConfiguration {

    public ZzkkSpringProcessEngineConfiguration() {
        databaseTypeMappings = getDefaultDatabaseTypeMappings();
    }

    public static Properties getDefaultDatabaseTypeMappings(){
        Properties defaultDatabaseTypeMappings = SpringProcessEngineConfiguration.getDefaultDatabaseTypeMappings();
        defaultDatabaseTypeMappings.setProperty("OSCAR", DATABASE_TYPE_ORACLE);
        defaultDatabaseTypeMappings.setProperty("POLARDB Database Compatible with Oracle", DATABASE_TYPE_POSTGRES);
        return defaultDatabaseTypeMappings;
    }

}
