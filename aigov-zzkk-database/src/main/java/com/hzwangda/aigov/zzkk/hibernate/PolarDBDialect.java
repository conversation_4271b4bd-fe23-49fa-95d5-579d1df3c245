package com.hzwangda.aigov.zzkk.hibernate;

import org.hibernate.dialect.PostgresPlusDialect;
import org.hibernate.type.descriptor.sql.*;

import java.sql.Types;

/**
 * PostgresSQL不支持LOB字段，会导致@Lob注解被解析从Long类型。
 * 修改 PostgresPlusDialect 来兼容实体类中的@Lob注解
 * <AUTHOR>
 */
public class PolarDBDialect extends PostgresPlusDialect {

    @Override
    public SqlTypeDescriptor remapSqlTypeDescriptor(SqlTypeDescriptor sqlTypeDescriptor) {
        switch (sqlTypeDescriptor.getSqlType()) {
            case Types.CLOB:
                return LongVarcharTypeDescriptor.INSTANCE;
            case Types.BLOB:
                return LongVarbinaryTypeDescriptor.INSTANCE;
        }
        return super.remapSqlTypeDescriptor(sqlTypeDescriptor);
    }

    @Override
    public SqlTypeDescriptor getSqlTypeDescriptorOverride(int sqlCode) {
        SqlTypeDescriptor descriptor;
        switch (sqlCode) {
            case Types.BLOB:
                // Force BLOB binding. Otherwise, byte[] fields annotated
                // with @Lob will attempt to use
                // BlobTypeDescriptor.PRIMITIVE_ARRAY_BINDING. Since the
                // dialect uses oid for Blobs, byte arrays cannot be used.
                //descriptor = BlobTypeDescriptor.BLOB_BINDING;
                descriptor = BlobTypeDescriptor.STREAM_BINDING;
                break;
            case Types.CLOB:
                //descriptor = ClobTypeDescriptor.CLOB_BINDING;
                descriptor = ClobTypeDescriptor.STREAM_BINDING;
                break;
            default:
                descriptor = super.getSqlTypeDescriptorOverride(sqlCode);
                break;
        }
        return descriptor;
    }
    
}
