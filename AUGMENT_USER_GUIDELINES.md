# AIGOV智治协同系统 - Augment AI 使用指南

## 项目概述

AIGOV智治协同系统是一个基于Spring Boot 2.1.18的政务办公协同平台，采用前后端分离架构，集成了工作流引擎、在线办公、文档管理等核心功能。

### 核心技术栈
- **后端**: Spring Boot 2.1.18, Spring Security, Spring Data JPA, Flowable工作流引擎
- **数据库**: MySQL/Oracle双数据库支持
- **缓存**: Redis (db7)
- **文档处理**: WPS在线编辑、金格中间件
- **存储**: 阿里云OSS对象存储

## 项目架构说明

### 模块结构
```
aigov-service/
├── bf/                          # 基础框架模块
│   ├── bf-common/              # 公共模块(工具类、配置、异常处理)
│   ├── bf-system/              # 系统核心模块(用户管理、权限、监控)
│   ├── bf-logging/             # 日志模块
│   ├── bf-tools/               # 第三方工具模块
│   └── bf-generator/           # 代码生成模块
├── aigov-workflow/             # 工作流引擎模块
├── aigov-storage-oss/          # OSS存储服务模块
├── aigov-zzkk-database/        # 中正科技数据库模块
├── aigov-officeonline/         # 在线办公模块
└── project-demo/               # 业务演示模块(主要启动入口)
```

### 业务功能域
- **用户权限管理**: 基于RBAC的权限控制体系
- **工作流管理**: 基于Flowable的流程引擎
- **公文管理**: 政务公文流转处理
- **在线办公**: WPS/金格在线编辑集成
- **文档存储**: OSS云存储服务
- **系统监控**: 服务器性能监控
- **浙政钉集成**: 政务钉钉单点登录
- **代码生成**: 基于模板的代码自动生成

## Augment AI 使用指南

### 1. 代码查询与理解

#### 查询特定功能实现
```
请帮我查找工作流引擎中流程定义的创建逻辑
```

#### 查询业务模块结构
```
请分析公文管理模块的完整业务流程
```

#### 查询技术实现细节
```
请查看OSS存储服务的文件上传实现方式
```

### 2. 代码开发与修改

#### 新增业务功能
```
请在公文管理模块中新增一个文档审批状态查询接口
```

#### 修改现有功能
```
请修改用户登录逻辑，增加登录失败次数限制功能
```

#### 集成第三方服务
```
请帮我集成钉钉机器人消息推送功能到工作流完成通知中
```

### 3. 问题排查与优化

#### 性能优化
```
请分析并优化公文列表查询的性能问题
```

#### Bug修复
```
工作流流程图显示异常，请帮我排查问题
```

#### 安全加固
```
请检查并加强文件上传的安全验证机制
```

### 4. 数据库操作

#### 数据模型分析
```
请分析工作流相关的数据表结构和关联关系
```

#### SQL优化
```
请优化公文查询的SQL语句性能
```

#### 数据迁移
```
请帮我编写从旧系统迁移用户数据的脚本
```

### 5. 代码生成与模板

#### 代码生成器使用
```
请使用代码生成器为新的业务实体生成CRUD代码
```

#### 模板定制
```
请帮我定制代码生成模板，增加审计字段
```

#### 批量生成
```
请为多个业务表批量生成标准的增删改查功能
```

## 开发规范与约定

### 代码规范
- **Java**: 遵循阿里巴巴Java开发手册
- **数据库**: 表名使用下划线命名，字段名驼峰命名
- **API**: RESTful风格，统一返回格式
- **文档**: 使用Swagger进行API文档管理

### 命名约定
- **Controller**: 以Controller结尾
- **Service**: 以Service结尾，实现类以ServiceImpl结尾
- **Entity**: 与数据表对应的实体类
- **DTO**: 数据传输对象，以Dto结尾
- **VO**: 视图对象，以Vo结尾

### 安全要求
- 所有API接口需要权限验证
- 文件上传需要类型和大小限制
- 敏感数据需要加密存储
- SQL注入防护

### 测试要求
- 单元测试覆盖率不低于70%
- 集成测试覆盖核心业务流程
- 性能测试关注响应时间和并发能力

## 常用开发场景

### 场景1: 新增业务模块
1. 设计数据库表结构
2. 使用代码生成器生成基础CRUD代码
3. 编写业务逻辑Service层
4. 配置权限和API接口
5. 编写单元测试和集成测试

### 场景2: 工作流集成
1. 设计流程定义BPMN文件
2. 配置流程表单和节点处理器
3. 实现业务数据与流程的绑定
4. 开发流程监控和管理功能
5. 集成待办事项和消息通知

### 场景3: 第三方系统集成
1. 分析接口文档和认证方式
2. 封装HTTP客户端调用
3. 实现数据格式转换
4. 添加异常处理和重试机制

## 注意事项

### 开发环境要求
- JDK 1.8+
- Maven 3.6+
- Redis 5.0+
- MySQL 8.0+
- Lombok插件(IDE必须安装)

### 部署相关
- 使用Docker容器化部署
- 配置文件区分环境(dev/test/prod)
- 日志文件统一管理
- 监控告警配置

### 性能考虑
- 数据库连接池配置优化
- Redis缓存策略设计
- 大文件上传分片处理
- 接口响应时间监控

---

**提示**: 在使用Augment AI时，请尽量提供具体的业务场景和技术要求，这样AI能够给出更精准的代码建议和实现方案。
