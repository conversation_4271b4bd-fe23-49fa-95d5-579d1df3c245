package com.wangda.oa.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.wangda.oa.config.OSSConfigurationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class AliOssUtil implements DisposableBean {

    private OSS ossClient;

    private String bucketName;

    /**
     * 创建oss实例
     *
     * @return: com.aliyun.oss.OSS
     *
     */
    public AliOssUtil(OSSConfigurationProperties properties) {
        //获取oss配置
        if(Objects.isNull(properties) || StringUtils.isEmpty(properties.getAccessKeyId())) {
            log.warn("AliOss配置参数还没有配置");
            return;
        }

        //初始化AccessKey
        String accessKeyId = properties.getAccessKeyId();
        //初始化AccessKeySecret
        String accessKeySecret = properties.getAccessKeySecret();
        //初始化Endpoint
        String endpoint = properties.getEndpoint();
        //初始化bucketName
        bucketName = properties.getBucketName();
        // 创建OSSClient的实例
        ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

    }

    public OSS getOssClient() {
        return ossClient;
    }

    public String getBucketName() {
        return bucketName;
    }

    @Override
    public void destroy() throws Exception {
        ossClient.shutdown();
    }
}
