package com.wangda.oa;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.utils.AliOssUtil;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Objects;

/**
 * OSSClient 不需要每次都关闭请求完成后会自动关闭
 * 可以查看源码 80行 会调用关闭链接所以在bean销毁时调用OSSClient.shutdown()
 * {@link com.aliyun.oss.internal.OSSOperation}
 */
@ConditionalOnProperty(value = "file.storageMode", havingValue = "ossStorageServiceImpl", matchIfMissing = true)
@Service
@Slf4j
public class OSSStorageServiceImpl implements IStorageService {

    private final AliOssUtil ossUtil;

    private final LocalStorageRepository localStorageRepository;
    private final String bucketName;

    public OSSStorageServiceImpl(AliOssUtil ossUtil, LocalStorageRepository localStorageRepository) {
        this.ossUtil = ossUtil;
        this.localStorageRepository = localStorageRepository;
        this.bucketName = ossUtil.getBucketName();
    }

    private final static String separator = "/";


    @Override
    public boolean delete(String key) {
        final OSS ossClient = ossUtil.getOssClient();
        ossClient.deleteObject(bucketName, key);
        return true;
    }

    @Override
    public InputStream getInputStream(String key) {
        final OSS ossClient = ossUtil.getOssClient();

        return ossClient.getObject(bucketName, key).getObjectContent();
    }

    @Override
    public LocalStorage create(String name, MultipartFile multipartFile) {
        String suffix = FileUtil.getExtensionName(multipartFile.getOriginalFilename());
        String type = FileUtil.getFileType(suffix);
        final String format = DateUtil.format(new DateTime(), "yyyy/yyyyMM");
        // 例如 2022/202201/id2342332.docx
        String key = format.concat(separator).concat(IdUtil.objectId()).concat(".").concat(suffix);
        try {
            final OSS ossClient = ossUtil.getOssClient();

            // 开始添加到oss
            ossClient.putObject(bucketName, key, multipartFile.getInputStream());

            name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(multipartFile.getOriginalFilename()) : name;
            LocalStorage localStorage = new LocalStorage(multipartFile.getOriginalFilename(), name, suffix, key, type, FileUtil.getSize(multipartFile.getSize()));
            return localStorageRepository.save(localStorage);
        }catch(Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public LocalStorage create(String name, File file) {
        String suffix = FileUtil.getExtensionName(file.getName());
        String type = FileUtil.getFileType(suffix);
        String today = DateUtil.today();
        final String format = DateUtil.format(new DateTime(), "yyyy/yyyyMM");
        // 例如 2022/202201/id2342332.docx
        String key = format.concat(separator).concat(IdUtil.objectId()).concat(".").concat(suffix);
        try {
            final OSS ossClient = ossUtil.getOssClient();

            // 开始添加到oss
            ossClient.putObject(bucketName, key, new FileInputStream(file));

            name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(file.getName()) : name;
            LocalStorage localStorage = new LocalStorage(file.getName(), name, suffix, key, type, FileUtil.getSize(file.length()));
            return localStorageRepository.save(localStorage);
        }catch(Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void downloadFile(Long id, String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception {
        LocalStorage localStorage = localStorageRepository.findById(id).orElseGet(LocalStorage::new);
        final InputStream file = getInputStream(localStorage);
        //先取数据库的文件名称
        String name = getDispositionFileName(fileName, localStorage);
        downloadFile(request, response, file, name);
    }

    @Override
    public void previewFile(Long id, String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception {
        LocalStorage localStorage = localStorageRepository.findById(id).orElseGet(LocalStorage::new);

        GetObjectRequest getObjectRequest = new GetObjectRequest(ossUtil.getBucketName(), localStorage.getPath());
        if(Objects.nonNull(request) && StringUtils.isNotEmpty(request.getParameter("x-oss-process"))) {
            getObjectRequest.setProcess(request.getParameter("x-oss-process"));
        }
        OSSObject object = ossUtil.getOssClient().getObject(getObjectRequest);
        long size = object.getObjectMetadata().getContentLength();
        response.setHeader("Content-Length", Long.toString(size));
        response.setContentType(object.getObjectMetadata().getContentType());
        final InputStream inputStream = object.getObjectContent();
        //先取数据库的文件名称
        String name = getDispositionFileName(fileName, localStorage);

        previewFile(request, response, inputStream, name);
    }

    private String getDispositionFileName(String fileName, LocalStorage localStorage) {
        String name = localStorage.getName();
        // 如果有参数以前端参数为准
        if(StringUtils.isNotEmpty(fileName)) {
            name = fileName;
        }

        //没有后缀名的话就给他拼接一个后缀或者后缀不是一个真实的后缀名
        String suffix = FileUtil.getSuffix(name);
        String pathSuffix = FileUtil.getSuffix(localStorage.getPath());
        if(!pathSuffix.equalsIgnoreCase(suffix)) {
            name = name + "." + pathSuffix;
        }

        // 对文件名进行URL编码
        String encodedFileName = null;
        try {
            encodedFileName = URLEncoder.encode(StrUtil.removeAllLineBreaks(name), "utf-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        // 组装contentDisposition的值
        name = new StringBuilder(encodedFileName).append(";")
                .append("filename*=").append("utf-8''")
                .append(encodedFileName).toString();

        return name;
    }


    @Override
    public long size(String key) {
        final OSS ossClient = ossUtil.getOssClient();
        OSSObject object = ossClient.getObject(bucketName, key);
        return object.getObjectMetadata().getContentLength();
    }

    private InputStream getInputStream(LocalStorage localStorage) throws Exception {
        final OSS ossClient = ossUtil.getOssClient();
        String path = localStorage.getPath();
        OSSObject object = ossClient.getObject(bucketName, path);

        return object.getObjectContent();
    }

    private void downloadFile(HttpServletRequest request, HttpServletResponse response, InputStream inputStream, String fileName) {
        response.setCharacterEncoding(request.getCharacterEncoding());
        response.setContentType("application/octet-stream");
        ServletOutputStream outputStream = null;
        try {
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            outputStream = response.getOutputStream();
            IoUtil.copy(inputStream, outputStream);
            response.flushBuffer();
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }finally {
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
        }
    }

    /**
     * 预览文件
     * @param request     /
     * @param response    /
     * @param inputStream /
     */
    private void previewFile(HttpServletRequest request, HttpServletResponse response, InputStream inputStream, String fileName) {
        response.setCharacterEncoding(request.getCharacterEncoding());
        ServletOutputStream outputStream = null;
        try {
            response.setHeader("Content-Disposition", "inline;filename=" + fileName);
            outputStream = response.getOutputStream();
            IoUtil.copy(inputStream, outputStream);
            response.flushBuffer();
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }finally {
            IoUtil.close(inputStream);
            IoUtil.close(outputStream);
        }
    }
}
