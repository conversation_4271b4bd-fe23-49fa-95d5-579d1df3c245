<h1 style="text-align: center">智治协同系统</h1>

#### 其他依懒
- 使用lombok，开发工具需要导入lombok插件

#### 项目简介
一个基于 Spring Boot 2.1.0 、 Spring Boot Jpa、 JWT、Spring Security、Redis、Vue的前后端分离的后台管理系统 v2.6

**账号密码：** `admin / 123456`

- `oa-mgt-server` 代办服务，端口：8000
- `backlog-service` 代办服务，端口：8001

#### 中间件
- `redis` db7

#### 项目结构
项目采用按功能分模块的开发方式，结构如下

- `oa-admin-common` 为系统的公共模块，各种工具类，公共配置存在该模块

- `oa-admin-system` 为系统核心模块也是项目入口模块，也是最终需要打包部署的模块

- `oa-admin-logging` 为系统的日志模块，其他模块如果需要记录日志需要引入该模块

- `oa-admin-tools` 为第三方工具模块，包含：图床、邮件、云存储、本地存储、支付宝

- `oa-admin-generator` 为系统的代码生成模块，代码生成的模板在 system 模块中

#### 详细结构

```
- oa-admin-common 公共模块
    - annotation 为系统自定义注解
    - aspect 自定义注解的切面
    - base 提供了Entity、DTO基类和mapstruct的通用mapper
    - config 自定义权限实现、redis配置、swagger配置、Rsa配置等
    - exception 项目统一异常的处理
    - utils 系统通用工具类
- oa-admin-system 系统核心模块（系统启动入口）
	- config 配置跨域与静态资源，与数据权限
	    - thread 线程池相关
	- modules 系统相关模块(登录授权、系统监控、定时任务、运维管理等)
	- aigov
	    - bpm 工作流相关
	    - docconvert 文件转化服务客户端
	    - officeonline 文件在线编辑相关
	    - sso 单点登录
	    - uc 统一用户
	    - zwdd 浙政钉（政务钉钉）
- oa-admin-logging 系统日志模块
- oa-admin-tools 系统第三方工具模块
- oa-admin-generator 系统代码生成模块
```
    
