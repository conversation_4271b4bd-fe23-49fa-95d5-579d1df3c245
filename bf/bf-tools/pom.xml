<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hzwangda.bf</groupId>
        <artifactId>bf</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bf-tools</artifactId>
    <name>工具模块</name>

    <properties>
        <mail.version>1.5.0-b01</mail.version>
        <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
        <alipay.version>4.23.26.ALL</alipay.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
        </dependency>
        <!-- 同时需要common模块和logging模块只需要引入logging模块即可 -->
        <dependency>
            <groupId>com.hzwangda.bf</groupId>
            <artifactId>bf-logging</artifactId>
            <version>1.2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--邮件依赖-->
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
            <version>${mail.version}</version>
        </dependency>

        <!--七牛云存储-->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>

        <!--支付宝依赖-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>${alipay.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>HzWangdaRepo</id>
            <url>https://mvnrepository.hzwangda.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
