package com.wangda.oa.service;

import com.wangda.oa.domain.LocalStorage;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;

public interface IStorageService {

    /**
     * 存储模式，是否本地存储。
     * @return
     */
    default boolean isLocalStorageMode() {
        return false;
    }

    /**
     * 一般情况下，不支持文件更新操作。只允许创建、删除。
     * @param key
     * @param name
     * @param file
     * @return
     */
    default String update(String key, String name, File file) {
        return null;
    }

    boolean delete(String key);

    InputStream getInputStream(String key);

    /**
     * 上传
     * @param name 文件名称
     * @param file 文件
     * @return
     */
    LocalStorage create(String name, MultipartFile file);

    /**
     * 创建文件
     * @param name 文件名称
     * @param file 文件
     * @return
     */
    LocalStorage create(String name, File file);

    /**
     * Description: 文件下载
     * @param id
     * @param fileName
     * @return: java.lang.String
     * @Date: 2021/6/15 20:25
     * @Author: maogy
     * @throws:
     */
    void downloadFile(Long id, String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception;

    default void downloadFile(Long id, HttpServletResponse response, HttpServletRequest request) throws Exception {
        downloadFile(id, null, response, request);
    }

    /**
     * 预览附件
     * @param id
     * @param fileName
     * @param response
     * @param request
     * @throws Exception
     */
    void  previewFile(Long id, String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception;

    long size(String key);

}
