/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.rest;

import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageQueryCriteria;
import com.wangda.oa.service.mapstruct.LocalStorageMapper;
import com.wangda.oa.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;

/**
 * <AUTHOR> Jie
 * @date 2019-09-05
 */
@Validated
@RestController
@RequiredArgsConstructor
@Api(tags = "工具：本地存储管理")
@RequestMapping("/api/localStorage")
public class LocalStorageController {

    private final IStorageService storageService;
    private final StorageManageService storageManageService;
    private final LocalStorageMapper localStorageMapper;
    private final FileProperties fileProperties;

    @ApiOperation("查询文件")
    @GetMapping
    @PreAuthorize("@el.check('storage:list')")
    public ResponseEntity<Object> query(LocalStorageQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(storageManageService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("查询文件")
    @PostMapping(value = "/findByIds")
    public ResponseEntity<Object> findByIds(@NotBlank String ids) {
        return new ResponseEntity<>(storageManageService.findByIds(ids), HttpStatus.OK);
    }

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('storage:list')")
    public void download(HttpServletResponse response, LocalStorageQueryCriteria criteria) throws IOException {
        storageManageService.download(storageManageService.queryAll(criteria), response);
    }

    @Log("上传文件")
    @ApiOperation("上传文件")
    @PostMapping
    public ResponseEntity<Object> create(@RequestParam String name, @RequestParam("file") MultipartFile file) {
        // 对上传文件进行安全检查
        String suffixAllowlist = fileProperties.getSuffixAllowlist();
        if(StringUtils.isNotBlank(suffixAllowlist)) {
            String suffix = FileUtil.getExtensionName(file.getOriginalFilename());
            if(StringUtils.isNotBlank(suffix) && !ArrayUtils.contains(suffixAllowlist.split("\\,"), suffix)) {
                throw new BadRequestException("因安全原因，不允许上传" + suffix + "格式文件");
            }
        }

        String suffixBlocklist = fileProperties.getSuffixBlocklist();
        if(StringUtils.isNotBlank(suffixBlocklist)) {
            String suffix = FileUtil.getExtensionName(file.getOriginalFilename());
            if(StringUtils.isNotBlank(suffix) && ArrayUtils.contains(suffixBlocklist.split("\\,"), suffix)) {
                throw new BadRequestException("因安全原因，不允许上传" + suffix + "格式文件");
            }
        }

        LocalStorage localStorage = storageService.create(name, file);
        return new ResponseEntity<>(localStorageMapper.toDto(localStorage), HttpStatus.CREATED);
    }

    @Log("上传图片")
    @PostMapping("/pictures")
    @ApiOperation("上传图片")
    public ResponseEntity<Object> upload(@RequestParam MultipartFile file) {
        // 判断文件是否为图片
        String suffix = FileUtil.getExtensionName(file.getOriginalFilename());
        if(!FileUtil.IMAGE.equals(FileUtil.getFileType(suffix))) {
            throw new BadRequestException("只能上传图片");
        }
        LocalStorage localStorage = storageService.create(null, file);
        return new ResponseEntity<>(localStorage, HttpStatus.OK);
    }

    @ApiOperation("修改文件")
    @PutMapping
    public ResponseEntity<Object> update(@Validated @RequestBody LocalStorage resources) {
        storageManageService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("多选删除")
    @DeleteMapping
    @ApiOperation("多选删除")
    public ResponseEntity<Object> delete(@RequestBody Long[] ids) {
        storageManageService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("下载文件")
    @ApiOperation("下载文件")
    @GetMapping(value = "/downloadFile/{id}")
    @AnonymousAccess
    public ResponseEntity<Object> downloadFile(@PathVariable Long id, String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception {
        try {
            storageService.downloadFile(id, fileName, response, request);
        }catch(Exception e) {
            throw new BadRequestException("找不到该文件资源");
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("预览文件")
    @GetMapping(value = "/previewFile/{id}")
    @AnonymousAccess
    public ResponseEntity<Object> previewFile(@PathVariable Long id, String fileName, HttpServletResponse response, HttpServletRequest request) throws Exception {
        try {
            storageService.previewFile(id, fileName, response, request);
        }catch(Exception e) {
            throw new BadRequestException("找不到该文件资源");
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
