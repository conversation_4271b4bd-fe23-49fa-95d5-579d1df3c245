package com.wangda.oa.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.MimeContentType;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jmimemagic.Magic;
import net.sf.jmimemagic.MagicMatch;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@ConditionalOnProperty(value = "file.storageMode", havingValue = "localStorageServiceImpl", matchIfMissing = true)
@Slf4j
public class LocalStorageServiceImpl implements IStorageService {

    private final LocalStorageRepository localStorageRepository;
    private final FileProperties properties;

    @Override
    public boolean isLocalStorageMode() {
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LocalStorage create(String name, MultipartFile multipartFile) {
        FileUtil.checkSize(properties.getMaxSize(), multipartFile.getSize());
        String suffix = FileUtil.getExtensionName(multipartFile.getOriginalFilename());
        String type = FileUtil.getFileType(suffix);
        File file = FileUtil.upload(multipartFile, properties.getPath().getPath() + type + File.separator);
        if(ObjectUtil.isNull(file)) {
            throw new BadRequestException("上传失败");
        }
        try {
            name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(multipartFile.getOriginalFilename()) : name;

            // path优先存相对路径
            String basePath = properties.getPath().getPath();
            String relativePath = file.getPath();
            if(relativePath.startsWith(basePath)) {
                int beginIndex = properties.getPath().getPath().length();
                relativePath = file.getPath().substring(beginIndex);
            }

            LocalStorage localStorage = new LocalStorage(
                    file.getName(),
                    name,
                    suffix,
                    relativePath,
                    type,
                    FileUtil.getSize(multipartFile.getSize())
            );
            return localStorageRepository.save(localStorage);
        }catch(Exception e) {
            FileUtil.del(file);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LocalStorage create(String name, File file) {
        FileUtil.checkSize(properties.getMaxSize(), file.length());
        String suffix = FileUtil.getExtensionName(file.getName());
        if(ObjectUtil.isNull(file)) {
            throw new BadRequestException("文件不存在");
        }
        try {
            name = StringUtils.isBlank(name) ? FileUtil.getFileNameNoEx(file.getName()) : name;
            String type = FileUtil.getFileType(suffix);

            // path若已经在存储路径下，优先存相对路径；否则将文件拷贝到存储路径下
            String basePath = properties.getPath().getPath();
            String relativePath = file.getPath();
            if(relativePath.startsWith(basePath)) {
                int beginIndex = properties.getPath().getPath().length();
                relativePath = file.getPath().substring(beginIndex);
            }else {
                relativePath = FileUtil.newRelativeFilePath(name, suffix);
                file = FileUtil.copy(file.getPath(), basePath + relativePath, false);

                int beginIndex = properties.getPath().getPath().length();
                relativePath = file.getPath().substring(beginIndex);
            }

            LocalStorage localStorage = new LocalStorage(
                    file.getName(),
                    name,
                    suffix,
                    relativePath,
                    type,
                    FileUtil.getSize(file.length())
            );
            return localStorageRepository.save(localStorage);
        }catch(Exception e) {
            FileUtil.del(file);
            throw e;
        }
    }

    @Override
    public void downloadFile(Long id, String fileName, HttpServletResponse response, HttpServletRequest request) {
        LocalStorage localStorage = localStorageRepository.findById(id).orElseGet(LocalStorage::new);
        String path = localStorage.getPath();
        File file = getFile(path);
        if(StringUtils.isEmpty(fileName)) {
            fileName = localStorage.getName();
        }
        downloadFile(request, response, file, fileName, false);
    }

    @Override
    public void previewFile(Long id, String fileName, HttpServletResponse response, HttpServletRequest request) {
        LocalStorage localStorage = localStorageRepository.findById(id).orElseGet(LocalStorage::new);
        String path = localStorage.getPath();
        File file = getFile(path);
        if(StringUtils.isEmpty(fileName)) {
            fileName = localStorage.getName();
        }
        previewFile(request, response, file, fileName);
    }

    @Override
    public boolean delete(String key) {
        return FileUtil.del(key);
    }

    public File getFile(String key) {
        String path = key;
        if(StringUtils.isEmpty(path)) {
            return null;
        }

        String basePath = properties.getPath().getPath();
        if(Objects.nonNull(path) && !path.startsWith(basePath)) {
            path = basePath + path;
        }
        Path filePath = Paths.get(path);
        File file = filePath.toFile();

        return file;
    }

    @Override
    public long size(String key) {
        return getFile(key).length();
    }

    @Override
    public InputStream getInputStream(String key) {
        File file = getFile(key);
        if(Objects.isNull(file)) {
            return null;
        }

        try {
            FileInputStream is = new FileInputStream(file);
            return is;
        }catch(FileNotFoundException e) {
            e.printStackTrace();
            return null;
        }
    }

    private void previewFile(HttpServletRequest request, HttpServletResponse response, File file, String fileName) {
        response.setCharacterEncoding(request.getCharacterEncoding());
        FileInputStream fis = null;
        ServletOutputStream outputStream = null;
        String contentType = null;
        try {
            String name = file.getName();
            int pos = name.lastIndexOf(46);
            if(pos > -1) {
                String ext = name.substring(pos + 1);
                if(MimeContentType.MIME_TYPE.containsKey(ext)) {
                    contentType = MimeContentType.MIME_TYPE.get(ext);
                }
            }

            if(StringUtils.isNotEmpty(contentType)) {
                response.setContentType(contentType);
            }else {
                MagicMatch magicMatch = Magic.getMagicMatch(file, true);
                response.setContentType(magicMatch.getMimeType());
            }
            response.setHeader("Content-Disposition", "inline;filename=" + getFixedFileName(file, fileName));
            response.setHeader("Content-Length", Long.toString(file.length()));

            fis = new FileInputStream(file);
            outputStream = response.getOutputStream();
            IoUtil.copy(fis, outputStream);
            response.flushBuffer();
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }finally {
            if(fis != null) {
                try {
                    fis.close();
                }catch(IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if(outputStream != null) {
                try {
                    outputStream.close();
                }catch(IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    private void downloadFile(HttpServletRequest request, HttpServletResponse response, File file, String fileName, boolean deleteOnExit) {
        response.setCharacterEncoding(request.getCharacterEncoding());
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + getFixedFileName(file, fileName));
        response.setHeader("Content-Length", Long.toString(file.length()));
        FileInputStream fis = null;
        ServletOutputStream outputStream = null;
        try {
            fis = new FileInputStream(file);
            outputStream = response.getOutputStream();
            IoUtil.copy(fis, outputStream);
            response.flushBuffer();
        }catch(Exception e) {
            log.error(e.getMessage(), e);
        }finally {
            if(fis != null) {
                try {
                    fis.close();
                    if(deleteOnExit) {
                        file.deleteOnExit();
                    }
                }catch(IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if(outputStream != null) {
                try {
                    outputStream.close();
                }catch(IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    private String getFixedFileName(File file, String fileName) {
        // 文件名需要带后缀名，按需补全
        if(StringUtils.isEmpty(fileName)) {
            fileName = file.getName();
        }else {
            int dotIndex = file.getName().lastIndexOf(".");
            if(dotIndex > -1) {
                String fileSuffix = file.getName().substring(dotIndex);
                if(!fileName.endsWith(fileSuffix)) {
                    fileName += fileSuffix;
                }
            }
        }

        // 对文件名进行URL编码
        String encodedFileName = null;
        try {
            encodedFileName = URLEncoder.encode(fileName, "utf-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        // 组装contentDisposition的值
        fileName = new StringBuilder(encodedFileName).append(";")
                .append("filename*=").append("utf-8''")
                .append(encodedFileName).toString();

        return fileName;
    }

}
