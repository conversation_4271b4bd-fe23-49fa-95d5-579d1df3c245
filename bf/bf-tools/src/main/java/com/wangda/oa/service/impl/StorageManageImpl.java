package com.wangda.oa.service.impl;

import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.service.dto.LocalStorageQueryCriteria;
import com.wangda.oa.service.mapstruct.LocalStorageMapper;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StorageManageImpl implements StorageManageService {

    private final IStorageService storageService;
    private final LocalStorageRepository localStorageRepository;
    private final LocalStorageMapper localStorageMapper;

    @Override
    public Object queryAll(LocalStorageQueryCriteria criteria, Pageable pageable) {
        Page<LocalStorage> page = localStorageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(localStorageMapper::toDto));
    }

    @Override
    public List<LocalStorageDto> queryAll(LocalStorageQueryCriteria criteria) {
        return localStorageMapper.toDto(localStorageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    public LocalStorageDto findById(Long id) {
        LocalStorage localStorage = localStorageRepository.findById(id).orElseGet(LocalStorage::new);
        return localStorageMapper.toDto(localStorage);
    }

    @Override
    public List<LocalStorageDto> findByIds(String ids) {
        if(StringUtils.isEmpty(ids)) {
            return null;
        }
        List<Long> storageIds = Arrays.asList(ids.split(ElAdminConstant.SEPARATOR_COMMA)).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<LocalStorage> localStorage = localStorageRepository.findAllById(storageIds);
        List<LocalStorageDto> localStorageDtoList = localStorageMapper.toDto(localStorage);
        if(storageIds.size() > 1) {
            Map<Long, Integer> sortedMap = new HashMap();
            for(int i = 0; i < storageIds.size(); i++) {
                sortedMap.put(storageIds.get(i), i);
            }
            localStorageDtoList = localStorageDtoList.stream().map(localStorageDto -> {
                localStorageDto.setSort(sortedMap.get(localStorageDto.getId()));
                return localStorageDto;
            }).sorted(Comparator.comparing(LocalStorageDto::getSort, Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
        }
        return localStorageDtoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(LocalStorage resources) {
        LocalStorage localStorage = localStorageRepository.findById(resources.getId()).orElseGet(LocalStorage::new);
        ValidationUtil.isNull(localStorage.getId(), "LocalStorage", "id", resources.getId());
        localStorage.copy(resources);
        localStorageRepository.save(localStorage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(Long[] ids) {
        for(Long id : ids) {
            LocalStorage storage = localStorageRepository.findById(id).orElseGet(LocalStorage::new);
            storageService.delete(storage.getPath());
            localStorageRepository.delete(storage);
        }
    }

    @Override
    public void download(List<LocalStorageDto> queryAll, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for(LocalStorageDto localStorageDTO : queryAll) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("文件名", localStorageDTO.getRealName());
            map.put("备注名", localStorageDTO.getName());
            map.put("文件类型", localStorageDTO.getType());
            map.put("文件大小", localStorageDTO.getSize());
            map.put("创建者", localStorageDTO.getCreateBy());
            map.put("创建日期", localStorageDTO.getCreateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

}
