package com.wangda.oa.modules.system.repository;

import com.wangda.oa.modules.system.domain.Area;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-10-25
 */
public interface AreaRepository extends JpaRepository<Area, Long>, JpaSpecificationExecutor<Area>  {

    List<Area> findByLevel(String level);

    List<Area> findByCodeAreaAndLevel(String code, String level);

    List<Area> findByParentCode(Long parentCode);

}
