package com.wangda.oa.modules.extension.repository;

import com.wangda.oa.modules.extension.domain.WdGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20 上午10:08
 */
public interface WdGroupRepository extends JpaRepository<WdGroup, Long>, JpaSpecificationExecutor<WdGroup> {

    /**
     * 根据父id查询
     * @param pid
     * @return
     */
    int countByPid(Long pid);

    /**
     * 根据创建人查询数据
     * @param creatorId
     * @return
     */
    List<WdGroup> findByCreatorId(Long creatorId);

    /**
     * 根据创建人和类型查询数据
     * @param creatorId
     * @param type
     * @return
     */
    List<WdGroup> findByCreatorIdAndType(Long creatorId,Integer type);

    /**
     * 根据父id和类型查询数据
     * @param pid
     * @param type
     * @return
     */
    List<WdGroup> findByPidAndType(Long pid,Integer type);

    /**
     * 根据pid和type删除数据
     * @param pid
     * @param type
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByPidAndType(Long pid,Integer type);

    /**
     * 根据pid删除数据
     * @param pid
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByPid(Long pid);
}
