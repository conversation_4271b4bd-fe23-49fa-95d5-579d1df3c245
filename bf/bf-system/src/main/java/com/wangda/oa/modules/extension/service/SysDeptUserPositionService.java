package com.wangda.oa.modules.extension.service;

import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.extension.bo.*;
import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.dto.GroupUserQueryCriteria;
import com.wangda.oa.modules.extension.dto.org.OrgListDto;
import com.wangda.oa.modules.extension.dto.org.OrgListTableDto;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import com.wangda.oa.modules.extension.dto.org.UserPhoneListDto;
import com.wangda.oa.modules.extension.enums.PositionEnum;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午3:02
 */
public interface SysDeptUserPositionService {

    /**
     * 根据类别查询负责人
     * @param type
     * @param userId
     * @return
     */
    SysDeptUserPosition findUserByUserId(String type, Long userId);

    /**
     * 获取当前用户
     * @return UserListDto
     */
    UserListDto getCurrentSelectedUser();

    /**
     * 查询不在类别中的人
     * @param userId
     * @param types
     * @return
     */
    SysDeptUserPosition findUserByUserIdAndTypeIn(Long userId, List<String> types);

    /**
     * 新增或修改部门职位
     * @param bo
     * @return
     */
    ResultJson<Boolean> addOrUpdate(PositionBO bo);

    /**
     * 新增或修改部门职位(多个人)
     * @param bo
     * @return
     */
    ResultJson<Boolean> addOrUpdateUsers(PositionBatchBO bo);

    /**
     * 删除部门职位
     * @param bo
     * @return
     */
    ResultJson<Boolean> delete(PositionDeleteBO bo);

    /**
     * 查询部门职位列表
     * @param bo
     * @return
     */
    ResultJson<List<SysDeptUserPosition>> getList(PositionListBO bo);

    /**
     * 查询部门职位列表
     * @param bo
     * @return
     */
    List<SysDeptUserPosition> findList(PositionListBO bo);


    /**
     * 根据类别查询部门职位列表
     * @param positionEnum 类别
     * @return
     */
    List<SysDeptUserPosition> findListByType(PositionEnum positionEnum);

    /**
     * 查询组织架构
     * @return
     */
    ResultJson<Object> getOrgList(CommonControlsBO bo);

    /**
     * 查询组织用户
     * @return
     */
    ResultJson<List<OrgListDto>> getOrgUserList(CommonSelectBO bo);

    /**
     * 查询通讯录通用控件
     * @param bo
     * @return
     */
    ResultJson<Object> getOrgPhoneList(CommonPhoneControlsBO bo);

    /**
     * 根据用户名查询用户集合
     * @param bo
     * @return
     */
    ResultJson<List<UserListDto>> getUserList(UserListBO bo);

    /**
     * 根据用户名查询用户集合
     * @param bo
     * @return
     */
    ResultJson<List<UserPhoneListDto>> getUserPhoneList(UserListBO bo);

    /**
     * 新增或修改条线
     * @param bo
     * @return
     */
    ResultJson<Boolean> addOrUpdateLine(WdLineBO bo);

    /**
     * 删除条线
     * @param bo
     * @return
     */
    ResultJson<Boolean> deleteLine(PositionDeleteBO bo);

    /**
     * 查询条线的树(只返回组)
     * @param bo
     * @return
     */
    ResultJson<Object> getLineListTree(WdLineListBO bo);

    /**
     * 查询条线树
     * @return
     */
    ResultJson<Object> getLineTreeList();

    /**
     * 根据类型新增推荐人
     * @param bo
     * @return
     */
    ResultJson<Boolean> addRecentlyPeople(RecentlyPeopleListBO bo);

    /**
     * 根据类型查询推荐人
     * @param bo
     * @return
     */
    ResultJson<Object> getRecentlyPeopleList(RecentlyPeopleBO bo);

    /**
     * 新增或修改常用组
     * @param bo
     * @return
     */
    ResultJson<Boolean> addOrUpdateGroup(WdGroupBO bo);

    /**
     * 新增或修改常用组的人
     * @param bo
     * @return
     */
    ResultJson<Boolean> addOrUpdateGroupUser(WdGroupUserBO bo);

    /**
     * 删除常用组或人
     * @param bo
     * @return
     */
    ResultJson<Boolean> deleteGroup(WdDeleteGroupBO bo);

    /**
     * 查询常用组的树和人
     * @param userId
     * @return
     */
    ResultJson<Object> getGroupListTree(Long userId, Integer type);

    /**
     * 查询常用组的树
     * @param userId
     * @return
     */
    ResultJson<Object> getGroupListTreeAndType(Long userId, Integer type);

    /**
     * 查询常用组的人
     * @param criteria
     * @param pageable
     * @return
     */
    ResultJson<Object> getGroupUserList(GroupUserQueryCriteria criteria, Pageable pageable);

    /**
     * 根据职位查询部门表格树
     * @param bo
     * @return
     */
    ResultJson<List<OrgListTableDto>> getDeptTableList(DeptTableBO bo);

    /**
     * 查询常用组的人(不分页)
     * @return
     */
    ResultJson<Object> getAllGroupUserList(GroupUserQueryCriteria criteria);

    /**
     * 根据部门ID和职位查询对应人员
     * @param deptId
     * @param value
     * @return List
     */
    List<SysDeptUserPosition> findUserByDeptAndPosition(Long deptId, PositionEnum value);

    List<Long> findUserByDept(Long deptId, PositionEnum value);

    /**
     * 根据类别查询
     * @param types
     * @return
     */
    List<SysDeptUserPosition> findByTypeIn(List<String> types);
}
