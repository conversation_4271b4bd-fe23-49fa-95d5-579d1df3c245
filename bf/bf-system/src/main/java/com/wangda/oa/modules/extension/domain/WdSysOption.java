package com.wangda.oa.modules.extension.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @Data
 * @description
 */

@Data
@Entity
@Table(name = "WD_SYS_OPTION")
public class WdSysOption extends BaseDomain {

    @Column(name = "key_type")
    @ApiModelProperty(value = "配置项类别")
    @TableField("key_type")
    private String key;
    @Column(name = "name")
    @ApiModelProperty(value = "配置项名称")
    private String name;
    @Column(name = "value")
    @ApiModelProperty(value = "配置项值")
    private String value;
    @Column(name = "expired")
    @ApiModelProperty(value = "是否过期")
    private Date expired;

}
