package com.wangda.oa.modules.system.service.impl;

import com.wangda.oa.modules.system.domain.UserSettings;
import com.wangda.oa.modules.system.repository.UserSettingsRepository;
import com.wangda.oa.modules.system.service.UserSettingsService;
import com.wangda.oa.modules.system.service.dto.UserSettingsQueryCriteria;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @program: oa-mgt-server
 * @description: 用户个性化配置
 * @author: maogy
 * @create: 2021-12-03 17:21
 **/
@Service
@RequiredArgsConstructor
public class UserSettingsServiceImpl implements UserSettingsService {

    private final UserSettingsRepository userSettingsRepository;

    @Override
    public Map<String, Object> queryPage(UserSettingsQueryCriteria queryCriteria, Pageable pageable) {
        Page<UserSettings> page = userSettingsRepository.findAll((root, query, cb)-> QueryHelp.getPredicate(root,queryCriteria,cb),pageable);
        return PageUtil.toPage(page);
    }

    @Override
    public UserSettings add(UserSettings userSettings) {
        return userSettingsRepository.save(userSettings);
    }

    @Override
    public UserSettings update(UserSettings userSettings) {
        UserSettings updateUserSettings = userSettingsRepository.findById(userSettings.getId()).orElseGet(UserSettings::new);
        ValidationUtil.isNull(updateUserSettings,"D06GoodsCategory","id",userSettings.getId());
        updateUserSettings.copy(userSettings);
        updateUserSettings = userSettingsRepository.save(updateUserSettings);
        return updateUserSettings;
    }

    @Override
    public void deleteByIds(Long[] ids) {
        userSettingsRepository.deleteAllByIdIn(ids);
    }

    @Override
    public UserSettings findByUserName(String userName) {
        return userSettingsRepository.findFirstByUserName(userName);
    }
}
