/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.repository;

import com.wangda.oa.modules.system.domain.Dept;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-03-25
 */
public interface DeptRepository extends JpaRepository<Dept, Long>, JpaSpecificationExecutor<Dept> {

    /**
     * 根据 PID 查询
     * @param id pid
     * @return /
     */
    List<Dept> findByPid(Long id);

    /**
     * 获取顶级部门
     * @return /
     */
    List<Dept> findByPidIsNull();

    /**
     * 获取启用的顶级部门
     * @return /
     */
    @Query("from Dept d where d.pid is null and d.enabled = true order by d.deptSort asc")
    List<Dept> findByPidIsNullAndEnabled();

    @Query("from Dept d where d.pid = :pid and d.enabled = true order by d.deptSort asc ")
    List<Dept> findByPidAndEnabled(Long pid);

    @Query("from Dept d where d.name like :name and d.enabled = :enabled order by d.deptSort asc ")
    List<Dept> findByNameAndEnabled(String name, Boolean enabled);

    /**
     * 根据角色ID 查询
     * @param roleId 角色ID
     * @return /
     */
    @Query(value = "select d.* from sys_dept d, sys_roles_depts r where " +
            "d.dept_id = r.dept_id and r.role_id = ?1", nativeQuery = true)
    Set<Dept> findByRoleId(Long roleId);

    /**
     * 判断是否存在子节点
     * @param pid /
     * @return /
     */
    int countByPid(Long pid);

    /**
     * 根据ID更新sub_count
     * @param count /
     * @param id    /
     */
    @Modifying
    @Query(value = " update sys_dept set sub_count = ?1 where dept_id = ?2 ", nativeQuery = true)
    void updateSubCntById(Integer count, Long id);

    /**
     * 根据id查询顶级部门
     * @param deptIds
     * @return List
     */
    List<Dept> findAllByIdInAndPidIsNullOrderByDeptSortAsc(List<Long> deptIds);

    /**
     * 根据是否启用查询数据
     * @param enabled
     * @return
     */
    List<Dept> findByEnabled(Boolean enabled);

    List<Dept> findAllByIdInAndPidIsNotNull(Long[] deptIds);

    @Query("from Dept d where d.pid in ?1 and d.enabled = true order by d.deptSort asc ")
    List<Dept> findByPidInAndEnabled(List<Long> pid);
}
