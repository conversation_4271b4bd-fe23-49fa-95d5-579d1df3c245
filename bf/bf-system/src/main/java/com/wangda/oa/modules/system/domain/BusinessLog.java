package com.wangda.oa.modules.system.domain;

import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.PrePersist;
import javax.persistence.Table;

/**
 * 操作日志表
 * <AUTHOR>
 * @data 2022/4/6 09:26
 */
@Entity
@Getter
@Setter
@Table(name="business_log")
public class BusinessLog extends BaseEntity {

    @Id
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "操作类别")
    private String operationType;

    @ApiModelProperty(value = "业务类别")
    private String businessType;

    @ApiModelProperty(value = "业务id")
    private String businessId;

    @ApiModelProperty(value = "描述")
    private String represent;

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }

}
