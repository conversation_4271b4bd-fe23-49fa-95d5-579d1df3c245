package com.wangda.oa.modules.extension.bo;

import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午2:41
 */
@Data
public class PositionBatchBO {

    @ApiModelProperty(value = "部门id")
    @NotNull(message = "部门id不能为空")
    private Long deptId;

    @ApiModelProperty(value = "类型(head:负责人)")
    @NotBlank(message = "类型不能为空")
    private String type;

    @ApiModelProperty(value = "职位对应人员")
    private Set<SimpleUserDto> positionUsers;
}
