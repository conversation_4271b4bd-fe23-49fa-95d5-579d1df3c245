package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/24 下午7:16
 */
@Data
@Builder
@ApiModel(description="工作通知卡片消息-独立跳转")
public class WorkNotificationActionCardDlBO {

    @ApiModelProperty(value = "透出到会话列表和通知的文案，最长64个字符")
    private String title;
    @ApiModelProperty(value = "消息内容，目前不支持markdown，只能传纯文本")
    private String markdown;
    @ApiModelProperty(value = "使用独立跳转ActionCard样式时的按钮排列方式，竖直排列(0)，横向排列(1)；必须与btn_json_list同时设置")
    private String btn_orientation;
    @ApiModelProperty(value = "使用独立跳转ActionCard样式时的按钮列表；必须与btn_orientation同时设置")
    private List<BtnJsonList> btn_json_list;
    @Data
    class BtnJsonList{
        @ApiModelProperty(value = "使用独立跳转ActionCard样式时的按钮的标题，最长20个字符")
        private String title;
        @ApiModelProperty(value = "pc端打开的url.消息点击链接地址，当发送消息为小程序时支持小程序跳转链接，最长500个字符")
        private String action_pc_url;
        @ApiModelProperty(value = "移动端打开的url.消息点击链接地址，当发送消息为小程序时支持小程序跳转链接，最长500个字符")
        private String action_mobile_url;
    }
    @Tolerate
    WorkNotificationActionCardDlBO(){}
}
