package com.wangda.oa.modules.system.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wangda.boot.platform.idWorker.IdWorker;
import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

/**
 * @program: oa-mgt-server
 * @description: 用户个性化配置
 * @author: maogy
 * @create: 2021-12-03 17:10
 **/
@Entity
@Getter
@Setter
@Table(name="sys_user_settings")
public class UserSettings extends BaseEntity {

    @Id
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String userName;


    @ApiModelProperty(value = "配置key")
    private String setting_key;

    @ApiModelProperty(value = "配置value")
    @Lob
    private String setting_value;

    public void copy(UserSettings userSettings){
        BeanUtil.copyProperties(userSettings,this, CopyOptions.create().setIgnoreNullValue(true));
    }

    @PrePersist
    public void preCreateEntity() {
        setId(this.id == null ? IdWorker.generateId() : this.id);
    }

}
