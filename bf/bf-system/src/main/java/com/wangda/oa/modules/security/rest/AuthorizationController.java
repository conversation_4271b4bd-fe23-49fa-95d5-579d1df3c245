/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.security.rest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.IdUtil;
import com.baomidou.kisso.SSOHelper;
import com.baomidou.kisso.SSOToken;
import com.baomidou.kisso.Token;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousDeleteMapping;
import com.wangda.oa.annotation.rest.AnonymousGetMapping;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import com.wangda.oa.config.RsaProperties;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.service.SysUserPlatformService;
import com.wangda.oa.modules.security.config.bean.LoginCodeEnum;
import com.wangda.oa.modules.security.config.bean.LoginProperties;
import com.wangda.oa.modules.security.config.bean.SecurityProperties;
import com.wangda.oa.modules.security.helper.DingHelper;
import com.wangda.oa.modules.security.security.CASAuthToken;
import com.wangda.oa.modules.security.security.TokenProvider;
import com.wangda.oa.modules.security.service.OnlineUserService;
import com.wangda.oa.modules.security.service.dto.*;
import com.wangda.oa.modules.system.service.ChildUserService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.ChildUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.utils.CacheKey;
import com.wangda.oa.utils.RedisUtils;
import com.wangda.oa.utils.RsaUtils;
import com.wangda.oa.utils.SecurityUtils;
import com.wf.captcha.base.Captcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Jie
 * @date 2018-11-23
 * 授权、根据token获取用户详细信息
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Api(tags = "系统：系统授权接口")
public class AuthorizationController {
    private final SecurityProperties properties;
    private final RedisUtils redisUtils;
    private final OnlineUserService onlineUserService;
    private final TokenProvider tokenProvider;
    private final SysUserPlatformService sysUserPlatformService;
    private final AuthenticationManagerBuilder authenticationManagerBuilder;
    private final UserService userService;
    private final ChildUserService childUserService;
    private final PasswordEncoder passwordEncoder;

    @Resource
    private LoginProperties loginProperties;

    @Log("用户登录")
    @ApiOperation("登录授权")
    @AnonymousPostMapping(value = "/login")
    public ResponseEntity<Object> login(@Validated @RequestBody AuthUserDto authUser, HttpServletRequest request) throws Exception {
        Object frozenTimes = redisUtils.get(CacheKey.FROZEN_KEY.concat(authUser.getUsername()));
        if(Objects.nonNull(frozenTimes) && (Integer) frozenTimes >= 5) {
            throw new BadRequestException("账号被冻结" + SystemConstant.NUMBER_TEN + "分钟，请稍候再试或联系管理员");
        }

        // 密码解密
        String password = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, authUser.getPassword());
        // 查询验证码
//        String code = (String) redisUtils.get(authUser.getUuid());
        // 清除验证码
//        redisUtils.del(authUser.getUuid());
//        if (StringUtils.isBlank(code)) {
//            throw new BadRequestException("验证码不存在或已过期");
//        }
//        if (StringUtils.isBlank(authUser.getCode()) || !authUser.getCode().equalsIgnoreCase(code)) {
//            throw new BadRequestException("验证码错误");
//        }
        UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(authUser.getUsername(), password);
        Authentication authentication;
        try {
            authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
        }catch(UsernameNotFoundException e) {
            throw new BadRequestException("账号或密码不正确");
        }catch(BadCredentialsException e) {
            if(authUser.getUsername().toLowerCase().endsWith("admin")) {
                throw new BadRequestException("账号或密码不正确");
            }else {
                long failCount = redisUtils.incr(CacheKey.FROZEN_KEY.concat(authUser.getUsername()), SystemConstant.NUMBER_TEN, TimeUnit.MINUTES);
                if(failCount >= 5) {
                    throw new BadRequestException("账号被冻结" + SystemConstant.NUMBER_TEN + "分钟，请稍候再试或联系管理员");
                }else {
                    throw new BadRequestException("账号或密码已输入错误" + failCount + "次，错误5次将冻结账号");
                }
            }
        }catch(InternalAuthenticationServiceException e) {
            throw new BadRequestException("账号被冻结，请联系管理员");
        }

        SecurityContextHolder.getContext().setAuthentication(authentication);
        // 生成令牌
        String token = tokenProvider.createToken(authentication);
        final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
        // 保存在线信息
        onlineUserService.save(jwtUserDto, token, request);

        // 过滤密码
        JwtUserVo jwtUserVo = new JwtUserVo();
        BeanUtil.copyProperties(jwtUserDto, jwtUserVo, CopyOptions.create().ignoreError());

        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", properties.getTokenStartWith() + token);
            put("user", jwtUserVo);
        }};
        if(loginProperties.isSingleLogin()) {
            //踢掉之前已经登录的token
            onlineUserService.checkLoginOnUser(authUser.getUsername(), token);
        }
        return ResponseEntity.ok(authInfo);
    }

    @Log("单位账号登录")
    @ApiOperation("登录授权")
    @AnonymousPostMapping(value = "/unitLogin")
    public ResponseEntity<Object> unitLogin(@Validated @RequestBody AuthUserDto authUser, HttpServletRequest request) throws Exception {
        // 密码解密
        String password = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, authUser.getPassword());
        //判断是否为子用户
        ChildUserDto childUser = childUserService.findByName(authUser.getUsername());
        Authentication authentication;
        if(childUser != null) {
            if(passwordEncoder.matches(password, childUser.getPassword())) {
                //子用户用户名密码符合则查询主用户表
                UserDto user = userService.findByName(childUser.getDeptUsername());
                if(user != null) {
                    //主用户存在则执行原来的步骤登录
                    authUser.setUsername(user.getUsername());
                }else {
                    throw new BadCredentialsException("主账号不存在!");
                }
                // 根据用户名生成主用户令牌
                CASAuthToken authenticationToken = new CASAuthToken(authUser.getUsername());
                authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }else {
                throw new BadCredentialsException("密码错误!");
            }
        }else {
            throw new BadCredentialsException("单位账号不存在,请联系管理员新增!");
        }

        // 生成令牌
        String token = tokenProvider.createToken(authentication);
        //保存子用户username到redis
        onlineUserService.saveChildUser(childUser.getUsername(), token, request);
        final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
        // 保存在线信息
        onlineUserService.save(jwtUserDto, token, request);

        // 过滤密码
        JwtUserVo jwtUserVo = new JwtUserVo();
        BeanUtil.copyProperties(jwtUserDto, jwtUserVo, CopyOptions.create().ignoreError());

        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", properties.getTokenStartWith() + token);
            put("user", jwtUserVo);
        }};
        if(loginProperties.isSingleLogin()) {
            //踢掉之前已经登录的token
            onlineUserService.checkLoginOnUser(authUser.getUsername(), token);
        }
        return ResponseEntity.ok(authInfo);
    }

    @Log("不要验证码用户登录")
    @ApiOperation("登录授权")
    @AnonymousPostMapping(value = "/userLogin")
    public ResponseEntity<Object> userLogin(@Validated @RequestBody AuthUserDto authUser, HttpServletRequest request) {
        try {
            // 密码解密
            String password = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, authUser.getPassword());
            UsernamePasswordAuthenticationToken authenticationToken =
                    new UsernamePasswordAuthenticationToken(authUser.getUsername(), password);
            Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            // 生成令牌
            String token = tokenProvider.createToken(authentication);
            final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
            // 保存在线信息
            onlineUserService.saveAndSetTime(jwtUserDto, token, request, SystemConstant.DATA_TOKEN_OVER_DUETIME);

            // 过滤密码
            JwtUserVo jwtUserVo = new JwtUserVo();
            BeanUtil.copyProperties(jwtUserDto, jwtUserVo, CopyOptions.create().ignoreError());

            // 返回 token 与 用户信息
            Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
                put("token", properties.getTokenStartWith() + token);
                put("user", jwtUserVo);
            }};
            return ResponseEntity.ok(authInfo);
        }catch(Exception e) {
            return ResponseEntity.status(ResultCodeEnum.USER_ACCOUNT_ERROR.getCode()).body(ResultCodeEnum.USER_ACCOUNT_ERROR.getMessageCN());
        }
    }

    @ApiOperation("获取用户信息")
    @GetMapping(value = "/info")
    public ResponseEntity<Object> getUserInfo() {
        return ResponseEntity.ok(SecurityUtils.getCurrentUser());
    }

    @ApiOperation("获取验证码")
    @AnonymousGetMapping(value = "/code")
    public ResponseEntity<Object> getCode() {
        // 获取运算的结果
        Captcha captcha = loginProperties.getCaptcha();
        String uuid = properties.getCodeKey() + IdUtil.simpleUUID();
        //当验证码类型为 arithmetic时且长度 >= 2 时，captcha.text()的结果有几率为浮点型
        String captchaValue = captcha.text();
        if(captcha.getCharType() - 1 == LoginCodeEnum.arithmetic.ordinal() && captchaValue.contains(".")) {
            captchaValue = captchaValue.split("\\.")[0];
        }
        // 保存
        redisUtils.set(uuid, captchaValue, loginProperties.getLoginCode().getExpiration(), TimeUnit.MINUTES);
        // 验证码信息
        Map<String, Object> imgResult = new HashMap<String, Object>(2) {{
            put("img", captcha.toBase64());
            put("uuid", uuid);
        }};
        return ResponseEntity.ok(imgResult);
    }

    @ApiOperation("退出登录")
    @AnonymousDeleteMapping(value = "/logout")
    public ResponseEntity<Object> logout(HttpServletRequest request) {
        onlineUserService.logout(tokenProvider.getToken(request));
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "OA认证")
    @AnonymousGetMapping("/kissoAuth")
    @ResponseBody
    @CrossOrigin
    public ResponseEntity<Object> kissoAuth(HttpServletRequest request, HttpServletResponse response) {
        Token token = SSOHelper.getToken(request);
        if(null != token) {
            request.setAttribute("SSOTokenAttr", token);
        }
        SSOToken ssoToken = SSOHelper.attrToken(request);
        if(null == ssoToken) {
            return ResponseEntity.status(ResultCodeEnum.USER_NOT_LOGGED_IN.getCode()).body(ResultCodeEnum.USER_NOT_LOGGED_IN.getMessageCN());
        }

        String uid = ssoToken.getUid();
        String msg = "登录信息 ip=" + ssoToken.getIp();
        msg += "， uid=" + uid;
        log.info("cookie认证信息 msg：" + msg);

        // 去库里查找如果有生成token，如果没有则提示没有用户
//        userService.findByName(uid);

        // 生成令牌
        SysAppLoginDto sysAppLoginDto = generateOnlineToken(request, uid, SystemConstant.DATA_TOKEN_OVER_DUETIME_ONE);

        // 过滤密码
        JwtUserVo jwtUserVo = new JwtUserVo();
        BeanUtil.copyProperties(sysAppLoginDto.getUser(), jwtUserVo, CopyOptions.create().ignoreError());

        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", sysAppLoginDto.getValue());
            put("user", jwtUserVo);
        }};
        return ResponseEntity.ok(authInfo);
    }

    @Log("模拟登陆")
    @ApiOperation(value = "模拟登陆")
    @PostMapping("/simulateLogin")
    public ResponseEntity<Object> simulateLogin(@Validated @RequestBody SimulateUserDto simulateUserDto, HttpServletRequest request) {
        // 去库里查找如果有生成token，如果没有则提示没有用户
        UserDto userDto = userService.findByName(simulateUserDto.getUsername());
        if(Objects.isNull(userDto)) {
            return ResponseEntity.status(ResultCodeEnum.ILLEGAL_ARGUMENT.getCode()).body("找不到用户");
        }

        SysAppLoginDto sysAppLoginDto = generateOnlineToken(request, simulateUserDto.getUsername(), SystemConstant.DATA_TOKEN_OVER_DUETIME_ONE);

        // 过滤密码
        JwtUserVo jwtUserVo = new JwtUserVo();
        BeanUtil.copyProperties(sysAppLoginDto.getUser(), jwtUserVo, CopyOptions.create().ignoreError());

        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", sysAppLoginDto.getValue());
            put("user", jwtUserVo);
        }};

        // 登出原先用户
        onlineUserService.logout(tokenProvider.getToken(request));
        return ResponseEntity.ok(authInfo);
    }

    @AnonymousGetMapping("scanCode")
    public ResponseEntity scanCode(String code, HttpServletRequest request) {
        Long userInfo = DingHelper.getUserInfo(code);
        String username = sysUserPlatformService.queryUsernameByPlatformUserId(userInfo.toString());

        SysAppLoginDto sysAppLoginDto = generateOnlineToken(request, username, SystemConstant.DATA_TOKEN_OVER_DUETIME);

        // 过滤密码
        JwtUserVo jwtUserVo = new JwtUserVo();
        BeanUtil.copyProperties(sysAppLoginDto.getUser(), jwtUserVo, CopyOptions.create().ignoreError());

        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {{
            put("token", sysAppLoginDto.getValue());
            put("user", jwtUserVo);
        }};
        return ResponseEntity.ok(authInfo);
    }

    /**
     * 模拟登陆
     * @return
     */
    @ApiOperation(value = "App模拟登陆", notes = "App模拟登陆")
    @AnonymousPostMapping(value = "/simulationLogin")
    @Log("App模拟登陆")
    public ResultJson<SysAppLoginDto> simulationLogin(@RequestBody @Validated SimulateUserDto bo, HttpServletRequest request) {
        if(StringUtils.isEmpty(bo.getPassword())) {
            return ResultJson.generateResult("密码不能为空!", ResultCodeEnum.NULL_ARGUMENT.getCode());
        }
        // 密码解密
        try {
            String password = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, bo.getPassword());
            if(!SystemConstant.SIMULATION_PASSWORD.equals(password)) {
                //密码不匹配返回
                return ResultJson.generateResult("密码错误!", ResultCodeEnum.BUSINESS_ERROR.getCode());
            }
        }catch(Exception exception) {
            throw new BadRequestException("密码错误!");
        }
        UserDto userDto = userService.findByName(bo.getUsername());
        if(userDto == null) {
            return ResultJson.generateResult("用户不存在", ResultCodeEnum.BUSINESS_ERROR.getCode());
        }
        SysAppLoginDto sysAppLoginDto = generateOnlineToken(request, bo.getUsername(), SystemConstant.DATA_TOKEN_OVER_DUETIME_ONE);

        // 登出原先用户
        onlineUserService.logout(tokenProvider.getToken(request));
        return ResultJson.generateResult(sysAppLoginDto);
    }

    /**
     * 生成token返回值
     * @param request
     * @param username
     * @param dueTime  超时时间
     * @return
     */
    private SysAppLoginDto generateOnlineToken(HttpServletRequest request, String username, long dueTime) {
        // 生成令牌
        CASAuthToken authenticationToken = new CASAuthToken(username);
        Authentication authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        // 生成令牌
        String oaToken = tokenProvider.createToken(authentication);
        final JwtUserDto jwtUserDto = (JwtUserDto) authentication.getPrincipal();
        // 保存在线信息
        onlineUserService.saveAndSetTime(jwtUserDto, oaToken, request, dueTime);

        //返回用户信息和token
        SysAppLoginDto sysAppLoginDto = new SysAppLoginDto();
        sysAppLoginDto.setType(1);
        sysAppLoginDto.setValue(properties.getTokenStartWith() + oaToken);
        sysAppLoginDto.setUser(jwtUserDto);
        return sysAppLoginDto;
    }
}
