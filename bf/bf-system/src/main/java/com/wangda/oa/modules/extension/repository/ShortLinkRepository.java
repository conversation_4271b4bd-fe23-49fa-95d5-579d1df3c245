package com.wangda.oa.modules.extension.repository;

import com.wangda.oa.modules.extension.domain.ShortLink;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ShortLinkRepository extends JpaRepository<ShortLink, String>, JpaSpecificationExecutor<ShortLink> {

    ShortLink getByCode(String code);

    @Query(value = "select sl from ShortLink sl where sl.code = :code and (sl.timeOut > :timeOut or sl.timeOut is null)")
    ShortLink getByCodeAndTimeOutAfterOrTimeOutIsNull(String code, LocalDateTime timeOut);

    void deleteAllByIdIn(Set<Long> ids);

    @Modifying
    @Query(value = "update ShortLink sl set sl.longUrl = :newLongUrl where sl.longUrl = :longUrl")
    void updateLongUrl(String longUrl, String newLongUrl);
}
