package com.wangda.oa.modules.extension.bo.zwdd.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午8:35
 */
@Data
@ApiModel(description="我发起的对外接口")
public class TaskListBO {
    @ApiModelProperty(value = "发起人ID",required = true)
    private String userId;
    @ApiModelProperty(value = "页面大小",required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "创建时间start")
    private String createStartTime;
    @ApiModelProperty(value = "创建时间end")
    private String createEndTime;
    @ApiModelProperty(value = "页码",required = true)
    private Integer pageNo;
    @ApiModelProperty(value = "应用key")
    private String appKey;
    @ApiModelProperty(value = "完成时间start")
    private String finishStartTime;
    @ApiModelProperty(value = "完成时间end")
    private String finishEndTime;
    @ApiModelProperty(value = "关键字")
    private String keyword;
    @ApiModelProperty(value = "只有我发起的对外接口有用。状态(0:待处理,1:取消,2:完成)")
    private Integer status;

}
