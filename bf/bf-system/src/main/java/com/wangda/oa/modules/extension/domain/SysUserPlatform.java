package com.wangda.oa.modules.extension.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * <AUTHOR>
 * @date 2021/5/13 下午7:41
 */
@Data
@Entity
@Table(name="sys_user_platform",uniqueConstraints = @UniqueConstraint(columnNames = {"type", "user_name"}))
public class SysUserPlatform extends BaseDomain {

    @Column(name = "user_name")
    @ApiModelProperty(value = "用户名")
    private String userName;

    @Column(name = "type")
    @ApiModelProperty(value = "类型(zwdd:政务钉钉)")
    private String type;

    @Column(name = "platform_user_id")
    @ApiModelProperty(value = "三方用户id")
    private String platformUserId;

    @Column(name = "platform_user_name")
    @ApiModelProperty(value = "三方用户名")
    private String platformUserName;
}
