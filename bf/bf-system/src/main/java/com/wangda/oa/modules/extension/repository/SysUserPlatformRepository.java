package com.wangda.oa.modules.extension.repository;

import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/13 下午7:47
 */
public interface SysUserPlatformRepository extends JpaRepository<SysUserPlatform, Long>, JpaSpecificationExecutor<SysUserPlatform> {

    /**
     * 根据类型
     * @param type
     * @return
     */
    List<SysUserPlatform> findByType(String type);

    /**
     * 根据类型查询已绑定用户
     * @param type
     * @return
     */
    List<SysUserPlatform> findByTypeAndPlatformUserIdIsNotNull(String type);

    /**
     * 根据第三方用户id查询数据
     * @param platformUserId
     * @param type
     * @return
     */
    List<SysUserPlatform> findByPlatformUserIdAndType(String platformUserId,String type);

    /**
     * 根据用户名和type
     * @param username
     * @param type
     * @return
     */
    List<SysUserPlatform> findByUserNameAndType(String username,String type);

    /**
     * 根据用户名称和类型查询数据
     * @param username
     * @param type
     * @return
     */
    SysUserPlatform getFirstByUserNameAndType(String username,String type);

    /**
     * 根据用户名称和类型查询
     * @param usernames
     * @param type
     * @return List
     */
    List<SysUserPlatform> findByUserNameInAndType(List<String> usernames, String type);

    /**
     * 根据用户id查询数据
     * @param userId
     * @return
     */
    @Query(value = "select sp from SysUserPlatform sp left join User u on sp.userName = u.username where u.id = ?1 and sp.type = ?2")
    SysUserPlatform getByUserId(Long userId, String type);

    /**
    * Description: 根据用户名和类型查询用户数据
    * @param userName
    * @param type
    * @return: com.wangda.oa.modules.oa.domain.SysUserPlatform
    * @Date: 2021/7/2 16:42
    * @Author: maogy
    * @throws:
    */
    @Modifying
    @Query("delete from SysUserPlatform s where s.userName = :userName and s.type = :type")
    int deleteByUserNameAndType(String userName,String type);
    @Query("select u.userName from SysUserPlatform u where u.platformUserId =:platformUserId")
    String  findUsernameByPlatformUserId(String platformUserId);

    /**
     * 根据平台用户名称和类型查询数据
     * @param platformUserName
     * @param type
     * @return
     */
    SysUserPlatform getFirstByPlatformUserNameAndType(String platformUserName, String type);
}
