package com.wangda.oa.modules.extension.bo.zwdd.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午8:35
 */
@Data
@ApiModel(description="我发起的对外接口")
public class TaskCloseBO {
    @ApiModelProperty(value = "用户ID",required = true)
    private String userId;
    @ApiModelProperty(value = "实例唯一ID,新增时第三方传入的实例id,非浙政钉返回的实例id",required = true)
    private String packageUuid;
    @ApiModelProperty(value = "类型(0或者空:传入的实例id为第三方的实例id,为1则为浙政钉返回的实例id)")
    private Integer type;

    @ApiModelProperty(value = "创建时间", hidden=true)
    private Date createDate = new Date();
}
