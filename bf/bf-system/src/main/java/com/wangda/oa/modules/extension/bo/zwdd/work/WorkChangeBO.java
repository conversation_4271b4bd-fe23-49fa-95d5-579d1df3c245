package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午5:16
 */
@Data
@ApiModel(description="工作通知")
public class WorkChangeBO {
    @ApiModelProperty(value = "文本消息：text, 链接消息：link, OA消息：oa, 卡片消息：action_card",required = true)
    private String msgType;
    @ApiModelProperty(value = "【选填】消息自动过期时间（日程类消息使用，其他场景勿用）")
    private String expireTime;
    @ApiModelProperty(value = "接收人用户ID(accountId)， 多个人时使用半角逗号分隔",required = true)
    private String accountIds;
    @ApiModelProperty(value = "业务消息id",required = true)
    private String bizMsgId;
    @ApiModelProperty(value = "必填,json对象 必须 {\"msgtype\":\"text\",\"text\":{\"content\":\"消息内容\"}} 消息内容，目前支持：文本消息：text, 链接消息：link, OA消息：oa, 卡片消息：action_card。最长不超过2048个字节\n")
    private String jsonContent;
}
