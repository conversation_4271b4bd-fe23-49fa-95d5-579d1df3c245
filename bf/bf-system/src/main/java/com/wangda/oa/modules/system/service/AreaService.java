package com.wangda.oa.modules.system.service;

import com.wangda.oa.modules.system.service.dto.AreaQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Description: 操作管理员权限
 * @Author:caiyy
 * @Date: 2021/09/30
 */
public interface AreaService {

    /**
     * 查询json格式的地区信息
     * @param code 地区行政编码
     * @param rank 层级
     * @return
     */
    public LinkedHashMap<String, Object> getAreaJson(String code, Integer rank);

    /**
     * 查询json格式的地区信息（根据市级名称查县）
     *
     * @return
     */
    public LinkedHashMap<String, Object> getCityDistrictToJson(String code);

    /**
     * 调用高德地图API取最新数据
     *
     * @return
     */
    public void reloadArea();

    /**
     * 查询地区信息
     *
     * @param criteria
     * @param pageable
     * @return
     */
    public Map<String, Object> query(AreaQueryCriteria criteria, Pageable pageable);
}
