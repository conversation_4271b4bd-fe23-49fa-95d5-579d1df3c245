package com.wangda.oa.modules.system.utils;

import com.wangda.oa.modules.security.config.bean.SecurityProperties;
import com.wangda.oa.utils.RedisUtils;
import com.wangda.oa.utils.RequestHolder;
import com.wangda.oa.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @data 2022/4/1 11:09
 */
@Component
@Slf4j
public class ChildUserUtils {

    @Autowired
    private RedisUtils redisUtilsAutowired;

    @Autowired
    private SecurityProperties propertiesAutowired;

    private static RedisUtils redisUtils;

    private static SecurityProperties properties;

    @PostConstruct
    public void init() {
        this.redisUtils = redisUtilsAutowired;
        this.properties = propertiesAutowired;
    }

    /**
     * 获取当前登录的用户名
     * @return username
     */
    public static String getCurrentChildUsername() {
        Object obj=redisUtils.get(resolveToken(RequestHolder.getHttpServletRequest()));
        if(obj!=null){
            return String.valueOf(obj);
        }
        return SecurityUtils.getCurrentUsername();
    }

    /**
     * 初步检测Token
     *
     * @param request /
     * @return /
     */
    private static String resolveToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(properties.getHeader());
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(properties.getTokenStartWith())) {
            // 去掉令牌前缀
            return bearerToken.replace(properties.getTokenStartWith(), "");
        } else {
            log.debug("非法Token：{}", bearerToken);
        }
        return null;
    }
}
