package com.wangda.oa.modules.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.config.AutonaviConfig;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.mnt.constant.AreaConstant;
import com.wangda.oa.modules.system.domain.Area;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.repository.AreaRepository;
import com.wangda.oa.modules.system.service.AreaService;
import com.wangda.oa.modules.system.service.dto.AreaQueryCriteria;
import com.wangda.oa.modules.system.service.mapstruct.AreaMapper;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @Description: 区域信息管理
 * @Author: caiyy
 * @Date: 2021/10/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AreaServiceImpl implements AreaService {

    private final AreaRepository areaRepository;

    private final AreaMapper areaMapper;

    private static final String CHILDREN = "districts";

    private final AutonaviConfig autonaviConfig;

    private final RestTemplate restTemplate;

    private final RedisUtils redisUtils;
    private final static ReentrantLock lock = new ReentrantLock();
    private final static ConcurrentHashMap<String, Map<String, Object>> cache = new ConcurrentHashMap<>(1);
    private final static Condition empty = lock.newCondition();
    /**
     * area数据没有任何线程在初始化
     */
    private volatile int initialize = 0;

    @Override
    public LinkedHashMap<String, Object> getAreaJson(String code, Integer rank) {
        if (rank == null) {
            rank = 0;
        }
        LinkedHashMap<String, Object> provinceMap = new LinkedHashMap<String, Object>();
        LinkedHashMap<String, Object> cityMap = new LinkedHashMap<String, Object>();
        Map<String, Object> totalMap;
        lock.lock();
        try {
            if (initialize == 1) {
                empty.await();
            }
            totalMap = cache.get(AreaConstant.AREA);

            if (totalMap == null && initialize == 0) {
                initialize = 1;
                // 查询数据库
                List<Area> all = areaRepository.findAll();
                Map<String, Object> citymap = new HashMap<>();
                Map<String, Object> levelMap = new HashMap<>();
                Map<Long, Object> parentIdMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(all)) {
                    all.stream().forEach(area -> {
                        String codeArea = area.getCodeArea();
                        Long parentId = area.getParentCode();
                        String level = area.getLevel();
                        ArrayList list = null;
                        ArrayList levelList = null;
                        if (parentIdMap.get(parentId) != null) {
                            list = (ArrayList) parentIdMap.get(parentId);
                        } else {
                            list = new ArrayList();
                        }
                        if (levelMap.get(level) != null) {
                            levelList = (ArrayList) levelMap.get(level);
                        } else {
                            levelList = new ArrayList();
                        }
                        list.add(area);
                        levelList.add(area);
                        citymap.put(codeArea, area);
                        parentIdMap.put(parentId, list);
                        levelMap.put(level, levelList);
                    });
                }
                totalMap = new HashMap<>();
                totalMap.put(AreaConstant.CODE_AREA, citymap);
                totalMap.put(AreaConstant.PARENT_ID_AREA, parentIdMap);
                totalMap.put(AreaConstant.LEVEL_AREA, levelMap);
                cache.put(AreaConstant.AREA, totalMap);
                // 初始化数据完成
                initialize = 2;
                empty.signalAll();
            }
        } catch (Exception e) {
            throw new BadRequestException("发生错误！");
        } finally {
            lock.unlock();
        }
        // 查询省级
        Map<String, Object> levelMap = (Map<String, Object>) totalMap.get(AreaConstant.LEVEL_AREA);
        List<Area> provinceAreas = (List<Area>) levelMap.get("province");
        // 放入省级数据
        putData(provinceAreas, provinceMap);
        if (!ObjectUtils.isEmpty(code)) {
            Map<String, Object> areaMap = (Map<String, Object>) totalMap.get(AreaConstant.CODE_AREA);
            Map<Long, Object> parentIdMap = (Map<Long, Object>) totalMap.get(AreaConstant.PARENT_ID_AREA);

            Area area = (Area) areaMap.get(code);
            if ("province".equals(area.getLevel())) {
                // 查询市级
                List<Area> cityAreas = (List<Area>) parentIdMap.get(area.getId());
                // 放入市级数据
                putData(cityAreas, cityMap);
                if (rank == 2) {
                    List<LinkedHashMap<String, Object>> list = new ArrayList<>();
                    for (Object object : cityMap.values()) {
                        LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) object;
                        LinkedHashMap<String, Object> temp = new LinkedHashMap<>();
                        temp.put("center", map.get("center"));
                        temp.put("children", map.get("children"));
                        temp.put("cityCode", map.get("cityCode"));
                        temp.put("codeArea", map.get("codeArea"));
                        temp.put("id", map.get("id"));
                        temp.put("level", map.get("level"));
                        temp.put("name", map.get("name"));
                        temp.put("parentCode", map.get("parentCode"));
                        list.add(temp);
                    }
                    // 把市级数据放入省级
                    putChildrenForProvince(list, provinceMap);
                    return provinceMap;
                }
                return cityMap;
            } else if ("city".equals(area.getLevel())) {
                LinkedHashMap<String, Object> districtMap = new LinkedHashMap<String, Object>();
                // 查询县级以及以下所有数据
                List<Area> districtAreas = (List<Area>) parentIdMap.get(area.getId());
                // 把县级数据放入市级
                putData(districtAreas, districtMap);
                return districtMap;
            }
        } else {
            List<Area> cityAreas = new ArrayList<>();
            List<LinkedHashMap<String, Object>> list = new ArrayList<>();
            switch (rank) {
                case 1:
                    return provinceMap;
                case 2:
                    // 查询市级
                    cityAreas = (List<Area>) levelMap.get("city");
                    // 放入市级数据
                    putData(cityAreas, cityMap);
                    list = new ArrayList<>();
                    for (Object object : cityMap.values()) {
                        LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) object;
                        LinkedHashMap<String, Object> temp = new LinkedHashMap<>();
                        temp.put("center", map.get("center"));
                        temp.put("children", map.get("children"));
                        temp.put("cityCode", map.get("cityCode"));
                        temp.put("codeArea", map.get("codeArea"));
                        temp.put("id", map.get("id"));
                        temp.put("level", map.get("level"));
                        temp.put("name", map.get("name"));
                        temp.put("parentCode", map.get("parentCode"));
                        list.add(temp);
                    }
                    // 把市级数据放入省级
                    putChildrenForProvince(list, provinceMap);
                    return provinceMap;
                case 3:
                    // 查询市级
                    cityAreas = (List<Area>) levelMap.get("city");
                    // 放入市级数据
                    putData(cityAreas, cityMap);
                    // 查询县级
                    List<Area> districtAreas = (List<Area>) levelMap.get("district");
                    // 把县级数据放入市级
                    putChildren(districtAreas, cityMap);
                    list = new ArrayList<>();
                    for (Object object : cityMap.values()) {
                        LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) object;
                        LinkedHashMap<String, Object> temp = new LinkedHashMap<>();
                        temp.put("center", map.get("center"));
                        temp.put("children", map.get("children"));
                        temp.put("cityCode", map.get("cityCode"));
                        temp.put("codeArea", map.get("codeArea"));
                        temp.put("id", map.get("id"));
                        temp.put("level", map.get("level"));
                        temp.put("name", map.get("name"));
                        temp.put("parentCode", map.get("parentCode"));
                        list.add(temp);
                    }
                    // 把市级数据放入省级
                    putChildrenForProvince(list, provinceMap);
                    return provinceMap;
                default:
                    return provinceMap;
            }
        }
        return provinceMap;
    }

    @Override
    public LinkedHashMap<String, Object> getCityDistrictToJson(String code) {
        LinkedHashMap<String, Object> cityMap = new LinkedHashMap<String, Object>();
        // 查询市级
        List<Area> cityAreas = areaRepository.findByCodeAreaAndLevel(code, "city");
        if (!CollectionUtils.isEmpty(cityAreas)) {
            // 查询县级
            List<Area> districtAreas = areaRepository.findByParentCode(cityAreas.get(0).getId());
            // 放入市级数据
            putData(cityAreas, cityMap);
            // 把县级数据放入市级
            putChildren(districtAreas, cityMap);
        }

        return cityMap;
    }

    @Override
    @Transactional
    public void reloadArea() {
        if (StringUtils.isBlank(autonaviConfig.getAreaUrl())) {
            throw new CustomException("请先配置高德获取区域相关参数");
        }
        HttpHeaders headers = new HttpHeaders();
        //设置为form方式
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, Object> map = new LinkedMultiValueMap();
        map.put("key", Collections.singletonList(autonaviConfig.getKey()));
        map.put("subdistrict", Collections.singletonList(autonaviConfig.getSubDistrict()));
        HttpEntity<MultiValueMap<String, Object>> requestBody = new HttpEntity<>(map,
                headers);
        ResponseEntity<String> response = ResponseEntity.ok("");
        try {
            response = restTemplate.postForEntity(autonaviConfig.getAreaUrl(), requestBody, String.class);
        } catch (Exception e) {
            log.warn("发送失败，requestBody:" + requestBody + " url:" + autonaviConfig.getAreaUrl());
        }

        //String result = SendHttpUtil.getResponse(url);
        JSONObject jo = (JSONObject) JSON.parse(response.getBody());
        // 实际数据（除去返回码，返回状态等信息）
        JSONArray allArea = (JSONArray) jo.get(CHILDREN);
        List<Area> areaList = new ArrayList<>();
        // 最外层为中华人民共和国
        JSONObject country = (JSONObject) allArea.get(0);
        areaList.add(JsonToArea(country, Long.valueOf(1), null));
        //省级
        JSONArray provinceArray = country.getJSONArray(CHILDREN);
        Iterator iterator = provinceArray.iterator();
        //省级ID
        Long id = Long.valueOf(100000);
        while (iterator.hasNext()) {
            //第一层为省
            JSONObject province = (JSONObject) iterator.next();
            areaList.add(JsonToArea(province, id, Long.valueOf(1)));
            //市级
            JSONArray cityArray = province.getJSONArray(CHILDREN);
            Iterator cityIterator = cityArray.iterator();
            //市级ID
            Long cityCount = Long.valueOf(1000);
            while (cityIterator.hasNext()) {
                //第二层为市
                JSONObject city = (JSONObject) cityIterator.next();
                areaList.add(JsonToArea(city, cityCount + id, id));
                //县级
                JSONArray districtArray = city.getJSONArray(CHILDREN);
                Iterator districtIterator = districtArray.iterator();
                //县级ID
                Long districtCount = Long.valueOf(1);
                while (districtIterator.hasNext()) {
                    //第三层为县
                    JSONObject district = (JSONObject) districtIterator.next();
                    areaList.add(JsonToArea(district, id + cityCount + districtCount, id + cityCount));
                    districtCount += Long.valueOf(1);
                }
                cityCount += Long.valueOf(1000);
            }
            id += Long.valueOf(100000);
        }
        // 先清空原有表的数据
        areaRepository.deleteAllInBatch();
        // 插入新的数据
        if (!CollectionUtils.isEmpty(areaList)) {
            int count = areaList.size();
            int index = 0;
            // 分批次插入
            while (count >= 200) {
                areaRepository.saveAll(areaList.subList(index * 200, (index + 1) * 200));
                index++;
                count = count - 200;
            }
            if (count > 0) {
                areaRepository.saveAll(areaList.subList(areaList.size() - count, areaList.size()));
            }
        }
        // 更新完数据后存入redis
        // 行政编码为key存放所有信息
        List<Area> all = areaRepository.findAll();
        Map<String, Object> cityMap = new HashMap<>();
        Map<String, Object> levelMap = new HashMap<>();
        Map<Long, Object> parentIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(all)) {
            for (Area area : all) {
                String codeArea = area.getCodeArea();
                Long parentId = area.getParentCode();
                String level = area.getLevel();
                ArrayList list = null;
                ArrayList levelList = null;
                if (parentIdMap.get(parentId) != null) {
                    list = (ArrayList) parentIdMap.get(parentId);
                } else {
                    list = new ArrayList();
                }
                if (levelMap.get(level) != null) {
                    levelList = (ArrayList) levelMap.get(level);
                } else {
                    levelList = new ArrayList();
                }
                list.add(area);
                levelList.add(area);
                cityMap.put(codeArea, area);
                parentIdMap.put(parentId, list);
                levelMap.put(level, levelList);
            }
        }
        Map<String, Object> redisMap = new HashMap<>();
        redisMap.put(AreaConstant.CODE_AREA, cityMap);
        redisMap.put(AreaConstant.PARENT_ID_AREA, parentIdMap);
        redisMap.put(AreaConstant.LEVEL_AREA, levelMap);
        redisUtils.set(AreaConstant.AREA, redisMap);
    }

    @Override
    public Map<String, Object> query(AreaQueryCriteria criteria, Pageable pageable) {
        Page<Area> page = areaRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(areaMapper::toDto).getContent(), page.getTotalElements());
    }

    private Area JsonToArea(JSONObject jsonObject, Long id, Long parentId) {
        Area area = new Area();
        area.setCodeArea((String) jsonObject.get("adcode"));
        String level = (String) jsonObject.get("level");
        area.setLevel(level);
        if (level.equals("country") || level.equals("province")) {
            // 国家和省没有城市编码
            area.setCityCode(null);
        } else {
            area.setCityCode((String) jsonObject.get("citycode"));
        }
        area.setCenter((String) jsonObject.get("center"));
        area.setName((String) jsonObject.get("name"));
        area.setId(id);
        area.setParentCode(parentId);
        return area;
    }

    /**
     * 用于生成linkedHashMap
     *
     * @param area 地区
     * @return
     */
    private LinkedHashMap<String, Object> putLinkMap(Area area) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<String, Object>();
        map.put("id", area.getId());
        map.put("center", area.getCenter());
        map.put("cityCode", area.getCityCode());
        map.put("codeArea", area.getCodeArea());
        map.put("level", area.getLevel());
        map.put("name", area.getName());
        map.put("parentCode", area.getParentCode());
        return map;
    }

    /**
     * 把地区数据放入linkedHashMap
     *
     * @param areas    某个等级的地区（省，市，县）
     * @param levelMap 某个等级的map
     */
    private void putData(List<Area> areas, LinkedHashMap<String, Object> levelMap) {
        if (!CollectionUtils.isEmpty(areas)) {
            for (Area area : areas) {
                LinkedHashMap<String, Object> map = putLinkMap(area);
                map.put("children", new ArrayList<>());
                levelMap.put(String.valueOf(map.get("id")), map);
            }
        }
    }

    /**
     * 放入某个等级的子节点
     *
     * @param areas    某个等级的地区（县->市）
     * @param levelMap 某个等级的map
     */
    private void putChildren(List<Area> areas, LinkedHashMap<String, Object> levelMap) {
        if (!CollectionUtils.isEmpty(areas)) {
            for (Area area : areas) {
                if (levelMap.containsKey(String.valueOf(area.getParentCode()))) {
                    LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) levelMap.get(String.valueOf(area.getParentCode()));
                    ArrayList children = (ArrayList) map.get("children");
                    LinkedHashMap<String, Object> areaMap = putLinkMap(area);
                    children.add(areaMap);
                }
            }
        }
    }

    /**
     * 放入某个等级的子节点
     *
     * @param areas    某个等级的地区（市->省）
     * @param levelMap 某个等级的map
     */
    private void putChildrenForProvince(List<LinkedHashMap<String, Object>> areas, LinkedHashMap<String, Object> levelMap) {
        if (!CollectionUtils.isEmpty(areas)) {
            for (LinkedHashMap<String, Object> area : areas) {
                if (levelMap.containsKey(String.valueOf(area.get("parentCode")))) {
                    LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) levelMap.get(String.valueOf(area.get("parentCode")));
                    ArrayList children = (ArrayList) map.get("children");
                    children.add(area);
                }
            }
        }
    }
}
