package com.wangda.oa.modules.extension.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午3:08
 */
@Getter
@ToString
@JSONType(serializeEnumAsJavaBean = true)
public enum PositionEnum {
    //负责人
    HEAD("head","处室负责人"),
    LEADERSHIP("leadership","分管领导"),
    HALL_LEADERSHIP("hallLeadership","厅领导"),
    UNIT_LEADERSHIP("unitLeadership","单位领导"),
    OFFICE_STAFF("officestaff","内勤");

    private String name;
    private String value;

    public String getValue() {
        return this.value;
    }

    public String getName() {
        return this.name;
    }

    PositionEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static PositionEnum getEnumByValue(String value) {
        for (PositionEnum e : PositionEnum.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

    public static List<Map<String,Object>> getAllInfo() {
        List<Map<String,Object>> list=new ArrayList<>();
        for (PositionEnum positionEnum : PositionEnum.values()) {
            Map<String,Object> map=new HashMap<>();
            map.put("name",positionEnum.getName());
            map.put("value",positionEnum.getValue());
            list.add(map);
        }
        return list;
    }

}
