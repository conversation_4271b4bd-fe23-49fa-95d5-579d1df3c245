package com.wangda.oa.modules.system.repository;

import com.wangda.oa.modules.system.domain.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2021/8/4 上午10:38
 **/
public interface UserRoleRepository extends JpaRepository<UserRole, Long>, JpaSpecificationExecutor<UserRole> {

    @Transactional
    void deleteByRoleId(Long roleId);

    @Transactional
    void deleteByRoleIdAndUserId(Long roleId,Long userId);
}
