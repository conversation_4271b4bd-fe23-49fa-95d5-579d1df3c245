package com.wangda.oa.modules.security.helper.response.base;

import com.wangda.oa.modules.security.helper.response.data.abs.ZwddData;

public class ZwddContent <T extends ZwddData> {
    private T data;
    private boolean success;
    private String requestId;
    private String responseMessage;
    private String responseCode;
    private String bizErrorCode;

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getBizErrorCode() {
        return bizErrorCode;
    }

    public void setBizErrorCode(String bizErrorCode) {
        this.bizErrorCode = bizErrorCode;
    }
}
