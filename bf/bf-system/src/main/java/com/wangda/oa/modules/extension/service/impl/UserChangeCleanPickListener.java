package com.wangda.oa.modules.extension.service.impl;

import com.wangda.oa.modules.system.service.event.DeptChangeEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class UserChangeCleanPickListener implements ApplicationListener<DeptChangeEvent> {
    //当用户发生变化清除掉缓存
    @Override
    public void onApplicationEvent(DeptChangeEvent deptChangeEvent) {
        UserDeptCache.cleanCache();
    }
}
