package com.wangda.oa.modules.extension.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
public class ShortLink extends BaseDomain {

    @ApiModelProperty(value = "编号")
    @Column(unique = true, length = 64, nullable = false)
    private String code;

    @ApiModelProperty(value = "长链接")
    @Column(nullable = false)
    @NotBlank
    private String longUrl;

    @ApiModelProperty(value = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "过期时间")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private LocalDateTime timeOut;

    @ApiModelProperty(value = "备注")
    private String remark;
}
