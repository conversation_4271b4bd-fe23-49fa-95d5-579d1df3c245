package com.wangda.oa.modules.extension.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午2:41
 */
@Data
public class PositionBO{

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "部门id")
    @NotNull(message = "部门id不能为空")
    private Long deptId;

    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    @NotNull(message = "用户姓名不能为空")
    private String userName;

    @ApiModelProperty(value = "用户名")
    @NotNull(message = "用户名不能为空")
    private String name;
    @ApiModelProperty(value = "类型(head:负责人)")
    @NotBlank(message = "类型不能为空")
    private String type;
}
