package com.wangda.oa.modules.extension.dto.org;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/21 下午6:26
 */
@Data
public class OrgListTableDto {
    @ApiModelProperty(value = "部门id")
    private Long id;
    @ApiModelProperty(value = "部门名称")
    private String nickName;
    @ApiModelProperty(value = "序号")
    private Integer sort;
    @ApiModelProperty(value = "所属人")
    private List<Map<String,Object>> highest=new ArrayList<>();
    @ApiModelProperty(value = "所属组织")
    private List<OrgListTableDto> children;
}
