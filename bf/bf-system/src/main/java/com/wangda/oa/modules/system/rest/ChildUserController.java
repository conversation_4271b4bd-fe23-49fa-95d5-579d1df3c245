/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.rest;

import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.config.RsaProperties;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.service.SysUserPlatformService;
import com.wangda.oa.modules.system.domain.ChildUser;
import com.wangda.oa.modules.system.domain.vo.ChildUserVO;
import com.wangda.oa.modules.system.domain.vo.UserPassVo;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.ChildUserService;
import com.wangda.oa.modules.system.service.dto.ChildUserDto;
import com.wangda.oa.modules.system.service.dto.ChildUserQueryCriteria;
import com.wangda.oa.modules.system.service.dto.UserQueryCriteria;
import com.wangda.oa.utils.RsaUtils;
import com.wangda.oa.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @data 2022/3/31 16:11
 */
@Api(tags = "系统：单位账号管理")
@RestController
@RequestMapping("/api/childUser")
@RequiredArgsConstructor
public class ChildUserController {

    private final PasswordEncoder passwordEncoder;
    private final ChildUserService userService;
    private final SysUserPlatformService sysUserPlatformService;
    private final ZwddProperties zwddProperties;

    @ApiOperation("查询用户")
    @GetMapping
    //@PreAuthorize("@el.check('childUsers:list')")
    public ResponseEntity<Object> query(ChildUserQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @Log("新增用户")
    @ApiOperation("新增用户")
    @PostMapping
    //@PreAuthorize("@el.check('childUsers:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody ChildUser resources) {
        if(StringUtils.isBlank(resources.getDeptUsername())) {
            throw new BadRequestException("请先选择左侧树下的单位账号！");
        }
        // 默认密码 Aa123456
        resources.setPassword(passwordEncoder.encode("Aa123456"));
        userService.create(resources);
        //新增第三方用户
        if(resources.getPlatformUserId() != null || resources.getPlatformUserName() != null) {
            SysUserPlatform sysUserPlatform = new SysUserPlatform();
            sysUserPlatform.setPlatformUserId(resources.getPlatformUserId());
            sysUserPlatform.setUserName(resources.getUsername());
            sysUserPlatform.setPlatformUserName(StringUtils.isNotEmpty(resources.getPlatformUserName())? resources.getPlatformUserName(): resources.getNickName());
            sysUserPlatform.setType(zwddProperties.getType());
            sysUserPlatformService.add(sysUserPlatform);
        }
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @Log("修改用户")
    @ApiOperation("修改用户")
    @PutMapping
    //@PreAuthorize("@el.check('childUsers:edit')")
    public ResponseEntity<Object> update(@Validated(ChildUser.Update.class) @RequestBody ChildUser resources) throws Exception {
        userService.update(resources);
        //修改绑定浙政钉用户信息
        if(resources.getPlatformUserId() != null || resources.getPlatformUserName() != null) {
            SysUserPlatform sysUserPlatform = new SysUserPlatform();
            sysUserPlatform.setType(zwddProperties.getType());
            sysUserPlatform.setUserName(resources.getUsername());
            sysUserPlatform.setPlatformUserName(StringUtils.isNotEmpty(resources.getPlatformUserName())? resources.getPlatformUserName(): resources.getNickName());
            sysUserPlatform.setPlatformUserId(resources.getPlatformUserId());
            sysUserPlatformService.update(sysUserPlatform);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除用户")
    @ApiOperation("删除用户")
    @DeleteMapping
    //@PreAuthorize("@el.check('childUsers:del')")
    public ResponseEntity<Object> delete(@RequestBody Set<Long> ids) {
        userService.delete(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 查询对应职位部门表格数据
     * @return
     */
    @ApiOperation(value = "查询对应单位账号下的子用户", notes = "查询对应单位账号下的子用户")
    @RequestMapping(value = "/getChildUserListByPid", method = RequestMethod.POST)
    @AnonymousAccess
    public ResultJson<List<ChildUserDto>> getChildUserListByPid(@RequestBody @Validated ChildUserVO vo) {
        return userService.getChildUserListByPid(vo);
    }

    @Log("导出用户数据")
    @ApiOperation("导出用户数据")
    @GetMapping(value = "/download")
    //@PreAuthorize("@el.check('childUsers:list')")
    public void download(HttpServletResponse response, UserQueryCriteria criteria) throws IOException {
        userService.download(userService.queryAll(criteria), response);
    }

    @ApiOperation("管理员修改密码")
    @PostMapping(value = "/adminUpdatePass")
    public ResponseEntity<Object> adminUpdatePass(@RequestBody UserPassVo passVo) throws Exception {
        if(StringUtils.isEmpty(passVo.getUsername())) {
            throw new CustomException("修改用户密码参数错误");
        }
        String newPass = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, passVo.getNewPass());
        ChildUserDto user = userService.findByName(passVo.getUsername());
        userService.updatePass(user.getUsername(), passwordEncoder.encode(newPass));
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("新增关联账号")
    @ApiOperation("新增关联账号")
    @PostMapping("/createAssociatedAccount")
    //@PreAuthorize("@el.check('childUsers:add')")
    public ResponseEntity<Object> createAssociatedAccount(@Validated @RequestBody ChildUserDto resources) {
        if(StringUtils.isBlank(resources.getDeptUsername())) {
            throw new BadRequestException("请先选择左侧树下的单位账号！");
        }
        userService.createAssociatedAccount(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

}
