/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.rest;

import com.wangda.oa.annotation.Log;
import com.wangda.oa.modules.system.domain.BusinessLog;
import com.wangda.oa.modules.system.service.BusinessLogService;
import com.wangda.oa.modules.system.service.dto.BusinessLogCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @data 2022/4/6 11:11
 */
@Api(tags = "系统：操作日志管理")
@RestController
@RequestMapping("/api/businessLog")
@RequiredArgsConstructor
public class BusinessLogController {

    private final BusinessLogService businessLogService;

    @Log("查询日志")
    @ApiOperation("查询日志")
    @GetMapping
    //@PreAuthorize("@el.check('businessLog:list')")
    public ResponseEntity<Object> query(BusinessLogCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(businessLogService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @Log("新增日志")
    @ApiOperation("新增日志")
    @PostMapping
    //@PreAuthorize("@el.check('businessLog:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody BusinessLog resources){
        businessLogService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

}
