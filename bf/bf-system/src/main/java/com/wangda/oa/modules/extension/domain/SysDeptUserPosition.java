package com.wangda.oa.modules.extension.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wangda.boot.platform.base.BaseDomain;
import com.wangda.oa.modules.extension.bo.PositionBatchBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午2:41
 */
@Data
@Entity
@Table(name="sys_dept_user_position")
public class SysDeptUserPosition extends BaseDomain {

    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @Column(name = "user_id")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    @Column(name = "user_name")
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @Column(name = "name")
    @ApiModelProperty(value = "用户名")
    private String name;

    @Column(name = "type")
    @ApiModelProperty(value = "类型")
    private String type;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    public void copy(PositionBatchBO source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
