package com.wangda.oa.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/12
 * @description 高德获取地区数据参数配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "autonavi")
public class AutonaviConfig {

    private String areaUrl;

    private String key;

    private String subDistrict;
}
