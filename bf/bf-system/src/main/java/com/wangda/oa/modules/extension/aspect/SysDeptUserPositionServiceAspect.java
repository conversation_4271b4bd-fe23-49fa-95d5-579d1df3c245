package com.wangda.oa.modules.extension.aspect;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.modules.extension.bo.CommonControlsBO;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;

@Aspect
@Component
@RequiredArgsConstructor
public class SysDeptUserPositionServiceAspect {
    private final RedisUtils redisUtils;

    @Around("execution(* com.wangda.oa.modules.extension.service.SysDeptUserPositionService.getOrgList(..))")
    public Object cacheUser(ProceedingJoinPoint joinPoint) throws Throwable {
        final String prefix = "position:";
        Object[] args = joinPoint.getArgs();
        CommonControlsBO arg = (CommonControlsBO) args[0];
        // 推荐的不走缓存其他的都拦截
        if (arg.getType() == SystemConstant.COMMON_CONTROLS_TYPE_RECOMMENDED) {
            return joinPoint.proceed();
        }
        String md5 = getMd5(arg);
        String key = prefix + md5;
        Object o = redisUtils.get(key);
        if (o != null) {
            return o;
        }
        // 避免集中过期造成redis删除压力
        int expire = RandomUtil.getRandom().nextInt(400, 600);
        Object proceed = joinPoint.proceed();
        redisUtils.setNx(key, proceed, expire);
        return proceed;

    }

    String getMd5(CommonControlsBO bo) {
        String md5Source = JSONObject.toJSONString(bo);
        return DigestUtils.md5DigestAsHex(md5Source.getBytes(StandardCharsets.UTF_8));
    }
}
