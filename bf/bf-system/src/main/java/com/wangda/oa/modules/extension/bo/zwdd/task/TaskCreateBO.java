package com.wangda.oa.modules.extension.bo.zwdd.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午8:35
 */
@Data
@Builder
@ApiModel(description="创建待办")
public class TaskCreateBO {
    @ApiModelProperty(value = "待办人信息，先不传。示例：{\"imgId\":\"\",\"name\":\"张三\"}不传的话待办人缺少名称与头像信息")
    private String assigneeInfo;
    @ApiModelProperty(value = "标题",required = true)
    private String subject;
    @ApiModelProperty(value = "创建人信息，先不传。示例：{\"imgId\":\"\",\"name\":\"张三\"}不传的话待办人缺少名称与头像信息")
    private String creatorInfo;
    @ApiModelProperty(value = "创建人ID",required = true)
    private String creatorId;
    @ApiModelProperty(value = "业务系统自定义ID，业务系统保证唯一，否则不保证幂等",required = true)
    private String bizTaskId;
    @ApiModelProperty(value = "移动端详情URL",required = true)
    private String mobileUrl;
    @ApiModelProperty(value = "待办人ID",required = true)
    private String assigneeId;
    @ApiModelProperty(value = "详情URL",required = true)
    private String url;
    @ApiModelProperty(value = "实例唯一ID，若需要多个任务关联到同一个实例下时则必传，如:整个流程发起多次待办但在发起人那只需要一条时",required = true)
    private String packageUuid;
    @ApiModelProperty(value = "是否发送动态卡片(true:会发送工作卡片通知,false:不会发送工作卡片通知)")
    private Boolean sendDynamicCard;

    @ApiModelProperty(value = "创建时间", hidden=true)
    @Builder.Default
    private Date createDate = new Date();

    @Tolerate
    TaskCreateBO(){}

    @Override
    public boolean equals(Object object) {
        if (this == object) return true;
        if (!(object instanceof TaskCreateBO)) return false;
        TaskCreateBO that = (TaskCreateBO) object;
        return Objects.equals(assigneeInfo, that.assigneeInfo) &&
                Objects.equals(subject, that.subject) &&
                Objects.equals(creatorInfo, that.creatorInfo) &&
                Objects.equals(creatorId, that.creatorId) &&
                Objects.equals(bizTaskId, that.bizTaskId) &&
                Objects.equals(mobileUrl, that.mobileUrl) &&
                Objects.equals(assigneeId, that.assigneeId) &&
                Objects.equals(url, that.url) &&
                Objects.equals(packageUuid, that.packageUuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(assigneeInfo, subject, creatorInfo, creatorId, bizTaskId, mobileUrl, assigneeId, url, packageUuid);
    }
}
