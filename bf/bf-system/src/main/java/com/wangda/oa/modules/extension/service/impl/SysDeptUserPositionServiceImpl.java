package com.wangda.oa.modules.extension.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.bo.*;
import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.domain.WdGroup;
import com.wangda.oa.modules.extension.domain.WdLine;
import com.wangda.oa.modules.extension.dto.GroupUserQueryCriteria;
import com.wangda.oa.modules.extension.dto.UserDataDto;
import com.wangda.oa.modules.extension.dto.org.*;
import com.wangda.oa.modules.extension.enums.PositionEnum;
import com.wangda.oa.modules.extension.enums.UserSelectorTypeEnum;
import com.wangda.oa.modules.extension.mapper.WdLineMapper;
import com.wangda.oa.modules.extension.mapper.WdSysOptionMapper;
import com.wangda.oa.modules.extension.repository.DeptUserPositionRepository;
import com.wangda.oa.modules.extension.repository.WdGroupRepository;
import com.wangda.oa.modules.extension.repository.WdLineRepository;
import com.wangda.oa.modules.extension.service.SysDeptUserPositionService;
import com.wangda.oa.modules.system.domain.Dept;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.DeptRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.repository.query.UserDeptField;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.DeptDto;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.utils.SystemConstant;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午3:03
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SysDeptUserPositionServiceImpl implements SysDeptUserPositionService {

    private final DeptUserPositionRepository deptUserPositionRepository;

    private final DeptRepository deptRepository;

    private final WdSysOptionMapper wdSysOptionMapper;

    private final WdLineRepository wdLineRepository;

    private final WdLineMapper wdLineMapper;

    private final RedisTemplate redisTemplate;

    private final UserRepository userRepository;

    private final StringRedisTemplate stringRedisTemplate;

    private final WdGroupRepository wdGroupRepository;

    private final DeptService deptService;

    private final UserService userService;

    @Override
    public SysDeptUserPosition findUserByUserId(String type, Long userId) {
        return deptUserPositionRepository.findFirstByTypeAndUserId(type, userId);
    }

    @Override
    public SysDeptUserPosition findUserByUserIdAndTypeIn(Long userId, List<String> types) {
        return deptUserPositionRepository.findFirstByUserIdAndTypeIn(userId, types);
    }

    @Override
    public UserListDto getCurrentSelectedUser() {
        UserDeptField userDeptField = userRepository.findUserAndDept(SecurityUtils.getCurrentUsername());
        if(Objects.nonNull(userDeptField)) {
            UserListDto userListDto = new UserListDto();
            userListDto.setId(userDeptField.getId().toString());
            userListDto.setUserName(userDeptField.getUsername());
            userListDto.setNickName(userDeptField.getRealName());
            userListDto.setExtId(userDeptField.getGroupName() + userDeptField.getId());
            userListDto.setType(0);
            return userListDto;
        }
        return null;
    }

    @Override
    public ResultJson<Boolean> addOrUpdate(PositionBO bo) {
        SysDeptUserPosition sysDeptUserPosition;
        if(bo.getId() != null) {
            sysDeptUserPosition = deptUserPositionRepository.findById(bo.getId()).orElse(null);
            if(sysDeptUserPosition == null) {
                return ResultJson.generateResult(ResultCodeEnum.DATA_NOT_FOUND.getMessageCN(), ResultCodeEnum.DATA_NOT_FOUND.getCode());
            }
            sysDeptUserPosition.setModifiedDate(new Date());
        }else {
            sysDeptUserPosition = new SysDeptUserPosition();
            sysDeptUserPosition.setDeptId(bo.getDeptId());
            sysDeptUserPosition.setCreatorId(SecurityUtils.getCurrentUserId());
        }
        sysDeptUserPosition.setUserId(bo.getUserId());
        sysDeptUserPosition.setType(bo.getType());
        sysDeptUserPosition.setUserName(bo.getUserName());
        sysDeptUserPosition.setName(bo.getName());
        deptUserPositionRepository.save(sysDeptUserPosition);
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<Boolean> addOrUpdateUsers(PositionBatchBO bo) {
        deptUserPositionRepository.deleteByDeptIdAndType(bo.getDeptId(), bo.getType());
        Set<SimpleUserDto> set = bo.getPositionUsers();
        if(set == null || set.size() < 1) {
            return ResultJson.generateResult(true);
        }
        List<SysDeptUserPosition> list = set.stream().map(p -> {
            SysDeptUserPosition sysDeptUserPosition = new SysDeptUserPosition();
            sysDeptUserPosition.copy(bo);
            sysDeptUserPosition.setUserId(p.getId());
            sysDeptUserPosition.setUserName(p.getNickName());
            sysDeptUserPosition.setName(p.getUserName());
            return sysDeptUserPosition;
        }).collect(Collectors.toList());
        deptUserPositionRepository.saveAll(list);
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<Boolean> delete(PositionDeleteBO bo) {
        deptUserPositionRepository.deleteById(bo.getId());
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<List<SysDeptUserPosition>> getList(PositionListBO bo) {
        return ResultJson.generateResult(deptUserPositionRepository.findByDeptId(bo.getDeptId()));
    }

    @Override
    public List<SysDeptUserPosition> findList(PositionListBO bo) {
        return deptUserPositionRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, bo, criteriaBuilder));
    }

    @Override
    public List<SysDeptUserPosition> findListByType(PositionEnum positionEnum) {
        return deptUserPositionRepository.findByTypeOrderBySortAsc(positionEnum.getValue());
    }

    @Override
    public ResultJson<Object> getOrgList(CommonControlsBO bo) {
        if(bo.getType() == SystemConstant.COMMON_CONTROLS_TYPE_OGR) {
            //查询所有正常组织
            List<Dept> deptList;
            if(bo.getDeptIds() == null || bo.getDeptIds().size() < 1) {
                deptList = deptRepository.findByEnabled(true);

            }else {
                deptList = deptService.getListByDeptIdAndChildren(bo.getDeptIds());
            }
            //查询所有人员(写新方法为了不查询岗位、角色、部门浪费资源时间)
            List<UserDataDto> userInfoDtoList = wdSysOptionMapper.getUserListByEnabled();
            //查询所有负责人
            List<SysDeptUserPosition> positions = deptUserPositionRepository.findByTypeOrderBySortAsc(PositionEnum.HEAD.getValue());
            return ResultJson.generateResult(build(deptList, deptList.get(0).getPid(), userInfoDtoList, positions));
        }else if(bo.getType() == SystemConstant.COMMON_CONTROLS_TYPE_LINE) {
            //条线
            return this.getLineTreeList();
        }else if(bo.getType() == SystemConstant.COMMON_CONTROLS_TYPE_RECOMMENDED) {
            //推荐
            RecentlyPeopleBO peopleBO = new RecentlyPeopleBO();
            peopleBO.setType(bo.getRecommendedType());
            peopleBO.setLink(bo.getLink());
            peopleBO.setDefaultIds(bo.getDefaultIds());
            return this.getRecentlyPeopleList(peopleBO);
        }else if(bo.getType() == SystemConstant.COMMON_CONTROLS_TYPE_GROUP) {
            //常用组,需要type互换
            return this.getGroupListTree(bo.getUserId(), 1);
        }else if(bo.getType() == SystemConstant.COMMON_CONTROLS_TYPE_ORG_PERPLE) {
            //查询所有正常组织
            List<Dept> deptList = deptRepository.findByEnabled(true);
            List<Dept> deptPidList = deptRepository.findByPidIsNull();
            for(Dept dept : deptPidList) {
                if(dept.getId().intValue() == 20000) {
                    //为全部,新增
                    deptList.add(dept);
                    continue;
                }
                deptList.remove(dept);
            }
            //查询所有人员(写新方法为了不查询岗位、角色、部门浪费资源时间)
            List<UserDataDto> userInfoDtoList = wdSysOptionMapper.getUserListByEnabled();
            //查询所有负责人
            List<SysDeptUserPosition> positions = deptUserPositionRepository.findByTypeOrderBySortAsc(PositionEnum.HEAD.getValue());
            return ResultJson.generateResult(build(deptList, null, userInfoDtoList, positions));
        }
        return ResultJson.generateResult();
    }


    @Override
    public ResultJson<List<OrgListDto>> getOrgUserList(CommonSelectBO bo) {
        List<OrgListDto> orgListDtoList = new ArrayList<>();
        List<Dept> deptList = null;
        List<UserDataDto> userInfoDtoList = null;
        List<SysDeptUserPosition> positions = null;

        UserSelectorTypeEnum userSelectorTypeEnum = UserSelectorTypeEnum.getEnumByValue(bo.getUserSelector());
        switch(userSelectorTypeEnum) {
            // 本单位下用户
            case UNIT_USER_PICKER:

                // 判断是否单位账号
                UserDto user = userService.findByName(SecurityUtils.getCurrentUsername());
                if(2 == user.getUserType()) {
                    deptList = deptService.findByNameAndEnabled("%" + user.getName() + "%", true);
                }else {
                    List<Long> deptIds = new ArrayList<>();
                    DeptDto deptDto = this.getUnitDept(user.getDept().getId());
                    deptIds.add(deptDto.getId());
                    deptList = deptService.getListByDeptIdAndChildren(deptIds);
                }

                //查询所有人员(写新方法为了不查询岗位、角色、部门浪费资源时间)
                userInfoDtoList = wdSysOptionMapper.getUserListByEnabled();
                //查询所有负责人
                positions = deptUserPositionRepository.findByTypeOrderBySortAsc(PositionEnum.HEAD.getValue());
                break;
            // 本部门下用户
            case DEPT_USER_PICKER:

                // 判断是否单位账号
                user = userService.findByName(SecurityUtils.getCurrentUsername());
                if(2 == user.getUserType()) {
                    deptList = deptService.findByNameAndEnabled("%" + user.getName() + "%", true);
                }else {
                    List<Long> deptIds = new ArrayList<>();
                    deptIds.add(user.getDept().getId());
                    deptList = deptService.getListByDeptIdAndChildren(deptIds);
                }

                //查询所有人员(写新方法为了不查询岗位、角色、部门浪费资源时间)
                userInfoDtoList = wdSysOptionMapper.getUserListByEnabled();
                //查询所有负责人
                positions = deptUserPositionRepository.findByTypeOrderBySortAsc(PositionEnum.HEAD.getValue());
                break;
            // 选单位
            case UNIT_PICKER:
                List<User> userList = userService.findByEnabledAndUserType(true, 2);
                orgListDtoList = this.buildUnitUser(userList);
                break;
        }

        if(CollUtil.isNotEmpty(deptList)) {
            orgListDtoList = this.build(deptList, deptList.get(0).getPid(), userInfoDtoList, positions);
        }
        return ResultJson.generateResult(orgListDtoList);
    }

    public List<OrgListDto> buildUnitUser(List<User> userList) {
        List<OrgListDto> list = new ArrayList<>();
        if(CollUtil.isNotEmpty(userList)) {
            for(User user : userList) {
                OrgListDto listDto = new OrgListDto();
                listDto.setId(user.getId());
                listDto.setDeptName(user.getDept().getName());
                listDto.setNickName(user.getNickName());
                listDto.setUserName(user.getUsername());
                listDto.setType(0);
                listDto.setExtId(user.getDept().getName() + user.getId());
                listDto.setSort(user.getSort());
                list.add(listDto);
            }
            list.sort(Comparator.comparing(OrgListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        }
        return list;
    }

    public DeptDto getUnitDept(Long deptId) {
        DeptDto deptDto = null;
        boolean flag = true;
        while(flag && Objects.nonNull(deptId)) {
            deptDto = deptService.findById(deptId);
            if(1 == deptDto.getDeptType()) {
                flag = false;
                break;
            }else {
                deptId = deptDto.getPid();
            }
        }
        return deptDto;
    }

    @Override
    public ResultJson<Object> getOrgPhoneList(CommonPhoneControlsBO bo) {
        if(bo.getType() == SystemConstant.COMMON_PHONE_CONTROLS_TYPE_OGR) {
            //查询所有正常组织
            List<Dept> deptList = deptRepository.findByEnabled(true);
            //查询所有人员(写新方法为了不查询岗位、角色、部门浪费资源时间)
            List<UserDataDto> userInfoDtoList = wdSysOptionMapper.getUserListByEnabled();
            return ResultJson.generateResult(buildAndPhone(deptList, null, userInfoDtoList));
        }
        return ResultJson.generateResult();
    }

    @Override
    public ResultJson<List<UserListDto>> getUserList(UserListBO bo) {
        List<UserListDto> userListDtoList = new ArrayList<>();
        List<UserDeptField> userList = userRepository.findUserByNickName("%" + bo.getName() + "%");
        List<Long> includeUsers = bo.getIncludeUsers();
        if(CollUtil.isEmpty(includeUsers)) {
            includeUsers = new ArrayList<>();
        }else {
            includeUsers = UserDeptCache.setNotNullGet(includeUsers);
        }
        boolean notEmpty = CollUtil.isNotEmpty(includeUsers);
        if(!CollectionUtils.isEmpty(userList)) {
            for(UserDeptField user : userList) {
                if(notEmpty && !includeUsers.contains(user.getDeptId())) {
                    continue;
                }
                UserListDto userListDto = new UserListDto();
                userListDto.setId(user.getId().toString());
                userListDto.setUserName(user.getUsername());
                userListDto.setNickName(user.getRealName());
                userListDto.setExtId(user.getGroupName() + user.getId());
                userListDto.setType(0);
                userListDtoList.add(userListDto);
            }
        }
        return ResultJson.generateResult(userListDtoList);
    }

    @Override
    public ResultJson<List<UserPhoneListDto>> getUserPhoneList(UserListBO bo) {
        return ResultJson.generateResult(wdSysOptionMapper.getUserPhoneList(bo.getName()));
    }

    @Override
    public ResultJson<Boolean> addOrUpdateLine(WdLineBO bo) {
        if(bo.getPid().longValue() == 0L) {
            //pid为0代表顶级目录，顶级目录只允许存在一个
            int count = wdLineRepository.countByPid(bo.getPid());
            if(count > 0) {
                return ResultJson.generateResult("顶级条线已存在,请勿重复创建!", ResultCodeEnum.DATA_NOT_FOUND.getCode());
            }
        }
        int number;
        WdLine wdLine;
        if(bo.getId() != null) {
            wdLine = wdLineRepository.findById(bo.getId()).orElse(null);
            if(wdLine == null) {
                return ResultJson.generateResult(ResultCodeEnum.DATA_NOT_FOUND.getMessageCN(), ResultCodeEnum.DATA_NOT_FOUND.getCode());
            }
            number = wdLineRepository.countByPidAndTypeAndIdNot(bo.getPid(), bo.getType(), bo.getId());
        }else {
            wdLine = new WdLine();
            wdLine.setCreatorId(SecurityUtils.getCurrentUserId());
            number = wdLineRepository.countByPidAndType(bo.getPid(), bo.getType());
        }
        if(number > 0) {
            //若存在序号的记录则后续记录序号都累加
            wdLineMapper.updateSortBySort(bo.getSort(), bo.getType(), bo.getPid());
        }
        BeanUtils.copyProperties(bo, wdLine);
        wdLineRepository.save(wdLine);
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<Boolean> deleteLine(PositionDeleteBO bo) {
        WdLine wdLine = wdLineRepository.findById(bo.getId()).orElse(null);
        if(wdLine == null) {
            return ResultJson.generateResult(ResultCodeEnum.DATA_NOT_FOUND.getMessageCN(), ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }
        cycleWdLine(bo.getId());
        wdLineRepository.deleteById(bo.getId());
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<Object> getLineListTree(WdLineListBO bo) {
        //查询所有条线树数据
        List<WdLine> lineList;
        if(StringUtils.isNotBlank(bo.getName())) {
            lineList = wdLineRepository.findByNameLike("%" + bo.getName() + "%");
            return ResultJson.generateResult(queryLineList(lineList));
        }else {
            lineList = wdLineRepository.findAll();
            return ResultJson.generateResult(buildLine(lineList, 0L, null, new ArrayList<>()));
        }
    }

    @Override
    public ResultJson<Object> getLineTreeList() {
        //查询所有条线树数据
        List<WdLine> lineList = wdLineRepository.findAll();
        //查询所有人员对应岗位（查询岗位使用）
        //List<UserDataDto> treeDto=wdSysOptionMapper.getUserAndFirstJobList();
        //查询所有人员(部门使用)
        List<UserDataDto> userInfoTreeDto = wdSysOptionMapper.getUserListByEnabled();
        return ResultJson.generateResult(buildLine(lineList, 0L, null, userInfoTreeDto));
    }

    @Override
    public ResultJson<Boolean> addRecentlyPeople(RecentlyPeopleListBO bo) {
        List<Long> list = bo.getList();
        if(list == null || list.size() < 1) {
            return ResultJson.generateResult(true);
        }
        Long userId = SecurityUtils.getCurrentUserId();
        for(Long id : list) {
            if(id == null) {
                continue;
            }
            //(用户id+类型+环节)排名加1
            String key = SystemConstant.recommend_file + userId + (bo.getType() == null ? "" : bo.getType()) + (bo.getLink() == null ? "" : bo.getLink());
            redisTemplate.opsForZSet().incrementScore(key, id, 1.0);
        }
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<Object> getRecentlyPeopleList(RecentlyPeopleBO bo) {
        if(StringUtils.isNotBlank(bo.getLink()) && StringUtils.isBlank(bo.getType())) {
            return ResultJson.generateResult("环节不为空则类型不能为空!", ResultCodeEnum.DATA_NOT_FOUND.getCode());
        }
        Long userId = SecurityUtils.getCurrentUserId();
        //查询前20条记录(用户id+类型+环节)
        String key = SystemConstant.recommend_file + userId + (bo.getType() == null ? "" : bo.getType()) + (bo.getLink() == null ? "" : bo.getLink());
        Set<ZSetOperations.TypedTuple<Long>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 20);
        if(rangeWithScores == null || rangeWithScores.size() < 1) {
            //没有数据则查询该用户所有推荐人
            rangeWithScores = new HashSet<>();
            Set<String> set = stringRedisTemplate.keys(SystemConstant.recommend_file + userId + "*");
            for(String str : set) {
                Set<ZSetOperations.TypedTuple<Long>> keyScores = redisTemplate.opsForZSet().reverseRangeWithScores(str, 0, 20);
                rangeWithScores.addAll(keyScores);
            }
        }
        //转换为list且转换对象
        List<RecommendedDto> returnList = rangeWithScores.stream().map(p -> {
            RecommendedDto recommendedDto = new RecommendedDto();
            recommendedDto.setValue(p.getValue());
            recommendedDto.setScore(p.getScore());
            return recommendedDto;
        }).collect(Collectors.toList());
        //排序
        returnList.sort(Comparator.comparing(RecommendedDto::getScore, Comparator.nullsLast(Double::compareTo)).reversed());
        //去重复的人,只留分数最高的人记录
        Map<Long, Object> map = new HashMap<>(4);
        Iterator<RecommendedDto> dtoIterator = returnList.iterator();
        while(dtoIterator.hasNext()) {
            RecommendedDto tuple = dtoIterator.next();
            if(map.containsKey(tuple.getValue())) {
                //删除重复的人
                dtoIterator.remove();
            }
            map.put(tuple.getValue(), true);
        }
        map.clear();
        //是否有默认人，存在则加入list中,且位置在最前面(排名为原有最大的+1)，如果原有数据有当前人则要再加上原有排序值
        List<Long> list = bo.getDefaultIds();
        if(list != null && list.size() > 0) {
            //默认人list
            List<RecommendedDto> defaultList = new ArrayList<>();
            //最大的排序值
            Double max = 1.0;
            if(returnList != null && returnList.size() > 0) {
                //若有推荐人则拿去最大的分数记录+1
                max = returnList.get(0).getScore() + max;

            }
            for(Long id : list) {
                Iterator<RecommendedDto> tupleIterator = returnList.iterator();
                RecommendedDto dto = new RecommendedDto();
                dto.setScore(max);
                while(tupleIterator.hasNext()) {
                    //看推荐人和默认人是否有重叠
                    RecommendedDto tuple = tupleIterator.next();
                    if(tuple.getValue().longValue() == id.longValue()) {
                        //原有推荐人中含有默认人，排名再原有基础上增加，去除原有数据
                        dto.setScore(tuple.getScore() + dto.getScore());
                        tupleIterator.remove();
                        break;
                    }
                }
                dto.setValue(id);
                defaultList.add(dto);
            }
            returnList.addAll(defaultList);
        }
        //重新排序
        returnList.sort(Comparator.comparing(RecommendedDto::getScore, Comparator.nullsLast(Double::compareTo)).reversed());
        List<Object> returnListObject = new ArrayList<>();
        returnList.stream().forEach(p -> {
            User user = userRepository.findById(p.getValue()).orElse(null);
            if(user == null) {
                return;
            }
            returnListObject.add(user);
        });
        return ResultJson.generateResult(returnListObject);
    }

    @Override
    public ResultJson<Boolean> addOrUpdateGroup(WdGroupBO bo) {
        bo.setType(0);
        WdGroup wdGroup;
        if(bo.getId() != null) {
            wdGroup = wdGroupRepository.findById(bo.getId()).orElse(null);
            if(wdGroup == null) {
                return ResultJson.generateResult(ResultCodeEnum.DATA_NOT_FOUND.getMessageCN(), ResultCodeEnum.DATA_NOT_FOUND.getCode());
            }
        }else {
            wdGroup = new WdGroup();
            wdGroup.setCreatorId(SecurityUtils.getCurrentUserId());
        }
        BeanUtils.copyProperties(bo, wdGroup);
        wdGroupRepository.save(wdGroup);
        delCaches();
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<Boolean> addOrUpdateGroupUser(WdGroupUserBO bo) {
        //删除常用组下的所有人
        wdGroupRepository.deleteByPidAndType(bo.getId(), 1);
        //新增常用组下的人
        List<WdGroup> list = new ArrayList<>();
        if(bo.getUserList() != null && bo.getUserList().size() > 0) {
            bo.getUserList().stream().forEach(p -> {
                WdGroup wdGroup = new WdGroup();
                wdGroup.setName(p.getName());
                wdGroup.setUsername(p.getUsername());
                wdGroup.setUserId(p.getUserId());
                wdGroup.setType(1);
                wdGroup.setSort(p.getSort());
                wdGroup.setPid(bo.getId());
                wdGroup.setCreatorId(SecurityUtils.getCurrentUserId());
                list.add(wdGroup);
            });
        }
        wdGroupRepository.saveAll(list);
        delCaches();
        return ResultJson.generateResult(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultJson<Boolean> deleteGroup(WdDeleteGroupBO bo) {
        List<Long> ids = bo.getIds();
        for(Long id : ids) {
            int count = wdGroupRepository.countByPid(id);
            if(count > 0) {
                throw new BadRequestException("存在子组或人，请先删除!");
            }
            wdGroupRepository.deleteById(id);
            delCaches();
        }
        return ResultJson.generateResult(true);
    }

    @Override
    public ResultJson<Object> getGroupListTree(Long userId, Integer type) {
        //查询我的所有常用组数据
        if(Objects.isNull(userId)) {
            userId = SecurityUtils.getCurrentUserId();
        }
        List<WdGroup> groupList = wdGroupRepository.findByCreatorId(userId);
        Map<String, String> userDeptMap = new HashMap();
        Map<String, Boolean> userEnabledMap = new HashMap();
        if(CollUtil.isNotEmpty(groupList)) {
            List<String> usernameList = groupList.stream().map(wdGroup -> {
                return wdGroup.getUsername();
            }).collect(Collectors.toList());
            List<UserDto> userDtoList = userService.findByUserNames(usernameList);
            userDeptMap = userDtoList.stream().collect(Collectors.toMap(UserDto::getUsername, v -> v.getDept().getName(), (k1, k2) -> k1));
            userEnabledMap = userDtoList.stream().collect(Collectors.toMap(UserDto::getUsername, v -> v.getEnabled(), (k1, k2) -> k1));
        }
        return ResultJson.generateResult(buildGroupLine(groupList, 0L, type, userDeptMap, userEnabledMap));
    }

    @Override
    public ResultJson<Object> getGroupListTreeAndType(Long userId, Integer type) {
        //查询我的所有常用组数据
        userId = SecurityUtils.getCurrentUserId();
        List<WdGroup> groupList = wdGroupRepository.findByCreatorIdAndType(userId, type);
        Map<String, String> userDeptMap = new HashMap();
        if(CollUtil.isNotEmpty(groupList)) {
            List<String> usernameList = groupList.stream().map(wdGroup -> {
                return wdGroup.getUsername();
            }).collect(Collectors.toList());
            List<UserDto> userDtoList = userService.findByUserNames(usernameList);
            userDeptMap = userDtoList.stream().collect(Collectors.toMap(UserDto::getUsername, v -> v.getDept().getName(), (k1, k2) -> k1));
        }
        return ResultJson.generateResult(buildGroupLine(groupList, 0L, null, userDeptMap));
    }

    @Override
    public ResultJson<Object> getGroupUserList(GroupUserQueryCriteria criteria, Pageable pageable) {
        Page<WdGroup> page = wdGroupRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return ResultJson.generateResult(PageUtil.toPage(page));
    }

    @Override
    public ResultJson<List<OrgListTableDto>> getDeptTableList(DeptTableBO bo) {
        List<Dept> deptList = deptRepository.findByEnabled(true);
        //查询所有对应职位的人
        List<SysDeptUserPosition> positions = deptUserPositionRepository.findByTypeOrderBySortAsc(bo.getCode());
        return ResultJson.generateResult(buildDeptTable(deptList, null, positions));
    }

    @Override
    public ResultJson<Object> getAllGroupUserList(GroupUserQueryCriteria criteria) {
        return ResultJson.generateResult(wdGroupRepository.findByPidAndType(criteria.getPid(), criteria.getType()));
    }

    @Override
    public List<Long> findUserByDept(Long deptId, PositionEnum value) {
        return deptUserPositionRepository.findUsernameByDeptIdAndType(deptId, value.getValue());
    }

    @Override
    public List<SysDeptUserPosition> findUserByDeptAndPosition(Long deptId, PositionEnum value) {
        return deptUserPositionRepository.findByTypeAndDeptId(value.getValue(), deptId);
    }

    /**
     * 递归查子部门
     * @param deptList
     * @param parentId
     * @param userInfoDtoList
     * @return
     */
    private List<OrgListDto> build(List<Dept> deptList, Long parentId, List<UserDataDto> userInfoDtoList, List<SysDeptUserPosition> positions) {
        List<OrgListDto> list = new ArrayList<>();
        for(Dept dept : deptList) {
            //传入的pid为空则查询pid为空符合,传入的pid不为空则需要pid不为空且相同符合
            Boolean flag = (parentId == null && dept.getPid() == null) ||
                    (parentId != null && dept.getPid() != null && dept.getPid().longValue() == parentId.longValue());
            if(flag) {
                OrgListDto orgListDto = new OrgListDto();
                orgListDto.setId(dept.getId());
                orgListDto.setDeptName(dept.getName());
                orgListDto.setNickName(dept.getName());
                orgListDto.setSort(dept.getDeptSort());
                orgListDto.setType(1);
                orgListDto.setExtId(dept.getId().toString());
                Iterator<SysDeptUserPosition> iteratorPosition = positions.iterator();
                while(iteratorPosition.hasNext()) {
                    SysDeptUserPosition position = iteratorPosition.next();
                    if(position.getDeptId().longValue() == dept.getId().longValue()) {
                        //负责人赋值
                        Map<String, Object> highest = new HashMap<>(4);
                        highest.put("id", position.getUserId());
                        highest.put("nickName", position.getUserName());
                        highest.put("deptName", dept.getName());
                        highest.put("type", 0);
                        highest.put("userName", position.getName());
                        highest.put("extId", dept.getName() + position.getUserId());
                        orgListDto.setHighest(highest);
                        //负责人集合删除已使用数据
                        iteratorPosition.remove();
                    }
                }
                Iterator<UserDataDto> userDataDtoIterator = userInfoDtoList.iterator();
                while(userDataDtoIterator.hasNext()) {
                    UserDataDto userDataDto = userDataDtoIterator.next();
                    if(userDataDto.getDeptId().longValue() == dept.getId().longValue() || userDataDto.getConcurDept().contains(dept.getId())) {
                        OrgListDto listDto = new OrgListDto();
                        listDto.setId(userDataDto.getUserId());
                        listDto.setDeptName(dept.getName());
                        listDto.setNickName(userDataDto.getNickName());
                        listDto.setUserName(userDataDto.getUsername());
                        listDto.setType(0);
                        listDto.setExtId(dept.getName() + userDataDto.getUserId());
                        listDto.setSort(userDataDto.getSort());
                        //人员赋值
                        if(orgListDto.getChildren() == null) {
                            orgListDto.setChildren(new ArrayList<>());
                        }
                        orgListDto.getChildren().add(listDto);

                    }
                }
                List<OrgListDto> child = orgListDto.getChildren();
                if(child != null && child.size() > 0) {
                    child.sort(Comparator.comparing(OrgListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
                }
                List<OrgListDto> childList = build(deptList, dept.getId(), userInfoDtoList, positions);
                if(childList != null && childList.size() > 0) {
                    if(orgListDto.getChildren() == null) {
                        orgListDto.setChildren(new ArrayList<>());
                    }
                    //子部门加入children
                    orgListDto.getChildren().addAll(childList);
                }
                list.add(orgListDto);
            }
        }
        list.sort(Comparator.comparing(OrgListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    /**
     * 递归查子部门返回增加手机号
     * @param deptList
     * @param parentId
     * @param userInfoDtoList
     * @return
     */
    private List buildAndPhone(List<Dept> deptList, Long parentId, List<UserDataDto> userInfoDtoList) {
        List<OrgPhoneListDto> list = new ArrayList<>();
        for(Dept dept : deptList) {
            //传入的pid为空则查询pid为空符合,传入的pid不为空则需要pid不为空且相同符合
            Boolean flag = (parentId == null && dept.getPid() == null) ||
                    (parentId != null && dept.getPid() != null && dept.getPid().longValue() == parentId.longValue());
            if(flag) {
                OrgPhoneListDto orgListDto = new OrgPhoneListDto();
                orgListDto.setId(dept.getId());
                orgListDto.setNickName(dept.getName());
                orgListDto.setSort(dept.getDeptSort());
                orgListDto.setType(1);
                Iterator<UserDataDto> userDataDtoIterator = userInfoDtoList.iterator();
                while(userDataDtoIterator.hasNext()) {
                    UserDataDto userDataDto = userDataDtoIterator.next();
                    if(userDataDto.getDeptId().longValue() == dept.getId().longValue()) {
                        OrgPhoneListDto listDto = new OrgPhoneListDto();
                        listDto.setId(userDataDto.getUserId());
                        listDto.setNickName(userDataDto.getNickName());
                        listDto.setUserName(userDataDto.getUsername());
                        listDto.setType(0);
                        listDto.setSort(userDataDto.getSort());
                        listDto.setPhone(userDataDto.getPhone());
                        listDto.setDeptName(userDataDto.getDeptName());
                        //人员赋值
                        if(orgListDto.getChildren() == null) {
                            orgListDto.setChildren(new ArrayList<>());
                        }
                        orgListDto.getChildren().add(listDto);
                        //人员集合删除已使用数据
                        userDataDtoIterator.remove();
                    }
                }
                List<OrgPhoneListDto> child = orgListDto.getChildren();
                if(child != null && child.size() > 0) {
                    child.sort(Comparator.comparing(OrgPhoneListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
                }
                List<OrgPhoneListDto> childList = buildAndPhone(deptList, dept.getId(), userInfoDtoList);
                if(childList != null && childList.size() > 0) {
                    if(orgListDto.getChildren() == null) {
                        orgListDto.setChildren(new ArrayList<>());
                    }
                    //子部门加入children
                    orgListDto.getChildren().addAll(childList);
                }
                list.add(orgListDto);
            }
        }
        list.sort(Comparator.comparing(OrgPhoneListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    private List<OrgListDto> buildLine(List<WdLine> lineList, Long parentId, List<UserDataDto> treeDto, List<UserDataDto> userInfoTreeDto) {
        List<OrgListDto> list = lineList.stream().filter(wdLine -> wdLine.getPid().longValue() == parentId.longValue()).map(wdLine -> {
            OrgListDto lineTreeDto = new OrgListDto();
            lineTreeDto.setId(wdLine.getKeyId());
            lineTreeDto.setDeptName(wdLine.getName());
            lineTreeDto.setNickName(wdLine.getName());
            lineTreeDto.setType(wdLine.getType());
            lineTreeDto.setTreeId(parentId + wdLine.getName());
            lineTreeDto.setSort(wdLine.getSort());
            lineTreeDto.setLineId(wdLine.getId());
            //人员循环
            if(wdLine.getType() == 0) {
                Optional<User> userOptional = userRepository.findById(wdLine.getKeyId());
                userOptional.ifPresent(user -> lineTreeDto.setUserName(user.getUsername()));
                //                Iterator<UserDataDto> treeDtoIterator = treeDto.iterator();
//                while(treeDtoIterator.hasNext()){
//                    UserDataDto next = treeDtoIterator.next();
//                    if(next.getUserId().longValue()==wdLine.getKeyId().longValue()){
//                        //是人员且人员id符合则赋值岗位
//                        lineTreeDto.setPost(next.getPost());
//                        treeDtoIterator.remove();
//                    }
//                }
                List<OrgListDto> childList = buildLine(lineList, wdLine.getId(), treeDto, userInfoTreeDto);
                if(childList != null && childList.size() > 0) {
                    //若还有子部门或人继续赋值
                    lineTreeDto.setChildren(childList);
                }
            }else if(wdLine.getType() == 1) {
                //部门下的人循环赋值
                Iterator<UserDataDto> treeCopyDtoIterator = userInfoTreeDto.iterator();
                List<OrgListDto> userTreeDto = new ArrayList<>();
                while(treeCopyDtoIterator.hasNext()) {
                    UserDataDto next = treeCopyDtoIterator.next();
                    if(wdLine.getType() == 1) {
                        //是部门
                        if(next.getDeptId() != null && (wdLine.getKeyId().longValue() == next.getDeptId().longValue())) {
                            //部门id符合
                            OrgListDto userTree = new OrgListDto();
                            userTree.setNickName(next.getNickName());
                            userTree.setUserName(next.getUsername());
                            userTree.setId(next.getUserId());
                            userTree.setType(0);
                            userTree.setTreeId(parentId + userTree.getUserName());
                            userTree.setSort(next.getSort());
                            userTree.setLineId(null);
                            userTreeDto.add(userTree);
                            //因为一个人只能在一个部门，故可以删除已使用人员
                            //treeCopyDtoIterator.remove();
                        }
                    }
                }
                //部门底下人赋值
                lineTreeDto.setChildren(userTreeDto);
            }
            return lineTreeDto;
        }).collect(Collectors.toList());
        list.sort(Comparator.comparing(OrgListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    private List<OrgListDto> buildGroupLine(List<WdGroup> lineList, Long parentId, Integer type, Map<String, String> userDeptMap, Map<String, Boolean> userEnabledMap) {
        Stream<WdGroup> streamList = null;
        if(CollUtil.isNotEmpty(userEnabledMap)) {
            streamList = lineList.stream().filter(wdGroup -> (wdGroup.getPid().longValue() == parentId.longValue() && (StringUtils.isEmpty(wdGroup.getUsername()) || userEnabledMap.get(wdGroup.getUsername()))));
        }else {
            streamList = lineList.stream().filter(wdGroup -> (wdGroup.getPid().longValue() == parentId.longValue()));
        }
        List<OrgListDto> list = streamList.map(wdGroup -> {
            OrgListDto lineTreeDto = new OrgListDto();
            if(wdGroup.getType() == 0) {
                lineTreeDto.setId(wdGroup.getId());
                lineTreeDto.setCustomId(wdGroup.getId().toString());
                lineTreeDto.setExtId(wdGroup.getId().toString());
            }else {
                lineTreeDto.setUserName(wdGroup.getUsername());
                lineTreeDto.setId(wdGroup.getUserId());
                lineTreeDto.setCustomId(wdGroup.getPid() + wdGroup.getUsername());
                lineTreeDto.setExtId(userDeptMap.get(wdGroup.getUsername()) + wdGroup.getUserId());
            }
            lineTreeDto.setDeptName(wdGroup.getName());
            lineTreeDto.setNickName(wdGroup.getName());
            if(type != null) {
                int moduleType = wdGroup.getType();
                lineTreeDto.setType(moduleType == 0 ? 1 : 0);
            }
            lineTreeDto.setSort(wdGroup.getSort());
            lineTreeDto.setPid(wdGroup.getPid());
            List<OrgListDto> childList = buildGroupLine(lineList, wdGroup.getId(), type, userDeptMap, userEnabledMap);
            if(childList != null && childList.size() > 0) {
                //若还有子部门或人继续赋值
                lineTreeDto.setChildren(childList);
            }
            return lineTreeDto;
        }).collect(Collectors.toList());
        list.sort(Comparator.comparing(OrgListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    private List<OrgListDto> buildGroupLine(List<WdGroup> lineList, Long parentId, Integer type, Map<String, String> userDeptMap) {
        return this.buildGroupLine(lineList, parentId, type, userDeptMap, null);
    }

    /**
     * 递归查子部门
     * @param deptList
     * @param parentId
     * @return
     */
    private List<OrgListTableDto> buildDeptTable(List<Dept> deptList, Long parentId, List<SysDeptUserPosition> positions) {
        List<OrgListTableDto> list = new ArrayList<>();
        for(Dept dept : deptList) {
            //传入的pid为空则查询pid为空符合,传入的pid不为空则需要pid不为空且相同符合
            Boolean flag = (parentId == null && dept.getPid() == null) ||
                    (parentId != null && dept.getPid() != null && dept.getPid().longValue() == parentId.longValue());
            if(flag) {
                OrgListTableDto orgListDto = new OrgListTableDto();
                orgListDto.setId(dept.getId());
                orgListDto.setNickName(dept.getName());
                orgListDto.setSort(dept.getDeptSort());
                Iterator<SysDeptUserPosition> iteratorPosition = positions.iterator();
                while(iteratorPosition.hasNext()) {
                    SysDeptUserPosition position = iteratorPosition.next();
                    if(position.getDeptId().longValue() == dept.getId().longValue()) {
                        //负责人赋值
                        Map<String, Object> highest = new HashMap<>(4);
                        highest.put("id", position.getUserId());
                        highest.put("nickName", position.getUserName());
                        highest.put("userName", position.getName());
                        highest.put("lastTime", position.getModifiedDate() == null ? position.getCreateDate() : position.getModifiedDate());
                        highest.put("positionId", position.getId());
                        orgListDto.getHighest().add(highest);
                        //负责人集合删除已使用数据
                        iteratorPosition.remove();
                    }
                }
                List<OrgListTableDto> child = orgListDto.getChildren();
                if(child != null && child.size() > 0) {
                    child.sort(Comparator.comparing(OrgListTableDto::getSort, Comparator.nullsLast(Integer::compareTo)));
                }
                List<OrgListTableDto> childList = buildDeptTable(deptList, dept.getId(), positions);
                if(childList != null && childList.size() > 0) {
                    if(orgListDto.getChildren() == null) {
                        orgListDto.setChildren(new ArrayList<>());
                    }
                    //子部门加入children
                    orgListDto.getChildren().addAll(childList);
                }
                list.add(orgListDto);
            }
        }
        list.sort(Comparator.comparing(OrgListTableDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }

    @Override
    public List<SysDeptUserPosition> findByTypeIn(List<String> types) {
        return deptUserPositionRepository.findByTypeIn(types);
    }

    private List<OrgListDto> queryLineList(List<WdLine> lineList) {
        List<OrgListDto> list = lineList.stream().map(wdLine -> {
            OrgListDto lineTreeDto = new OrgListDto();
            lineTreeDto.setId(wdLine.getKeyId());
            lineTreeDto.setDeptName(wdLine.getName());
            lineTreeDto.setNickName(wdLine.getName());
            lineTreeDto.setType(wdLine.getType());
            lineTreeDto.setTreeId(wdLine.getPid() + wdLine.getName());
            lineTreeDto.setSort(wdLine.getSort());
            lineTreeDto.setLineId(wdLine.getId());
            return lineTreeDto;
        }).collect(Collectors.toList());
        list.sort(Comparator.comparing(OrgListDto::getSort, Comparator.nullsLast(Integer::compareTo)));
        return list;
    }


    private void cycleWdLine(Long pid) {
        List<WdLine> list = wdLineRepository.findByPid(pid);
        if(list != null && list.size() > 0) {
            wdLineRepository.deleteAll(list);
            for(WdLine wdLine : list) {
                cycleWdLine(wdLine.getId());
            }
        }
    }

    /**
     * 清理缓存
     * @param /
     */
    public void delCaches() {
        // 删除数据权限
        Set<Object> keySet = new HashSet<>();
        keySet.addAll(redisTemplate.keys("position*"));
        redisTemplate.delete(keySet);
    }

}
