/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.service;

import com.alibaba.fastjson.JSONObject;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.dto.UserQueryCriteria;
import com.wangda.oa.modules.system.service.dto.UserRoleQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> Jie
 * @date 2018-11-23
 */
public interface UserService {

    /**
     * 根据ID查询
     * @param id ID
     * @return /
     */
    UserDto findById(long id);

    /**
     * 新增用户
     * @param resources /
     */
    void create(User resources);

    /**
     * 编辑用户
     * @param resources /
     */
    void update(User resources) throws Exception;

    /**
     * 删除用户
     * @param ids /
     */
    void delete(Set<Long> ids);

    /**
     * 根据用户名查询
     * @param userName /
     * @return /
     */
    UserDto findByName(String userName);

    /**
     * 修改密码
     * @param username 用户名
     * @param encryptPassword 密码
     */
    void updatePass(String username, String encryptPassword);

    /**
     * 修改头像
     * @param file 文件
     * @return /
     */
    Map<String, String> updateAvatar(MultipartFile file);

    /**
     * 修改邮箱
     * @param username 用户名
     * @param email 邮箱
     */
    void updateEmail(String username, String email);

    /**
     * 查询全部
     * @param criteria 条件
     * @param pageable 分页参数
     * @return /
     */
    Object queryAll(UserQueryCriteria criteria, Pageable pageable);

    /**
     * 查询全部不分页
     * @param criteria 条件
     * @return /
     */
    List<UserDto> queryAll(UserQueryCriteria criteria);

    /**
     * 导出数据
     * @param queryAll 待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<UserDto> queryAll, HttpServletResponse response) throws IOException;

    /**
     * 用户自助修改资料
     * @param resources /
     */
    void updateCenter(User resources);

    /**
     * 禁用用户
     * @param username
     */
    void disableUser(String username);

    /**
     * 根据角色id查询用户
     * @return
     */
    Map findPageByRoleId(Long criteria, Pageable pageable);

    /**
     * 根据部门id获取用户列表
     * @param deptId
     * @param userType
     * @return
     */
    List<UserDto> findByDeptId(Long deptId, Integer userType);

    List<UserDto> findByDeptIds(List<Long> deptIds, Integer userType);

    List<JSONObject> getUnitUsersTree();

    List<String> findUsernameByIds(List<Long> ids);

    /**
     * 根据角色Id查询用户
     * @param criteria 条件
     * @param pageable 分页参数
     * @return /
     */
    Object findUsersByRoleId(UserRoleQueryCriteria criteria, Pageable pageable);

    /**
    * Description: 根据用户名批量查询用户
    * @param userNames
    * @return: java.util.List<com.wangda.oa.modules.system.service.dto.UserDto>
    * @Date: 2021/11/12 17:31
    * @Author: maogy
    * @throws:
    */
    List<UserDto> findByUserNames(List<String> userNames);

    /**
     * 根据用户中文名模糊查询用户
     * @param nickName
     * @param enabled
     * @return
     */
    List<UserDto> findByUserNickNameLike(String nickName, Boolean enabled);

    /**
     * 根据角色权限查询用户
     * @param authorityKey
     * @return
     */
    List<User> findByRoleAuthorityKey(String authorityKey);

    /**
    * Description: 根据角色id查询角色关联的所有用户
    * @param roleId
    * @return: java.util.List<com.wangda.oa.modules.system.domain.User>
    * @Date: 2021/12/5 13:05
    * @Author: maogy
    * @throws:
    */
    List<Map<String,Object>> findAllByRoleId(Long roleId);

    /**
    * Description: 根据用户类型查询
    * @param enabled
    * @param userTyp
    * @return: java.util.List<com.wangda.oa.modules.system.domain.User>
    * @Date: 2021/12/8 15:32
    * @Author: maogy
    * @throws:
    */
    List<User> findByEnabledAndUserType(Boolean enabled, Integer userTyp);

    /**
     *@param
     *@return
     *@creator dfr
     */
    List<Map> findByEnabledAndUserTypeList(Boolean enabled, Integer userTyp,String nickName);

    /**
     * Description: 根据用户名查询用户信息(用于流程审批时意见自动生成)
     * @param userNames
     * @return: java.util.List<com.wangda.oa.modules.system.service.dto.UserDto>
     * @Date: 2021/12/16 19:36
     * @Author: maogy
     * @throws:
     */
    List<UserDto> findSpecialUserByUserName(List<String> userNames);

    Boolean checkWhetherUnitAccount();



    /**
    * Description: 修改电话
    * @param username
    * @param phone
    * @return: void
    * @Date: 2022/1/6 17:53
    * @Author: maogy
    * @throws:
    */
    void updatePhone(String username, String phone);

    /**
     * 根据手机号查询用户
     * @param phone
     * @return
     */
    UserDto findByPhone(String phone);
}
