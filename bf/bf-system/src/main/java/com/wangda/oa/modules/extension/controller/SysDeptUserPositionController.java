package com.wangda.oa.modules.extension.controller;

import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.modules.extension.bo.*;
import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import com.wangda.oa.modules.extension.dto.GroupUserQueryCriteria;
import com.wangda.oa.modules.extension.dto.org.OrgListDto;
import com.wangda.oa.modules.extension.dto.org.OrgListTableDto;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import com.wangda.oa.modules.extension.dto.org.UserPhoneListDto;
import com.wangda.oa.modules.extension.enums.PositionEnum;
import com.wangda.oa.modules.extension.service.SysDeptUserPositionService;
import com.wangda.oa.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午2:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/position")
@Api(tags = "部门职位、组织架构查询、条线管理")
@Slf4j
public class SysDeptUserPositionController {

    private final SysDeptUserPositionService sysDeptUserPositionService;

    /**
     * 新增或修改部门职位
     * @return
     */
    @ApiOperation(value = "新增或修改部门职位", notes = "新增或修改部门职位")
    @RequestMapping(value = "/addOrUpdate", method = RequestMethod.POST)
    public ResultJson<Boolean> addOrUpdate(@RequestBody @Validated PositionBO bo) {
        return sysDeptUserPositionService.addOrUpdate(bo);
    }

    /**
     * 新增或修改部门职位（多个人）
     * @return
     */
    @ApiOperation(value = "新增或修改部门职位（多个人）", notes = "新增或修改部门职位（多个人）")
    @RequestMapping(value = "/addOrUpdateUsers", method = RequestMethod.POST)
    public ResultJson<Boolean> addOrUpdateUsers(@RequestBody @Validated PositionBatchBO bo) {
        return sysDeptUserPositionService.addOrUpdateUsers(bo);
    }

    /**
     * 删除部门职位
     * @return
     */
    @ApiOperation(value = "删除部门职位", notes = "删除部门职位")
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultJson<Boolean> delete(@RequestBody @Validated PositionDeleteBO bo) {
        return sysDeptUserPositionService.delete(bo);
    }

    /**
     * 查询部门职位列表
     * @return
     */
    @ApiOperation(value = "查询部门职位列表", notes = "查询部门职位列表")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    public ResultJson<List<SysDeptUserPosition>> getList(@RequestBody @Validated PositionListBO bo) {
        return sysDeptUserPositionService.getList(bo);
    }

    /**
     * 查询职位集合
     * @return
     */
    @ApiOperation(value = "查询职位集合", notes = "查询职位集合")
    @RequestMapping(value = "/getPosition", method = RequestMethod.POST)
    public ResultJson<List<Map<String, Object>>> getPosition() {
        return ResultJson.generateResult(PositionEnum.getAllInfo());
    }

    /**
     * 查询通用控件
     * @return
     */
    @ApiOperation(value = "查询通用控件", notes = "查询通用控件")
    @RequestMapping(value = "/getOrgList", method = RequestMethod.POST)
    @AnonymousAccess
    public ResultJson<Object> getOrgList(@RequestBody CommonControlsBO bo) {
        if(bo.getDeptIds() == null) {
            bo.setDeptIds(new ArrayList<>(0));
        }
        if(bo.getUserId() == null) {
            bo.setUserId(SecurityUtils.getCurrentUserId());
        }
        return sysDeptUserPositionService.getOrgList(bo);
    }

    /**
     * 选人通用控件
     * @return ResultJson
     */
    @ApiOperation(value = "选人通用控件")
    @RequestMapping(value = "/getOrgUserList", method = RequestMethod.POST)
    public ResultJson<List<OrgListDto>> getOrgUserList(@Validated @RequestBody CommonSelectBO bo) {
        return sysDeptUserPositionService.getOrgUserList(bo);
    }

    /**
     * 获取当前用户控件
     * @return
     */
    @ApiOperation(value = "获取当前用户数据")
    @RequestMapping(value = "/getCurrentSelectedUser", method = RequestMethod.GET)
    public ResponseEntity<UserListDto> getCurrentSelectedUser() {
        return ResponseEntity.ok(sysDeptUserPositionService.getCurrentSelectedUser());
    }

    /**
     * 查询通讯录控件
     * @return
     */
    @ApiOperation(value = "查询通讯录控件", notes = "查询通讯录控件")
    @RequestMapping(value = "/getOrgPhoneList", method = RequestMethod.POST)
    @AnonymousAccess
    public ResultJson<Object> getOrgPhoneList(@RequestBody CommonPhoneControlsBO bo) {
        return sysDeptUserPositionService.getOrgPhoneList(bo);
    }

    /**
     * 根据用户名查询用户集合
     * @return
     */
    @ApiOperation(value = "根据用户名查询用户集合", notes = "根据用户名查询用户集合")
    @RequestMapping(value = "/getUserList", method = RequestMethod.POST)
    public ResultJson<List<UserListDto>> getUserList(@RequestBody UserListBO bo) {
        return sysDeptUserPositionService.getUserList(bo);
    }

    /**
     * 根据用户名查询用户手机集合
     * @return
     */
    @ApiOperation(value = "根据用户名查询用户手机集合", notes = "根据用户名查询用户手机集合")
    @RequestMapping(value = "/getUserPhoneList", method = RequestMethod.POST)
    @AnonymousAccess
    public ResultJson<List<UserPhoneListDto>> getUserPhoneList(@RequestBody UserListBO bo) {
        return sysDeptUserPositionService.getUserPhoneList(bo);
    }

    /**
     * 新增或修改条线
     * @return
     */
    @ApiOperation(value = "新增或修改条线", notes = "新增或修改条线")
    @RequestMapping(value = "/addOrUpdateLine", method = RequestMethod.POST)
    public ResultJson<Boolean> addOrUpdateLine(@RequestBody @Validated WdLineBO bo) {
        return sysDeptUserPositionService.addOrUpdateLine(bo);
    }

    /**
     * 删除条线
     * @return
     */
    @ApiOperation(value = "删除条线", notes = "删除条线")
    @RequestMapping(value = "/deleteLine", method = RequestMethod.POST)
    public ResultJson<Boolean> deleteLine(@RequestBody @Validated PositionDeleteBO bo) {
        return sysDeptUserPositionService.deleteLine(bo);
    }

    /**
     * 查询条线的树(只返回组)
     * @return
     */
    @ApiOperation(value = "查询条线的树(只返回组)", notes = "查询条线的树(只返回组)")
    @RequestMapping(value = "/getLineListTree", method = RequestMethod.POST)
    public ResultJson<Object> getLineListTree(@RequestBody WdLineListBO bo) {
        return sysDeptUserPositionService.getLineListTree(bo);
    }

    /**
     * 根据类型新增推荐人
     * @return
     */
    @ApiOperation(value = "根据类型新增推荐人", notes = "根据类型新增推荐人")
    @RequestMapping(value = "/addRecentlyPeople", method = RequestMethod.POST)
    public ResultJson<Boolean> addRecentlyPeople(@RequestBody @Validated RecentlyPeopleListBO bo) {
        return sysDeptUserPositionService.addRecentlyPeople(bo);
    }

    /**
     * 新增或修改常用组
     * @return
     */
    @ApiOperation(value = "新增或修改常用组", notes = "新增或修改常用组")
    @RequestMapping(value = "/addOrUpdateGroup", method = RequestMethod.POST)
    public ResultJson<Boolean> addOrUpdateGroup(@RequestBody @Validated WdGroupBO bo) {
        return sysDeptUserPositionService.addOrUpdateGroup(bo);
    }

    /**
     * 新增或修改常用组的人
     * @return
     */
    @ApiOperation(value = "新增或修改常用组的人", notes = "新增或修改常用组的人")
    @RequestMapping(value = "/addOrUpdateGroupUser", method = RequestMethod.POST)
    public ResultJson<Boolean> addOrUpdateGroupUser(@RequestBody @Validated WdGroupUserBO bo) {
        return sysDeptUserPositionService.addOrUpdateGroupUser(bo);
    }

    /**
     * 删除常用组或人
     * @return
     */
    @ApiOperation(value = "删除常用组或人", notes = "删除常用组或人")
    @RequestMapping(value = "/deleteGroup", method = RequestMethod.POST)
    public ResultJson<Boolean> deleteGroup(@RequestBody @Validated WdDeleteGroupBO bo) {
        return sysDeptUserPositionService.deleteGroup(bo);
    }

    /**
     * 查询常用组的树(只返回组)
     * @return
     */
    @ApiOperation(value = "查询常用组的树(只返回组)", notes = "查询常用组的树(只返回组)")
    @RequestMapping(value = "/getGroupListTree", method = RequestMethod.POST)
    public ResultJson<Object> getGroupListTree(@RequestBody WdGroupBO bo) {
        return sysDeptUserPositionService.getGroupListTreeAndType(bo.getUserId(), 0);
    }

    /**
     * 查询常用组的人
     * @return
     */
    @ApiOperation(value = "查询常用组的人", notes = "查询常用组的人")
    @GetMapping(value = "/getGroupUserList")
    public ResultJson<Object> getGroupUserList(GroupUserQueryCriteria criteria, Pageable pageable) {
        return sysDeptUserPositionService.getGroupUserList(criteria, pageable);
    }

    /**
     * 查询常用组的人(不分页)
     * @return
     */
    @ApiOperation(value = "查询常用组的人(不分页)", notes = "查询常用组的人(不分页)")
    @GetMapping(value = "/getAllGroupUserList")
    public ResultJson<Object> getAllGroupUserList(GroupUserQueryCriteria criteria) {
        return sysDeptUserPositionService.getAllGroupUserList(criteria);
    }

    /**
     * 查询对应职位部门表格数据
     * @return
     */
    @ApiOperation(value = "查询对应职位部门表格数据", notes = "查询对应职位部门表格数据")
    @RequestMapping(value = "/getDeptTableList", method = RequestMethod.POST)
    @AnonymousAccess
    public ResultJson<List<OrgListTableDto>> getDeptTableList(@RequestBody @Validated DeptTableBO bo) {
        return sysDeptUserPositionService.getDeptTableList(bo);
    }

}
