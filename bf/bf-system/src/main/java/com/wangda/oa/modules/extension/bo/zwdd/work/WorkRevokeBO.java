package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午5:16
 */
@Data
@Builder
@ApiModel(description="工作通知")
public class WorkRevokeBO {

    @ApiModelProperty(value = "业务消息id",required = true)
    private String bizMsgId;

    @Tolerate
    WorkRevokeBO(){}
}
