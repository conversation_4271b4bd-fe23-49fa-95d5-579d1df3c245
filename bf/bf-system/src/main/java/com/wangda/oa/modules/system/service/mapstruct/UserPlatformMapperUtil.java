package com.wangda.oa.modules.system.service.mapstruct;

import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.service.SysUserPlatformService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Component
public class UserPlatformMapperUtil {

    @Autowired
    private SysUserPlatformService sysUserPlatformService;

    @Autowired
    private ZwddProperties zwddProperties;

    @Named("setPlatformUserId")
    public String setPlatformUserId(String username) {
        if(StringUtils.isEmpty(username)) {
            return null;
        }
        SysUserPlatform sysUserPlatform = sysUserPlatformService.queryByUserNameAndType(username, zwddProperties.getType());
        if(Objects.isNull(sysUserPlatform)) {
            return null;
        }
        return sysUserPlatform.getPlatformUserId();
    }

}
