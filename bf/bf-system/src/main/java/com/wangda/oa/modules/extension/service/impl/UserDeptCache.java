package com.wangda.oa.modules.extension.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.wangda.oa.modules.system.service.DeptService;
import com.wangda.oa.utils.SpringContextHolder;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

class UserDeptCache {
    private static final ConcurrentHashMap<String, List<Long>> cache = new ConcurrentHashMap<>();
    private static final DeptService service = SpringContextHolder.getBean(DeptService.class);

    public static List<Long> setNotNullGet(List<Long> deptIds) {
        List<Long> includeUsers = deptIds;
        String join = CollUtil.join(includeUsers, ",");
        return cache.computeIfAbsent(join, k -> service.findAllChildDeptIds(includeUsers));
    }

    public static void cleanCache() {
        cache.clear();
    }
}
