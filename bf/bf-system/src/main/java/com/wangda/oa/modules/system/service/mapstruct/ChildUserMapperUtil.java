package com.wangda.oa.modules.system.service.mapstruct;

import com.wangda.oa.modules.system.service.ChildUserService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

@Component
public class ChildUserMapperUtil {

    @Autowired
    @Lazy
    private ChildUserService childUserService;

    @Autowired
    private UserService userService;

    public SimpleChildUserDto toChildUser(String userName) {
        if(StringUtils.isEmpty(userName)) {
            return new SimpleChildUserDto();
        }
        boolean isChild=false;
        ChildUserDto childUser = childUserService.findByName(userName);
        SimpleUserDto simpleUserDto = null;
        if(Objects.nonNull(childUser)) {
            isChild=true;
            //子账号
            simpleUserDto = SimpleUserDto.builder()
                    .id(childUser.getId())
                    .userName(childUser.getUsername())
                    .nickName(childUser.getNickName())
                    .build();
        }
        if(isChild){
            //是子账号则继续查询主账号
            UserDto user = userService.findByName(childUser.getDeptUsername());
            if(Objects.nonNull(user)) {
                SimpleChildUserDto childUserDto = SimpleChildUserDto.builder()
                        .id(user.getId())
                        .userName(user.getUsername())
                        .nickName(user.getNickName())
                        .userDto(simpleUserDto)
                        .build();
                return childUserDto;
            }
        }else {
            UserDto userDto = userService.findByName(userName);
            if (Objects.nonNull(userDto)) {
                SimpleChildUserDto childUserDto = SimpleChildUserDto.builder()
                        .id(userDto.getId())
                        .userName(userDto.getUsername())
                        .nickName(userDto.getNickName())
                        .userDto(null)
                        .build();
                return childUserDto;
            }
        }
        return new SimpleChildUserDto();
    }

    public String toName(SimpleChildUserDto simpleUserDto) {
        if(simpleUserDto==null) {
            return null;
        }
        return simpleUserDto.getUserName();
    }

    public UnitConversionDto toNickName(String deptUsername) {
        UserDto user = userService.findByName(deptUsername);
        if(Objects.nonNull(user)) {
            return new UnitConversionDto(user.getNickName());
        }
        return new UnitConversionDto();
    }

}
