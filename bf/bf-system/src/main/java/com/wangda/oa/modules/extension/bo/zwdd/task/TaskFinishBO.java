package com.wangda.oa.modules.extension.bo.zwdd.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午8:35
 */
@Data
@Builder
@ApiModel(description="完成待办任务")
public class TaskFinishBO {
    @ApiModelProperty(value = "用户ID",required = true)
    private String userId;
    @ApiModelProperty(value = "任务唯一ID，由创建待办接口返回")
    private String taskUuid;
    @ApiModelProperty(value = "必传,业务系统自定义ID",required = true)
    private String bizTaskId;

    @ApiModelProperty(value = "创建时间", hidden=true)
    @Builder.Default
    private Date createDate = new Date();

    @Tolerate
    public TaskFinishBO(){}

}
