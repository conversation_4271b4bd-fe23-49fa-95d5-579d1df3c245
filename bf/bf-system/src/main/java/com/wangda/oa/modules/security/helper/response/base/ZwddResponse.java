package com.wangda.oa.modules.security.helper.response.base;

import com.wangda.oa.modules.security.helper.response.data.abs.ZwddData;

public class ZwddResponse< T extends ZwddData> {
    private boolean success;
    private ZwddContent<T> content;
    private String bizErrorCode;

    public boolean isSuccess() {
        return success;
    }

    public ZwddContent<T> getContent() {
        return content;
    }

    public void setContent(ZwddContent<T> content) {
        this.content = content;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getBizErrorCode() {
        return bizErrorCode;
    }

    public void setBizErrorCode(String bizErrorCode) {
        this.bizErrorCode = bizErrorCode;
    }
}
