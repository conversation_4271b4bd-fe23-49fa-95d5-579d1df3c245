package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2021/5/24 下午7:16
 */
@Data
@Builder
@ApiModel(description="工作通知卡片消息-整体跳转")
public class WorkNotificationActionCardZtBO {

    @ApiModelProperty(value = "透出到会话列表和通知的文案，最长64个字符")
    private String title;
    @ApiModelProperty(value = "消息内容，目前不支持markdown，只能传纯文本")
    private String markdown;
    @ApiModelProperty(value = "使用整体跳转ActionCard样式时的标题，必须与single_url同时设置，最长20个字符")
    private String single_title;
    @ApiModelProperty(value = "移动端打开的url")
    private String single_url;
    @ApiModelProperty(value = "pc端打开的url")
    private String single_pc_url;
    @Tolerate
    WorkNotificationActionCardZtBO(){}
}
