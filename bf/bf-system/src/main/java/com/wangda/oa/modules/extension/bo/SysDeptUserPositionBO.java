package com.wangda.oa.modules.extension.bo;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午2:41
 */
@Data
public class SysDeptUserPositionBO extends BaseDomain {

    @ApiModelProperty(value = "部门id")
    private Long deptId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "手机号")
    private String phone;
}
