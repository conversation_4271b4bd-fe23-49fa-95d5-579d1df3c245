package com.wangda.oa.modules.extension.enums;

import com.alibaba.fastjson.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/11/8
 * 选人组件参数
 */
@AllArgsConstructor
@Getter
@ToString
@JSONType(serializeEnumAsJavaBean = true)
public enum UserSelectorTypeEnum {

    USER_PICKER("user-picker", "选人"),
    UNIT_PICKER("unit-picker", "选单位"),
    UNIT_USER_PICKER("unit-user-picker", "选本单位下人"),
    DEPT_USER_PICKER("dept-user-picker", "选本部门下人");

    private String value;
    private String name;

    public static UserSelectorTypeEnum getEnumByValue(String value) {
        for(UserSelectorTypeEnum e : UserSelectorTypeEnum.values()) {
            if(e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}
