package com.wangda.oa.modules.extension.dto.org;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/4/9 上午9:41
 */
@Data
public class UserListDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "姓名")
    private String nickName;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "唯一编号")
    private String extId;

    @ApiModelProperty(value = "类别")
    private Integer type = 0;
}
