package com.wangda.oa.modules.system.repository.query;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 用户名对应部门名称
 *
 * <AUTHOR>
 * @date 2021/2/24 14:16
 */
@Data
@AllArgsConstructor
public class UserDeptField {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * username
     */
    private String username;

    /**
     * 用户名称
     */
    private String realName;

    /**
     * 部门名称
     */
    private String groupName;
    private Long deptId;
}
