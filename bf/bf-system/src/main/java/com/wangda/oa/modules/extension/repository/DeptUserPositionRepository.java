package com.wangda.oa.modules.extension.repository;

import com.wangda.oa.modules.extension.domain.SysDeptUserPosition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午2:44
 */
public interface DeptUserPositionRepository extends JpaRepository<SysDeptUserPosition, Long>, JpaSpecificationExecutor<SysDeptUserPosition> {

    /**
     * 根据部门id、类型和排除当前编辑的记录查看数量
     * @param deptId 部门id
     * @param type 类型
     * @param id 主键
     * @return
     */
    int countByDeptIdAndTypeAndIdNot(Long deptId,String type,Long id);

    /**
     * 根据部门id、类型的记录查看数量
     * @param deptId 部门id
     * @param type 类型
     * @return
     */
    int countByDeptIdAndType(Long deptId,String type);

    /**
     * 根据部门id查询记录
     * @param deptId
     * @return
     */
    List<SysDeptUserPosition> findByDeptId(Long deptId);

    /**
     * 根据类型查询数据
     * @param type
     * @return
     */
    List<SysDeptUserPosition> findByTypeOrderBySortAsc(String type);

    /**
     * 根据类别、部门id查询记录
     * @param type
     * @param deptId
     * @return
     */
    List<SysDeptUserPosition> findByTypeAndDeptId(String type, Long deptId);

    /**
     * 根据类别、用户id查询记录
     * @param type
     * @param userId
     * @return
     */
    SysDeptUserPosition findFirstByTypeAndUserId(String type, Long userId);

    /**
     * 根据用和类别查找
     * @param userId
     * @param types
     * @return List
     */
    SysDeptUserPosition findFirstByUserIdAndTypeIn(Long userId, List<String> types);

    /**
     * 根据部门id和类型删除
     * @param deptId
     * @param type
     * @return
     */
    @Modifying
    @Transactional
    Integer deleteByDeptIdAndType(Long deptId,String type);

    @Query("select n.userId from SysDeptUserPosition n where n.deptId = :deptId and n.type =:type ")
    List<Long> findUsernameByDeptIdAndType(Long deptId,String type);

    /**
     * 根据类别查找
     * @param types
     * @return List
     */
    List<SysDeptUserPosition> findByTypeIn(List<String> types);
}
