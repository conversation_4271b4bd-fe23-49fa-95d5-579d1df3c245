package com.wangda.oa.modules.extension.service;

import com.wangda.oa.modules.extension.bo.ShortLinkQueryCriteria;
import com.wangda.oa.modules.extension.domain.ShortLink;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ShortLinkService {

    /**
     * 获取
     * @param code
     * @return
     */
    ShortLink get(String code);

    /**
     * 生成
     * @param url
     * @param expiryDay 有效期天数 0:不过期
     * @return
     */
    String create(String url, long expiryDay);

    /**
     * 查询列表
     * @return
     */
    Page queryAll(ShortLinkQueryCriteria criteria, Pageable pageable);

    /**
     * 保存
     * @return
     */
    void save(ShortLink shortLink);

    /**
     * 删除
     * @param ids
     */
    void delete(Set<Long> ids);

    /**
     * 修改长连接
     * @param longUrl
     * @param newLongUrl
     */
    void updateLongUrl(String longUrl, String newLongUrl);
}
