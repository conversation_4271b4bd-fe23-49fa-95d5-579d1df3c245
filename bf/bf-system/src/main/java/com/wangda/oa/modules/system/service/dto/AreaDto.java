package com.wangda.oa.modules.system.service.dto;

import com.wangda.oa.base.BaseDTO;
import com.wangda.oa.modules.system.domain.Area;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Author: caiyy
 * @Date: 2021/10/25
 */
@Getter
@Setter
@NoArgsConstructor
public class AreaDto extends BaseDTO implements Serializable {

    private Long id;

    private String cityCode;

    private String codeArea;

    private String name;

    private String center;

    private String level;

    private Long parentCode;

    public Area toEntity(){
        Area area = new Area();
        area.setParentCode(this.parentCode);
        area.setId(this.id);
        area.setName(this.name);
        area.setCenter(this.center);
        area.setLevel(this.level);
        area.setCityCode(this.cityCode);
        area.setCodeArea(this.codeArea);
        return area;
    }
}
