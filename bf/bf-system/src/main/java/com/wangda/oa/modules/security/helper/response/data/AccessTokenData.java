package com.wangda.oa.modules.security.helper.response.data;

import com.wangda.oa.modules.security.helper.response.data.abs.ZwddData;

public  class AccessTokenData extends ZwddData {

        private int expiresIn;

        public int getExpiresIn() {
            return expiresIn;
        }

        public void setExpiresIn(int expiresIn) {
            this.expiresIn = expiresIn;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        private String accessToken;
    }