package com.wangda.oa.modules.system.domain;

import com.wangda.oa.modules.system.domain.vo.TriggersId;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/8/4 上午10:36
 **/
@Entity
@Getter
@Setter
@Table(name="sys_users_roles")
@IdClass(TriggersId.class)
public class UserRole implements Serializable {

    @Id
    @Column(name = "user_id")
    private Long userId;

    @Id
    @Column(name = "role_id")
    private Long roleId;
}
