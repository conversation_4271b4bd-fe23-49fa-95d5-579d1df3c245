package com.wangda.oa.modules.extension.service.impl;

import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.repository.SysUserPlatformRepository;
import com.wangda.oa.modules.extension.service.SysUserPlatformService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: oa-mgt-server
 * @description: 浙政钉账户
 * @author: maogy
 * @create: 2021-07-02 16:10
 **/
@Service
@RequiredArgsConstructor
public class SysUserPlatformServiceImpl implements SysUserPlatformService {

    private final SysUserPlatformRepository sysUserPlatformRepository;

    @Override
    public SysUserPlatform add(SysUserPlatform sysUserPlatform) {
        return sysUserPlatformRepository.save(sysUserPlatform);
    }

    @Override
    public void deleteByUserNameAndType(String userName,String type) {
        sysUserPlatformRepository.deleteByUserNameAndType(userName, type);
    }

    @Override
    public SysUserPlatform queryByUserNameAndType(String name,String type) {
        return sysUserPlatformRepository.getFirstByUserNameAndType(name, type);
    }

    @Override
    public List<SysUserPlatform> queryByUsernameInAndType(List<String> usernames, String type) {
        return sysUserPlatformRepository.findByUserNameInAndType(usernames, type);
    }

    @Override
    public SysUserPlatform update(SysUserPlatform update) {
        SysUserPlatform sysUserPlatform = sysUserPlatformRepository.getFirstByUserNameAndType(update.getUserName(),update.getType());
        if(sysUserPlatform !=null){
            sysUserPlatform.copy(update);
            return sysUserPlatformRepository.save(sysUserPlatform);
        }else{
            return sysUserPlatformRepository.save(update);
        }
    }

    @Override
    public String queryUsernameByPlatformUserId(String platformUserId) {
        return  sysUserPlatformRepository.findUsernameByPlatformUserId(platformUserId);
    }

    @Override
    public List<SysUserPlatform> findByTypeAndPlatformUserIdIsNotNull(String type) {
        return sysUserPlatformRepository.findByTypeAndPlatformUserIdIsNotNull(type);
    }

    @Override
    public SysUserPlatform findByPlatformUserNameAndType(String platformUserName, String type) {
        return sysUserPlatformRepository.getFirstByPlatformUserNameAndType(platformUserName, type);
    }
}
