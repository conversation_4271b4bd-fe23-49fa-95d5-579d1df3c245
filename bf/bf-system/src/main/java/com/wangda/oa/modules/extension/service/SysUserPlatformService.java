package com.wangda.oa.modules.extension.service;

import com.wangda.oa.modules.extension.domain.SysUserPlatform;

import java.util.List;

/**
* Description: 浙政钉编号
* @Date: 2021/7/2 15:58
* @Author: maogy
* @throws:
*/
public interface SysUserPlatformService {

    /**
    * Description: 新增
    * @param sysUserPlatform
    * @return: void
    * @Date: 2021/7/2 16:01
    * @Author: maogy
    * @throws:
    */
    SysUserPlatform add(SysUserPlatform sysUserPlatform);

    /**
    * Description: 删除
    * @param userName
* @param type
    * @return: void
    * @Date: 2021/7/2 16:46
    * @Author: maogy
    * @throws:
    */
    void deleteByUserNameAndType(String userName,String type);

    /**
    * Description: 查询
    * @param
    * @return: com.wangda.oa.modules.oa.domain.SysUserPlatform
    * @Date: 2021/7/2 16:01
    * @Author: maogy
    * @throws:
    */
    SysUserPlatform queryByUserNameAndType(String name,String type);

    /**
     * 根据用户和类别查询
     * @param usernames
     * @param type
     * @return List
     */
    List<SysUserPlatform> queryByUsernameInAndType(List<String> usernames, String type);

    /**
    * Description: 更新
    * @param sysUserPlatform
    * @return: com.wangda.oa.modules.oa.domain.SysUserPlatform
    * @Date: 2021/7/2 16:50
    * @Author: maogy
    * @throws:
    */
    SysUserPlatform  update(SysUserPlatform sysUserPlatform);
    String queryUsernameByPlatformUserId(String platformUserId);

    /**
     * 根据类别获取已绑定平台用户
     * @param type
     * @return List
     */
    List<SysUserPlatform> findByTypeAndPlatformUserIdIsNotNull(String type);

    /**
     * 根据平台名称和类别查询
     * @param platformUserName
     * @param type
     * @return
     */
    SysUserPlatform findByPlatformUserNameAndType(String platformUserName, String type);
}
