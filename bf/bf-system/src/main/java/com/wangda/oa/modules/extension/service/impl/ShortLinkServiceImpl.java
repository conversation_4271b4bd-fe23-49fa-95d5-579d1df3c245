package com.wangda.oa.modules.extension.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.wangda.oa.modules.extension.bo.ShortLinkQueryCriteria;
import com.wangda.oa.modules.extension.domain.ShortLink;
import com.wangda.oa.modules.extension.repository.ShortLinkRepository;
import com.wangda.oa.modules.extension.service.ShortLinkService;
import com.wangda.oa.modules.system.utils.StdRandom;
import com.wangda.oa.utils.QueryHelp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service
public class ShortLinkServiceImpl implements ShortLinkService {

    @Resource
    ShortLinkRepository shortLinkRepository;

    @Override
    public ShortLink get(String code) {
        return shortLinkRepository.getByCodeAndTimeOutAfterOrTimeOutIsNull(code, LocalDateTime.now());
    }

    @Override
    @Transactional
    public String create(String url, long expiryDay) {

        String code = StdRandom.randomStr(6);
        ShortLink exits = shortLinkRepository.getByCode(code);

        if (exits != null) {
            this.create(url, expiryDay);
        }

        StringBuffer urlConvert;
        try {
            urlConvert = convertCn(url);
            ShortLink shortLink = new ShortLink();
            shortLink.setCode(code);
            shortLink.setLongUrl(urlConvert.toString());

            LocalDateTime now = LocalDateTime.now();
            shortLink.setCreateTime(now);

            // 设置一天后过期
            if(expiryDay > 0) {
                shortLink.setTimeOut(now.plusDays(expiryDay));
            }

            shortLinkRepository.save(shortLink);
            return code;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Page queryAll(ShortLinkQueryCriteria criteria, Pageable pageable) {
        return shortLinkRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(ShortLink shortLink) {
        if(StringUtils.isEmpty(shortLink.getCode())) {
            String code = StdRandom.randomStr(6);
            ShortLink exits = shortLinkRepository.getByCode(code);
            if (exits != null) {
                this.save(shortLink);
                return;
            }
            shortLink.setCode(code);
        }
        if(Objects.isNull(shortLink.getCreateTime())) {
            shortLink.setCreateTime(LocalDateTime.now());
        }

        try {
            StringBuffer urlConvert = convertCn(shortLink.getLongUrl());
            shortLink.setLongUrl(urlConvert.toString());
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        if(StringUtils.isEmpty(shortLink.getCode())) {
            shortLinkRepository.save(shortLink);
        }else {
            ShortLink resources = shortLinkRepository.getByCode(shortLink.getCode());
            if(Objects.nonNull(resources)) {
                BeanUtil.copyProperties(shortLink, resources, CopyOptions.create().ignoreNullValue());
                shortLinkRepository.save(resources);
            }else {
                shortLinkRepository.save(shortLink);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Set<Long> ids) {
        shortLinkRepository.deleteAllByIdIn(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLongUrl(String longUrl, String newLongUrl) {
        shortLinkRepository.updateLongUrl(longUrl, newLongUrl);
    }

    /**
     * 中文字符转义
     */
    private StringBuffer convertCn(String url) throws UnsupportedEncodingException {
        String regx = "[\u4e00-\u9fa5]+";
        Pattern p = Pattern.compile(regx);
        Matcher m = p.matcher(url);
        StringBuffer urlConvert = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(urlConvert, URLEncoder.encode(m.group(0), "UTF-8"));
        }
        m.appendTail(urlConvert);
        return urlConvert;
    }

}
