package com.wangda.oa.config;

import com.baomidou.kisso.web.WebKissoConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/25
 * @description kisso配置
 */
@Configuration
public class KissoConfigurer {

    @Bean
    public void KissoInit() {
        new WebKissoConfigurer().initKisso();
    }

}
