package com.wangda.oa.modules.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/4/1 上午11:41
 */
@Data
public class ChildUserVO {

    @ApiModelProperty(value = "单位账号username")
    @NotBlank(message = "账号username必传")
    private String deptUsername;
}
