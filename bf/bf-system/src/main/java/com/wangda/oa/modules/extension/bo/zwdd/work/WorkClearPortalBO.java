package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午5:16
 */
@Data
@Builder
@ApiModel(description="清除工作台红点")
public class WorkClearPortalBO {
    @ApiModelProperty(value = "业务消息id，自定义，逗号分隔，不传为清空用户下所以消息")
    private String bizMsgIds;
    @ApiModelProperty(value = "接收人id",required = true)
    private Long accountId;

    @Tolerate
    WorkClearPortalBO(){}
}
