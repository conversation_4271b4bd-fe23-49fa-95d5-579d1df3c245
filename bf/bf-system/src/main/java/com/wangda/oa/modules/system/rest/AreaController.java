package com.wangda.oa.modules.system.rest;

import com.wangda.oa.modules.system.service.AreaService;
import com.wangda.oa.modules.system.service.dto.AreaQueryCriteria;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;

/**
 * @Description: 用于管理省市信息
 * @Author: caiyy
 * @Date: 2021/10/25
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "省市信息")
@RequestMapping("/api/areaInformation")
public class AreaController {

    private final AreaService areaService;

    @ApiOperation("获得json格式的地区信息")
    @GetMapping("/getAreaToJson")
    public ResponseEntity<Object> getAreaToJson(String parentCode, Integer rank) {
        LinkedHashMap<String, Object> map = areaService.getAreaJson(parentCode, rank);
        return new ResponseEntity<>(map.values(), HttpStatus.OK);
    }

    @ApiOperation("获得json格式的地区信息(市县级)")
    @GetMapping("/getCityDistrictToJson")
    public ResponseEntity<Object> getCityDistrictToJson(String code) {
        LinkedHashMap<String, Object> map = areaService.getCityDistrictToJson(code);
        return new ResponseEntity<>(map.values(), HttpStatus.OK);
    }

    @ApiOperation("更新数据")
    @PostMapping("/reloadArea")
    public ResponseEntity<Object> reloadArea() {
        areaService.reloadArea();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("条件查询")
    @GetMapping
    public ResponseEntity<Object> query(AreaQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(areaService.query(criteria, pageable), HttpStatus.OK);
    }
}
