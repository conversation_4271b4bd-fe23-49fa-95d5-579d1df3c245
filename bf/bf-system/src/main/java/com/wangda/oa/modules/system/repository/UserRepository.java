/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.repository;

import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.domain.vo.UserVo;
import com.wangda.oa.modules.system.repository.query.UserDeptField;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> Jie
 * @date 2018-11-22
 */
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 查询用户部门
     */
    @Query("SELECT new com.wangda.oa.modules.system.repository.query.UserDeptField(u.id, u.username, u.nickName, g.name,g.id) FROM User u, Dept g WHERE u.dept.id = g.id and u.username = :username")
    UserDeptField findUserAndDept(String username);

    /**
     * 查询用户
     */
    @Query("SELECT new com.wangda.oa.modules.system.repository.query.UserDeptField(u.id, u.username, u.nickName, g.name,g.id) FROM User u, Dept g WHERE u.dept.id = g.id and u.enabled = true and u.nickName like :nickName")
    List<UserDeptField> findUserByNickName(String nickName);

    List<User> findByUsernameIn(List<String> usernames);

    /**
     * 根据用户名查询
     * @param username 用户名
     * @return /
     */
    User findByUsername(String username);

    /**
     * 根据用户姓名查询
     * @param nickName
     * @return
     */
    List<User> findByNickNameAndEnabled(String nickName, Boolean enabled);

    /**
     * 根据用户姓名模糊查询
     * @param nickName
     * @return
     */
    List<User> findByNickNameLikeAndEnabled(String nickName, Boolean enabled);

    /**
     * 根据用户姓名查询
     * @param nickName
     * @return
     */
    List<User> findByNickNameLike(String nickName);

    /**
     * 根据用户姓名查询
     * @param nickName
     * @return
     */
    List<User> findByNickName(String nickName);

    /**
     * 根据邮箱查询
     * @param email 邮箱
     * @return /
     */
    User findByEmail(String email);

    /**
     * 修改密码
     * @param username              用户名
     * @param pass                  密码
     * @param lastPasswordResetTime /
     */
    @Modifying
    @Query(value = "update sys_user set password = ?2 , pwd_reset_time = ?3 where username = ?1", nativeQuery = true)
    void updatePass(String username, String pass, Date lastPasswordResetTime);

    /**
     * 修改邮箱
     * @param username 用户名
     * @param email    邮箱
     */
    @Modifying
    @Query(value = "update sys_user set email = ?2 where username = ?1", nativeQuery = true)
    void updateEmail(String username, String email);

    /**
     * 禁用账号
     * @param username
     */
    @Modifying
    @Query("update User u set u.enabled = false where u.username=:username")
    void disableUser(String username);

    /**
     * 根据角色查询用户
     * @param roleId /
     * @return /
     */
    @Query(value = "SELECT u.* FROM sys_user u, sys_users_roles r WHERE" +
            " u.user_id = r.user_id AND r.role_id = ?1", nativeQuery = true)
    List<User> findByRoleId(Long roleId);

    /**
     * 根据角色查询用户*(分页)
     * @param roleId /
     * @return /
     */
    @Query(value = "SELECT u.* FROM sys_user u, sys_users_roles r WHERE" +
            " u.user_id = r.user_id AND r.role_id = ?1", nativeQuery = true)
    Page<User> findPageByRoleId(Long roleId, Pageable pageable);

    /**
     * 根据角色中的部门查询
     * @param id /
     * @return /
     */
    @Query(value = "SELECT u.* FROM sys_user u, sys_users_roles r, sys_roles_depts d WHERE " +
            "u.user_id = r.user_id AND r.role_id = d.role_id AND r.role_id = ?1", nativeQuery = true)
    List<User> findByDeptRoleId(Long id);

    /**
     * 根据菜单查询
     * @param id 菜单ID
     * @return /
     */
    @Query(value = "SELECT u.* FROM sys_user u, sys_users_roles ur, sys_roles_menus rm WHERE\n" +
            "u.user_id = ur.user_id AND ur.role_id = rm.role_id AND rm.menu_id = ?1", nativeQuery = true)
    List<User> findByMenuId(Long id);

    /**
     * 根据Id删除
     * @param ids /
     */
    void deleteAllByIdIn(Set<Long> ids);

    /**
     * 根据岗位查询
     * @param ids /
     * @return /
     */
    @Query(value = "SELECT count(1) FROM sys_user u, sys_users_jobs j WHERE u.user_id = j.user_id AND j.job_id IN ?1", nativeQuery = true)
    int countByJobs(Set<Long> ids);

    /**
     * 根据部门查询
     * @param deptIds /
     * @return /
     */
    @Query(value = "SELECT count(1) FROM sys_user u WHERE u.dept_id IN ?1", nativeQuery = true)
    int countByDepts(Set<Long> deptIds);

    /**
     * 根据角色查询
     * @param ids /
     * @return /
     */
    @Query(value = "SELECT count(1) FROM sys_user u, sys_users_roles r WHERE " +
            "u.user_id = r.user_id AND r.role_id in ?1", nativeQuery = true)
    int countByRoles(Set<Long> ids);

    /**
     * 查询部门下的可用人员
     * @param deptId
     * @return
     */
    @Query(value = "SELECT username FROM sys_user u WHERE u.dept_id = ?1 and enabled=1", nativeQuery = true)
    List<String> getUsernameByDeptId(String deptId);

    @Query(value = "SELECT new com.wangda.oa.modules.system.domain.vo.UserVo(u.id,u.username,u.nickName,u.dept.id) FROM User u WHERE u.username IN ?1 ")
    List<UserVo> findAllByUsernameIn(String[] usernames);

    /**
     * 根据对应流程的key来查询用户
     * @param authorityKey
     * @return
     */
    @Query(value = "SELECT u.* FROM sys_role r, sys_users_roles ur, sys_user u WHERE " +
            "r.role_id = ur.role_id AND u.user_id = ur.user_id AND r.authority_key LIKE CONCAT('%',?1,'%')", nativeQuery = true)
    List<User> findUsersByAuthorityKey(String authorityKey);

    List<User> findByDeptIdAndEnabledAndUserType(Long deptId, Boolean enabled, Integer userType);

    List<User> findByDeptIdInAndEnabledAndUserTypeOrderBySortAsc(List<Long> deptIds, Boolean enabled, Integer userType);

    List<User> findByEnabledAndUserType(Boolean enabled, Integer userType);

    List<User> findByEnabledAndUserTypeAndNickNameLike(Boolean enabled, Integer userType, String nickName);

    /**
     * Description: 查询角色关联的所有用户
     * @param roleId
     * @return: java.util.List<com.wangda.oa.modules.system.domain.User>
     * @Date: 2021/12/5 13:05
     * @Author: maogy
     * @throws:
     */
    @Query(value = "select u.id as id,u.nickName as nickName,u.username as userName from User u, UserRole r WHERE" +
            " u.id = r.userId and r.roleId = :roleId")
    List<Map<String, Object>> findUsersByRoleId(Long roleId);


    @Query(value = "select count(1) from sys_users_roles where USER_ID = ?1 AND ROLE_ID = (select role_id from sys_role WHERE sys_role.name=?2)", nativeQuery = true)
    Integer checkWhetherUnitAccount(Long id, String roleName);

    /**
     * Description: 修改电话号码
     * @param username
     * @param phone
     * @Date: 2022/1/6 19:06
     * @Author: maogy
     * @throws:
     */
    @Modifying
    @Query(value = "update sys_user set phone = ?2 where username = ?1", nativeQuery = true)
    int updatePhone(String username, String phone);

    /**
     * 根据手机号查询用户
     * @param phone
     * @return
     */
    User findFirstByPhone(String phone);
}
