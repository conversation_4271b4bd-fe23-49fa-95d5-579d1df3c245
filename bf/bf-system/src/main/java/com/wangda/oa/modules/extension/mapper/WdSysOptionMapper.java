package com.wangda.oa.modules.extension.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.wangda.oa.modules.extension.domain.WdSysOption;
import com.wangda.oa.modules.extension.dto.LeadershipListDto;
import com.wangda.oa.modules.extension.dto.UserDataDto;
import com.wangda.oa.modules.extension.dto.org.UserListDto;
import com.wangda.oa.modules.extension.dto.org.UserPhoneListDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WdSysOptionMapper extends BaseMapper<WdSysOption> {

    /**
     * 查询正常的用户数据(为了不查询角色岗位部门额外信息)
     * @return
     */
    List<UserDataDto> getUserListByEnabled();

    /**
     * 根据用户名查询用户集合
     * @param name
     * @return
     */
    List<UserListDto> getUserList(@Param("name") String name);

    /**
     * 根据用户名查询用户集合
     * @param name
     * @return
     */
    List<UserPhoneListDto> getUserPhoneList(@Param("name") String name);

    /**
     * 查询正常的用户数据和对应的岗位
     * @return
     */
    List<UserDataDto> getUserAndFirstJobList();

    /**
     * 查询厅领导下用户信息
     * @return
     */
    List<LeadershipListDto> getLeadershipList(@Param("username") String username);

    @Select("SELECT id, key_type AS \"key\", name, value, expired,version,create_date,modified_date,creator_id,modified_id,enabled FROM wd_sys_option ${ew.customSqlSegment}")
    IPage<WdSysOption> selectPage(WdSysOption page, @Param(Constants.WRAPPER)Wrapper<WdSysOption> queryWrapper);
}
