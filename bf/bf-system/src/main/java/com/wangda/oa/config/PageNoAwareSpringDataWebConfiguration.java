package com.wangda.oa.config;

import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.web.config.SpringDataWebConfiguration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;

import java.util.List;

@Configuration
public class PageNoAwareSpringDataWebConfiguration extends SpringDataWebConfiguration {

    public PageNoAwareSpringDataWebConfiguration(ApplicationContext context,
            @Qualifier("mvcConversionService") ObjectFactory<ConversionService> conversionService) {
        super(context, conversionService);
    }

    @Bean
    @Override
    public PageNoHandlerMethodArgumentResolver pageableResolver() {

        PageNoHandlerMethodArgumentResolver pageableResolver = new PageNoHandlerMethodArgumentResolver(
                sortResolver());
        customizePageableResolver(pageableResolver);
        return pageableResolver;
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        super.addArgumentResolvers(argumentResolvers);
        argumentResolvers.add(pageableResolver());
    }
}
