package com.wangda.oa.modules.system.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

/**
 * @program: oa-mgt-server
 * @description: 用户角色
 * @author: maogy
 * @create: 2021-12-04 12:32
 **/
@Getter
@Setter
public class UserRoleDto {


    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "关联人员")
    private Set<SimpleUserDto> roleUsers;
}
