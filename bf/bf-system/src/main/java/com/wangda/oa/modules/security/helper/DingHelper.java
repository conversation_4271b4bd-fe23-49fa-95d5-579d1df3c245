package com.wangda.oa.modules.security.helper;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.GetClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.security.helper.response.base.ZwddResponse;
import com.wangda.oa.modules.security.helper.response.data.AccessTokenData;
import com.wangda.oa.modules.security.helper.response.data.UserInfoData;
import com.wangda.oa.utils.SpringContextHolder;

public class DingHelper {

    private ExecutableClient executableClient;
    private static String appKey=SpringContextHolder.getProperties("zwdd.loginAppKey");;
    private static String appsecret=SpringContextHolder.getProperties("zwdd.loginAppSecret");

    private DingHelper() {
        executableClient = ExecutableClient.getInstance();
        ZwddProperties properties = SpringContextHolder.getBean(ZwddProperties.class);

        executableClient.setAccessKey(properties.getAccessKey());
        executableClient.setSecretKey(properties.getSecretKey());
        executableClient.setDomainName(properties.getDomainName());
        executableClient.setProtocal(properties.getProtocal());
        executableClient.init();
    }

    private static class InstanceHolder {

        private static final DingHelper INSTANCE = new DingHelper();
    }

    private static ExecutableClient getInstance() {
        return InstanceHolder.INSTANCE.executableClient;
    }

    public static String getAccessToken() {
        ExecutableClient executableClient = getInstance();
        String api = "/gettoken.json";
        GetClient getClient = executableClient.newGetClient(api);
        //设置参数
        getClient.addParameter("appkey", appKey);
        getClient.addParameter("appsecret", appsecret);
        //调用API
        String apiResult = getClient.get();
        ZwddResponse<AccessTokenData> accessTokenDataZwddResponse = JSONObject.parseObject(apiResult, new TypeReference<ZwddResponse<AccessTokenData>>() {

        });
        AccessTokenData data = accessTokenDataZwddResponse.getContent().getData();
        return data.getAccessToken();
    }

    public static Long getUserInfo(String code) {
        ExecutableClient instance = getInstance();
        PostClient postClient = instance.newPostClient("/rpc/oauth2/getuserinfo_bycode.json");
        postClient.addParameter("access_token", getAccessToken());
        postClient.addParameter("code", code);
        String apiResult = postClient.post();
        ZwddResponse<UserInfoData> userInfoDataZwddResponse = JSONObject.parseObject(apiResult, new TypeReference<ZwddResponse<UserInfoData>>() {

        });
        return userInfoDataZwddResponse.getContent().getData().getAccountId();
    }
}
