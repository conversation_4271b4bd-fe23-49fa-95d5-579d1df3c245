package com.wangda.oa.modules.system.service.mapstruct;

import com.wangda.oa.base.BaseMapper;
import com.wangda.oa.modules.system.domain.Area;
import com.wangda.oa.modules.system.service.dto.AreaDto;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2021/10/25
 */
@Mapper(componentModel = "spring", uses = {AreaMapper.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AreaMapper extends BaseMapper<AreaDto, Area> {
}
