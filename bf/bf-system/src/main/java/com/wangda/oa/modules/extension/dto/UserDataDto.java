package com.wangda.oa.modules.extension.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午6:41
 */
@Data
public class UserDataDto {

    @ApiModelProperty(value = "ID")
    private Long userId;

    @ApiModelProperty(value = "用户部门id")
    private Long deptId;

    @ApiModelProperty(value = "用户名称")
    private String username;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "电话号码")
    private String phone;

    @ApiModelProperty(value = "用户性别")
    private String gender;

    @ApiModelProperty(value = "是否为admin账号")
    private Boolean isAdmin;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape= JsonFormat.Shape.STRING,pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "岗位")
    private String post;

    @ApiModelProperty(value = "排序")
    private Integer sort;
    private List<Long>concurDept;
    private String deptName;

}
