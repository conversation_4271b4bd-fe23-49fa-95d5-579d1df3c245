package com.wangda.oa.modules.system.service;

import com.wangda.oa.modules.system.domain.UserSettings;
import com.wangda.oa.modules.system.service.dto.UserSettingsQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
* Description: 用户个性化配置
* @Date: 2021/12/3 17:21
* @Author: maogy
*/
public interface UserSettingsService {

    /**
     * Description: 分页查询
     * @param queryCriteria
     * @param pageable
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     * @Date: 2021/10/27 17:07
     * @Author: maogy
     * @throws:
     */
    Map<String,Object> queryPage(UserSettingsQueryCriteria queryCriteria, Pageable pageable);


    /**
     * Description: 新增
     * @param userSettings
     * @return: com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.entity.D06Goods
     * @Date: 2021/10/27 17:09
     * @Author: maogy
     * @throws:
     */
    UserSettings add(UserSettings userSettings);


    /**
     * Description: 修改
     * @param userSettings
     * @return: com.hzwangda.aigov.modules.zjedu.goodsmanagement.domain.entity.D06Goods
     * @Date: 2021/10/27 17:09
     * @Author: maogy
     * @throws:
     */
    UserSettings update(UserSettings userSettings);



    /**
     * Description: 批量删除(支持单条记录删除)
     * @param ids
     * @return: void
     * @Date: 2021/10/27 17:08
     * @Author: maogy
     * @throws:
     */
    void deleteByIds(Long[] ids);

    /**
     * Description: 根据用户名查找数据
     * @param userName
     * @return: com.wangda.oa.modules.system.domain.UserSettings
     * @Date: 2021/12/3 19:17
     * @Author: maogy
     * @throws:
     */
    UserSettings findByUserName(String userName);

}
