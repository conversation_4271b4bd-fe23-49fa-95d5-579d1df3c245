package com.wangda.oa.modules.extension.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "wd_line")
public class WdLine extends BaseDomain {

    @Column(name = "type")
    @ApiModelProperty(value = "类型(0:人员,1:部门)")
    private Integer type;

    @Column(name = "key_id")
    @ApiModelProperty(value = "实体id(根据类型不同存储不同id)")
    private Long keyId;

    @Column(name = "name")
    @ApiModelProperty(value = "名称")
    private String name;

    @Column(name = "pid")
    @ApiModelProperty(value = "父类id")
    private Long pid;

    @Column(name = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

}
