/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.service;

import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.modules.system.domain.ChildUser;
import com.wangda.oa.modules.system.domain.vo.ChildUserVO;
import com.wangda.oa.modules.system.service.dto.ChildUserDto;
import com.wangda.oa.modules.system.service.dto.ChildUserQueryCriteria;
import com.wangda.oa.modules.system.service.dto.UserQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @data 2022/3/31 16:11
 */
public interface ChildUserService {

    /**
     * 根据ID查询
     * @param id ID
     * @return /
     */
    ChildUserDto findById(long id);

    /**
     * 新增子用户
     * @param resources /
     */
    void create(ChildUser resources);

    /**
     * 编辑子用户
     * @param resources /
     */
    void update(ChildUser resources) throws Exception;

    /**
     * 删除子用户
     * @param ids /
     */
    void delete(Set<Long> ids);

    /**
     * 根据子用户名查询
     * @param userName /
     * @return /
     */
    ChildUserDto findByName(String userName);

    /**
     * 查询全部
     * @param criteria 条件
     * @param pageable 分页参数
     * @return /
     */
    Object queryAll(ChildUserQueryCriteria criteria, Pageable pageable);

    /**
     * 查询全部不分页
     * @param criteria 条件
     * @return /
     */
    List<ChildUserDto> queryAll(UserQueryCriteria criteria);

    /**
     * 查询对应单位账号下的子用户
     * @param vo
     * @return
     */
    ResultJson<List<ChildUserDto>> getChildUserListByPid(ChildUserVO vo);

    /**
     * 导出数据
     * @param queryAll 待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ChildUserDto> queryAll, HttpServletResponse response) throws IOException;

    /**
     * 修改密码
     * @param username        用户名
     * @param encryptPassword 密码
     */
    void updatePass(String username, String encryptPassword);

    /**
     * 新增关联账号
     * @param resources
     */
    void createAssociatedAccount(ChildUserDto resources);

}
