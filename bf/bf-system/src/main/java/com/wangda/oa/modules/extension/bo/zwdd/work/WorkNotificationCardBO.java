package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2021/5/24 下午7:16
 */
@Data
@Builder
@ApiModel(description="工作通知卡片消息")
public class WorkNotificationCardBO {

    @ApiModelProperty(value = "卡片类型,type为3时使用.0:整体跳转，使用cardZtBO对象,1:独立跳转，使用cardDlBO对象")
    private int actionCardType;
    @ApiModelProperty(value = "卡片类型整体对象")
    private WorkNotificationActionCardZtBO cardZtBO;
    @ApiModelProperty(value = "卡片类型独立对象")
    private WorkNotificationActionCardDlBO cardDlBO;

    @Tolerate
    public WorkNotificationCardBO(){}
}
