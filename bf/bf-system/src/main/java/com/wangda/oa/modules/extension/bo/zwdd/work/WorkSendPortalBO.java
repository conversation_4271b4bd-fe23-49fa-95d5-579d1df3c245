package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午5:16
 */
@Data
@Builder
@ApiModel(description="工作台红点数")
public class WorkSendPortalBO {
    @ApiModelProperty(value = "业务消息id，自定义，逗号分隔（和addNum二选一）")
    private String bizMsgIds;
    @ApiModelProperty(value = "新增红点数（和bizMsgIds二选一）")
    private Long addNum;
    @ApiModelProperty(value = "接收人id",required = true)
    private Long accountId;

    @Tolerate
    WorkSendPortalBO(){}
}
