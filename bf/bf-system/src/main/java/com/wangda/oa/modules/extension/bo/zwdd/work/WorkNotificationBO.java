package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/11 下午5:16
 */
@Data
@Builder
@ApiModel(description="工作通知")
public class WorkNotificationBO {

    @ApiModelProperty(value = "接收者的部门id列表， 接收者是部门id下(包括子部门下)的所有用户，与receiverIds都为空时不发送，最大长度列表跟receiverIds加起来不大于1000")
    private String organizationCodes;

    @ApiModelProperty(value = "接收人用户ID(accountId)， 多个人时使用半角逗号分隔，与organizationCodes都为空时不发送，最大长度列表跟organizationCodes加起来不大于1000")
    private String receiverIds;

    @ApiModelProperty(value = "业务消息id，自定义，有去重功能 调用者的业务数据ID，同样的ID调用多次会提示\"重复\"错误")
    private String bizMsgId;

    @ApiModelProperty(value = "类型，默认文本类型(0:text,文本消息、传textBO对象,1:link,链接消息、传linkBO对象,2:oa,oa消息、传oaBO对象,3:action_card,卡片消息，看卡片类型传对应对象)",required = true)
    private int type;
    @ApiModelProperty(value = "文本类型对象")
    private WorkNotificationTextBO textBO;
    @ApiModelProperty(value = "链接类型对象")
    private WorkNotificationLinkBO linkBO;
    @ApiModelProperty(value = "OA类型对象")
    private WorkNotificationOaBO oaBO;
    @ApiModelProperty(value = "卡片类型对象")
    private WorkNotificationCardBO cardBO;

    @ApiModelProperty(value = "创建时间", hidden=true)
    @Builder.Default
    private Date createDate = new Date();

    @Tolerate
    public WorkNotificationBO(){}

    @Override
    public boolean equals(Object object) {
        if (this == object) return true;
        if (!(object instanceof WorkNotificationBO)) return false;
        WorkNotificationBO that = (WorkNotificationBO) object;
        return type == that.type &&
                Objects.equals(organizationCodes, that.organizationCodes) &&
                Objects.equals(receiverIds, that.receiverIds) &&
                Objects.equals(bizMsgId, that.bizMsgId) &&
                Objects.equals(textBO, that.textBO) &&
                Objects.equals(linkBO, that.linkBO) &&
                Objects.equals(oaBO, that.oaBO) &&
                Objects.equals(cardBO, that.cardBO);
    }

    @Override
    public int hashCode() {
        return Objects.hash(organizationCodes, receiverIds, bizMsgId, type, textBO, linkBO, oaBO, cardBO);
    }
}
