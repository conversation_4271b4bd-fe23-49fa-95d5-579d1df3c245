/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.rest;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.config.RsaProperties;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.service.SysUserPlatformService;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.domain.vo.UserPassVo;
import com.wangda.oa.modules.system.exception.CustomException;
import com.wangda.oa.modules.system.service.*;
import com.wangda.oa.modules.system.service.dto.RoleSmallDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.dto.UserQueryCriteria;
import com.wangda.oa.modules.system.service.dto.UserRoleQueryCriteria;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.RsaUtils;
import com.wangda.oa.utils.SecurityUtils;
import com.wangda.oa.utils.StringUtils;
import com.wangda.oa.utils.enums.CodeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Jie
 * @date 2018-11-23
 */
@Api(tags = "系统：用户管理")
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {

    private final PasswordEncoder passwordEncoder;
    private final UserService userService;
    private final DataService dataService;
    private final DeptService deptService;
    private final RoleService roleService;
    private final VerifyService verificationCodeService;
    private final SysUserPlatformService sysUserPlatformService;
    private final ZwddProperties zwddProperties;

    @Log("导出用户数据")
    @ApiOperation("导出用户数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('user:list')")
    public void download(HttpServletResponse response, UserQueryCriteria criteria) throws IOException {
        userService.download(userService.queryAll(criteria), response);
    }

    @ApiOperation("查询用户")
    @GetMapping
    @PreAuthorize("@el.check('user:list')")
    public ResponseEntity<Object> query(UserQueryCriteria criteria, Pageable pageable) {
        if(!ObjectUtils.isEmpty(criteria.getDeptId())) {
            criteria.getDeptIds().add(criteria.getDeptId());
            criteria.getDeptIds().addAll(deptService.getDeptChildren(criteria.getDeptId(),
                    deptService.findByPid(criteria.getDeptId())));
        }
        // 数据权限
        List<Long> dataScopes = dataService.getDeptIds(userService.findByName(SecurityUtils.getCurrentUsername()));
        // criteria.getDeptIds() 不为空并且数据权限不为空则取交集
        if(!CollectionUtils.isEmpty(criteria.getDeptIds()) && !CollectionUtils.isEmpty(dataScopes)) {
            // 取交集
            criteria.getDeptIds().retainAll(dataScopes);
            if(!CollectionUtil.isEmpty(criteria.getDeptIds())) {
                return new ResponseEntity<>(userService.queryAll(criteria, pageable), HttpStatus.OK);
            }
        }else {
            // 否则取并集
            criteria.getDeptIds().addAll(dataScopes);
            return new ResponseEntity<>(userService.queryAll(criteria, pageable), HttpStatus.OK);
        }
        return new ResponseEntity<>(PageUtil.toPage(null, 0), HttpStatus.OK);
    }

    @Log("新增用户")
    @ApiOperation("新增用户")
    @PostMapping
    @PreAuthorize("@el.check('user:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody User resources) {
        checkLevel(resources);
        // 默认密码 Aa123456
        resources.setPassword(passwordEncoder.encode("Aa123456"));
        userService.create(resources);
        //新增第三方用户
        if(resources.getPlatformUserId() != null || resources.getPlatformUserName() != null) {
            SysUserPlatform sysUserPlatform = new SysUserPlatform();
            sysUserPlatform.setPlatformUserId(resources.getPlatformUserId());
            sysUserPlatform.setUserName(resources.getUsername());
            sysUserPlatform.setPlatformUserName(StringUtils.isNotEmpty(resources.getPlatformUserName())? resources.getPlatformUserName(): resources.getNickName());
            sysUserPlatform.setType(zwddProperties.getType());
            sysUserPlatformService.add(sysUserPlatform);
        }
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @Log("修改用户")
    @ApiOperation("修改用户")
    @PutMapping
    @PreAuthorize("@el.check('user:edit')")
    public ResponseEntity<Object> update(@Validated(User.Update.class) @RequestBody User resources) throws Exception {
        checkLevel(resources);
        userService.update(resources);
        //修改绑定浙政钉用户信息
        if(resources.getPlatformUserId() != null || resources.getPlatformUserName() != null) {
            SysUserPlatform sysUserPlatform = new SysUserPlatform();
            sysUserPlatform.setType(zwddProperties.getType());
            sysUserPlatform.setUserName(resources.getUsername());
            sysUserPlatform.setPlatformUserName(StringUtils.isNotEmpty(resources.getPlatformUserName())? resources.getPlatformUserName(): resources.getNickName());
            sysUserPlatform.setPlatformUserId(resources.getPlatformUserId());
            sysUserPlatformService.update(sysUserPlatform);
        }
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("修改用户：个人中心")
    @ApiOperation("修改用户：个人中心")
    @PutMapping(value = "center")
    public ResponseEntity<Object> center(@Validated(User.Update.class) @RequestBody User resources) {
        if(!resources.getId().equals(SecurityUtils.getCurrentUserId())) {
            throw new BadRequestException("不能修改他人资料");
        }
        userService.updateCenter(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除用户")
    @ApiOperation("删除用户")
    @DeleteMapping
    @PreAuthorize("@el.check('user:del')")
    public ResponseEntity<Object> delete(@RequestBody Set<Long> ids) {
        for(Long id : ids) {
            Integer currentLevel = Collections.min(roleService.findByUsersId(SecurityUtils.getCurrentUserId()).stream().map(RoleSmallDto::getLevel).collect(Collectors.toList()));
            Integer optLevel = Collections.min(roleService.findByUsersId(id).stream().map(RoleSmallDto::getLevel).collect(Collectors.toList()));
            if(currentLevel > optLevel) {
                throw new BadRequestException("角色权限不足，不能删除：" + userService.findById(id).getUsername());
            }
        }
        userService.delete(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("修改密码")
    @PostMapping(value = "/updatePass")
    public ResponseEntity<Object> updatePass(@RequestBody UserPassVo passVo) throws Exception {
        String oldPass = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, passVo.getOldPass());
        String newPass = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, passVo.getNewPass());
        if(StringUtils.isEmpty(passVo.getUsername())) {
            passVo.setUsername(SecurityUtils.getCurrentUsername());
        }
        UserDto user = userService.findByName(passVo.getUsername());
        // TODO 浙江省教育厅项目临时关闭需要输入初始密码功能
        if(!"DEFAULT_PASSWORD".equals(oldPass) && !passwordEncoder.matches(oldPass, user.getPassword())) {
            throw new BadRequestException("修改失败，旧密码错误");
        }
        if(passwordEncoder.matches(newPass, user.getPassword())) {
            throw new BadRequestException("新密码不能与旧密码相同");
        }
        userService.updatePass(user.getUsername(), passwordEncoder.encode(newPass));

        if(StringUtils.isNotBlank(passVo.getPhone())) {
            userService.updatePhone(user.getUsername(), passVo.getPhone());
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("管理员修改密码")
    @PostMapping(value = "/adminUpdatePass")
    public ResponseEntity<Object> adminUpdatePass(@RequestBody UserPassVo passVo) throws Exception {
        if(StringUtils.isEmpty(passVo.getUsername())) {
            throw new CustomException("修改用户密码参数错误");
        }
        String newPass = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, passVo.getNewPass());
        UserDto user = userService.findByName(passVo.getUsername());
        userService.updatePass(user.getUsername(), passwordEncoder.encode(newPass));
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation("修改头像")
    @PostMapping(value = "/updateAvatar")
    public ResponseEntity<Object> updateAvatar(@RequestParam MultipartFile avatar) {
        return new ResponseEntity<>(userService.updateAvatar(avatar), HttpStatus.OK);
    }

    @Log("修改邮箱")
    @ApiOperation("修改邮箱")
    @PostMapping(value = "/updateEmail/{code}")
    public ResponseEntity<Object> updateEmail(@PathVariable String code, @RequestBody User user) throws Exception {
        String password = RsaUtils.decryptByPrivateKey(RsaProperties.privateKey, user.getPassword());
        UserDto userDto = userService.findByName(SecurityUtils.getCurrentUsername());
        if(!passwordEncoder.matches(password, userDto.getPassword())) {
            throw new BadRequestException("密码错误");
        }
        verificationCodeService.validated(CodeEnum.EMAIL_RESET_EMAIL_CODE.getKey() + user.getEmail(), code);
        userService.updateEmail(userDto.getUsername(), user.getEmail());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("getUsers")
    public ResponseEntity<Object> getUsers(@RequestBody List<String> usernames) {
        ArrayList<UserDto> userDtos = Lists.newArrayListWithCapacity(usernames.size());
        for(String username : usernames) {
            userDtos.add(userService.findByName(username));
        }
        return ResponseEntity.ok(userDtos);
    }

    /**
     * 根据角色id查询用户集合
     * @return
     */
    @ApiOperation("根据角色id查询用户集合")
    @GetMapping(value = "/findByRoleId")
    @PreAuthorize("@el.check('user:list')")
    ResponseEntity<Object> findPageByRoleId(UserQueryCriteria userQueryCriteria, Pageable pageable) {
        return new ResponseEntity<>(userService.findPageByRoleId(userQueryCriteria.getId(), pageable), HttpStatus.OK);
    }

    /**
     * 如果当前用户的角色级别低于创建用户的角色级别，则抛出权限不足的错误
     * @param resources /
     */
    private void checkLevel(User resources) {
        Integer currentLevel = Collections.min(roleService.findByUsersId(SecurityUtils.getCurrentUserId()).stream().map(RoleSmallDto::getLevel).collect(Collectors.toList()));
        Integer optLevel = roleService.findByRoles(resources.getRoles());
        if(currentLevel > optLevel) {
            throw new BadRequestException("角色权限不足");
        }
    }

    @GetMapping("/findByDeptId")
    @ApiOperation("根据部门获取人")
    public ResponseEntity<List<UserDto>> findByDeptId(@RequestParam Long deptId, @RequestParam Integer userType) {
        return new ResponseEntity(userService.findByDeptId(deptId, userType), HttpStatus.OK);
    }

    @GetMapping("/getUnitUsersTree")
    @ApiOperation("获取单位账号树")
    public ResponseEntity<List<JSONObject>> getUnitUsersTree() {
        return new ResponseEntity(userService.getUnitUsersTree(), HttpStatus.OK);
    }

    @GetMapping("/findUsersByRoleId")
    @ApiOperation("根据角色Id进行分页查询")
    public ResponseEntity<Object> findUsersByRoleId(UserRoleQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.findUsersByRoleId(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("根据角色Id进行分页查询")
    @PostMapping("/findUserByUserNames")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userNames", value = "用户名集合", required = true, paramType = "string")
    })
    public ResponseEntity<Object> findUserByUserNames(@RequestBody List<String> userNames) {
        return new ResponseEntity(userService.findByUserNames(userNames), HttpStatus.OK);
    }

    /**
     * 根据角色查询角色关联的所有用户
     * @return
     */
    @ApiOperation("根据角色查询角色关联的所有用户")
    @GetMapping(value = "/findAllByRoleId")
    @ApiImplicitParams({@ApiImplicitParam(name = "roleId", value = "角色id", paramType = "int")})
    ResponseEntity<Object> findAllByRoleId(@RequestParam Long roleId) {
        List<Map<String, Object>> list = userService.findAllByRoleId(roleId);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }


    @Log("根据用户名查询用户信息(用于流程审批时意见自动生成)")
    @ApiOperation("根据用户名查询用户信息(用于流程审批时意见自动生成)")
    @PostMapping("/findSpecialUserByUserName")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userNames", value = "用户名集合", required = true, paramType = "string")
    })
    public ResponseEntity<Object> findSpecialUserByUserName(@RequestBody List<String> userNames) {
        return new ResponseEntity(userService.findSpecialUserByUserName(userNames), HttpStatus.OK);
    }

    @ApiOperation("检查是否是下级单位账号")
    @GetMapping("/checkWhetherUnitAccount")
    public ResponseEntity<Boolean> checkWhetherUnitAccount() {
        return new ResponseEntity<>(userService.checkWhetherUnitAccount(), HttpStatus.OK);
    }

    @PostMapping("/getUnitUsersList")
    @ApiOperation("获取单位列表")
    public ResponseEntity<List<Object>> getUnitUsersList(@RequestBody(required = false) User resources) {
        String nickName = null;
        if(resources != null) {
            nickName = resources.getNickName();
        }
        return new ResponseEntity(userService.findByEnabledAndUserTypeList(true, 2, nickName), HttpStatus.OK);
    }

    @ApiOperation("根据手机号获取用户信息")
    @PostMapping(value = "/getUserInfoByPhone")
    public ResponseEntity<Object> getUserInfoByPhone(@RequestBody UserPassVo passVo) throws Exception {
        UserDto user = userService.findByPhone(passVo.getPhone());
        return new ResponseEntity<>(user, HttpStatus.OK);
    }

}
