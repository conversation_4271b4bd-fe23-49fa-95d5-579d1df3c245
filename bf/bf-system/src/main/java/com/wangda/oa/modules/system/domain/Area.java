package com.wangda.oa.modules.system.domain;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

/**
 * @Description: 地区信息
 * @Author: caiyy
 * @Date: 2021/10/25
 */
@Entity
@Getter
@Setter
@Table(name = "area_code")
public class Area extends BaseEntity {

    @Id
    @Column(name = "id")
    @NotNull(groups = BaseEntity.Update.class)
    @ApiModelProperty(value = "ID", hidden = true)
    private Long id;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "地区编码")
    private String codeArea;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "中心位置")
    private String center;

    @ApiModelProperty(value = "地区级别")
    private String level;

    @ApiModelProperty(value = "父级id")
    private Long parentCode;


}
