package com.wangda.oa.modules.extension.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/13 下午2:41
 */
@Data
public class RecentlyPeopleListBO {

    @ApiModelProperty(value = "保存的用户id")
    private List<Long> list;

    @ApiModelProperty(value = "类型")
    @NotBlank(message = "类型不能为空")
    private String type;

    @ApiModelProperty(value = "环节")
    private String link;
}
