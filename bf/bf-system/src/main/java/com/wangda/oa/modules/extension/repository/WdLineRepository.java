package com.wangda.oa.modules.extension.repository;

import com.wangda.oa.modules.extension.domain.WdLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/9 上午11:55
 */
public interface WdLineRepository extends JpaRepository<WdLine, Long>, JpaSpecificationExecutor<WdLine> {

    /**
     * 根据父id、类型查询序号是否存在(编辑)
     * @param pid
     * @param type
     * @param id
     * @return
     */
    int countByPidAndTypeAndIdNot(Long pid,Integer type,Long id);

    /**
     * 根据父id、类型查询序号是否存在(新增)
     * @param pid
     * @param type
     * @return
     */
    int countByPidAndType(Long pid,Integer type);

    /**
     * 根据父id查询子目录数量
     * @param pid
     * @return
     */
    int countByPid(Long pid);

    /**
     * 根据父类id和类型查询数据
     * @param pid
     * @param type
     * @return
     */
    List<WdLine> findByPidInAndType(List<Long> pid,Integer type);

    /**
     * 根据keyIds查询记录
     * @param keyIds
     * @return
     */
    List<WdLine> findByKeyIdIn(List<Long> keyIds);

    /**
     * 根据姓名模糊查询
     * @param name
     * @return
     */
    List<WdLine> findByNameLike(String name);

    /**
     * 根据父类id查询数据
     * @param pid
     * @return
     */
    List<WdLine> findByPid(Long pid);


}
