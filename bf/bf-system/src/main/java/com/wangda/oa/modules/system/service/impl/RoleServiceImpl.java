/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.exception.EntityExistException;
import com.wangda.oa.modules.security.service.UserCacheClean;
import com.wangda.oa.modules.system.domain.Menu;
import com.wangda.oa.modules.system.domain.Role;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.domain.UserRole;
import com.wangda.oa.modules.system.repository.RoleRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.repository.UserRoleRepository;
import com.wangda.oa.modules.system.service.MenuService;
import com.wangda.oa.modules.system.service.RoleService;
import com.wangda.oa.modules.system.service.dto.*;
import com.wangda.oa.modules.system.service.mapstruct.RoleMapper;
import com.wangda.oa.modules.system.service.mapstruct.RoleSmallMapper;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Jie
 * @date 2018-12-03
 */
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "role")
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository;
    private final RoleMapper roleMapper;
    private final RoleSmallMapper roleSmallMapper;
    private final RedisUtils redisUtils;
    private final UserRepository userRepository;
    private final UserCacheClean userCacheClean;
    private final UserRoleRepository userRoleRepository;
    @Lazy
    @Autowired
    private MenuService menuService;

    @Override
    public List<RoleDto> queryAll() {
        Sort sort = new Sort(Sort.Direction.ASC, "level");
        return roleMapper.toDto(roleRepository.findAll(sort));
    }

    @Override
    public List<RoleDto> queryAll(RoleQueryCriteria criteria) {
        return roleMapper.toDto(roleRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    public Object queryAll(RoleQueryCriteria criteria, Pageable pageable) {
        Page<Role> page = roleRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(roleMapper::toDto));
    }

    @Override
    public RoleDto getAuthority(Long id) {
        RoleDto roleDto = this.findById(id);

        // 获取所有格式化菜单
        Map<Long, List<MenuDto>> allMenuDtoMap = menuService.formatAllMenu();
        // 获取所属角色菜单
        Map<Long, MenuDto> menusDtoMap = roleDto.getMenus().stream().collect(Collectors.toMap(MenuDto::getId, v -> v));
        ListIterator<Map.Entry<Long, List<MenuDto>>> ltIterator = new ArrayList<>(allMenuDtoMap.entrySet()).listIterator(allMenuDtoMap.size());
        while(ltIterator.hasPrevious()) {
            Map.Entry<Long, List<MenuDto>> entry = ltIterator.previous();
            // 判断是否不属于角色菜单的子级菜单
            List<MenuDto> menuDtoList = entry.getValue().stream().filter(m -> !menusDtoMap.containsKey(m.getId())).collect(Collectors.toList());
            if(!CollectionUtil.isEmpty(menuDtoList)) {
                menusDtoMap.remove(entry.getKey());
            }
        }
        roleDto.setMenus(Sets.newHashSet(menusDtoMap.values()));

        return roleDto;
    }

    @Override
    public Object queryAllAuthority(RoleQueryCriteria criteria, Pageable pageable) {
        Page<Role> page = roleRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Page<RoleDto> roleDtoPage = page.map(roleMapper::toDto);
        List<RoleDto> roleDtoList = roleDtoPage.getContent();

        // 获取所有格式化菜单
        Map<Long, List<MenuDto>> allMenuDtoMap = menuService.formatAllMenu();
        for(RoleDto roleDto: roleDtoList) {
            // 获取所属角色菜单
            Map<Long, MenuDto> menusDtoMap = roleDto.getMenus().stream().collect(Collectors.toMap(MenuDto::getId, v -> v));
            ListIterator<Map.Entry<Long, List<MenuDto>>> ltIterator = new ArrayList<>(allMenuDtoMap.entrySet()).listIterator(allMenuDtoMap.size());
            while(ltIterator.hasPrevious()) {
                Map.Entry<Long, List<MenuDto>> entry = ltIterator.previous();
                // 判断是否不属于角色菜单的子级菜单
                List<MenuDto> menuDtoList = entry.getValue().stream().filter(m -> !menusDtoMap.containsKey(m.getId())).collect(Collectors.toList());
                if(!CollectionUtil.isEmpty(menuDtoList)) {
                    menusDtoMap.remove(entry.getKey());
                }
            }
            roleDto.setMenus(Sets.newHashSet(menusDtoMap.values()));
        }

        return PageUtil.toPage(roleDtoList, roleDtoPage.getTotalElements());
    }

    @Override
    @Cacheable(key = "'id:' + #p0")
    @Transactional(rollbackFor = Exception.class)
    public RoleDto findById(long id) {
        Role role = roleRepository.findById(id).orElseGet(Role::new);
        ValidationUtil.isNull(role.getId(), "Role", "id", id);
        return roleMapper.toDto(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Role resources) {
        if (roleRepository.findByName(resources.getName()) != null) {
            throw new EntityExistException(Role.class, "username", resources.getName());
        }
        //权限可能存在多个，用英文逗号拼接      ----修改  by ----caiyy    time：2021/10/09     start
        String authority = null;
        if(StringUtils.isNotBlank(resources.getAuthorityKey())) {
            List<String> keys = (List<String>) JSON.parse(resources.getAuthorityKey());
            authority = String.join(ElAdminConstant.SEPARATOR_COMMA, keys);
        }
        resources.setAuthorityKey(authority);
        //权限可能存在多个，用英文逗号拼接      ----修改  by ----caiyy    time：2021/10/09     end
        roleRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Role resources) {
        Role role = roleRepository.findById(resources.getId()).orElseGet(Role::new);
        ValidationUtil.isNull(role.getId(), "Role", "id", resources.getId());

        Role role1 = roleRepository.findByName(resources.getName());

        if (role1 != null && !role1.getId().equals(role.getId())) {
            throw new EntityExistException(Role.class, "username", resources.getName());
        }
        role.setName(resources.getName());
        role.setDescription(resources.getDescription());
        role.setDataScope(resources.getDataScope());
        role.setDepts(resources.getDepts());
        role.setLevel(resources.getLevel());
        //权限可能存在多个，用英文逗号拼接      ----修改  by ----caiyy    time：2021/10/09     start
        String authority = null;
        if(StringUtils.isNotBlank(resources.getAuthorityKey())) {
            List<String> keys = (List<String>) JSON.parse(resources.getAuthorityKey());
            authority = String.join(ElAdminConstant.SEPARATOR_COMMA, keys);
        }
        role.setAuthorityKey(authority);
        //权限可能存在多个，用英文逗号拼接      ----修改  by ----caiyy    time：2021/10/09     end
        roleRepository.save(role);
        // 更新相关缓存
        delCaches(role.getId(), null);
    }

    @Override
    public void updateMenu(Role resources, RoleDto roleDTO) {
        Role role = roleMapper.toEntity(roleDTO);
        List<User> users = userRepository.findByRoleId(role.getId());
        // 更新菜单
        role.setMenus(resources.getMenus());
        delCaches(resources.getId(), users);
        roleRepository.save(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void untiedMenu(Long menuId) {
        // 更新菜单
        roleRepository.untiedMenu(menuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Set<Long> ids) {
        for (Long id : ids) {
            // 更新相关缓存
            delCaches(id, null);
        }
        roleRepository.deleteAllByIdIn(ids);
    }

    @Override
    public List<RoleSmallDto> findByUsersId(Long id) {
        return roleSmallMapper.toDto(new ArrayList<>(roleRepository.findByUserId(id)));
    }

    @Override
    public Integer findByRoles(Set<Role> roles) {
        Set<RoleDto> roleDtos = new HashSet<>();
        for (Role role : roles) {
            roleDtos.add(findById(role.getId()));
        }
        return Collections.min(roleDtos.stream().map(RoleDto::getLevel).collect(Collectors.toList()));
    }

    @Override
    @Cacheable(key = "'auth:' + #p0.id")
    public List<GrantedAuthority> mapToGrantedAuthorities(UserDto user) {
        Set<String> permissions = new HashSet<>();
        // 如果是管理员直接返回
        if (user.getIsAdmin()) {
            permissions.add("admin");
            return permissions.stream().map(SimpleGrantedAuthority::new)
                    .collect(Collectors.toList());
        }
        Set<Role> roles = roleRepository.findByUserId(user.getId());
        permissions = roles.stream().flatMap(role -> role.getMenus().stream())
                .filter(menu -> StringUtils.isNotBlank(menu.getPermission()))
                .map(Menu::getPermission).collect(Collectors.toSet());

        // 加角色权限
        for (Role role: roles) {
            String authorityKeys = role.getAuthorityKey();
            if(StringUtils.isNotBlank(authorityKeys)) {
                List<String> authorityKeyList = Arrays.asList(authorityKeys.trim().split(ElAdminConstant.SEPARATOR_COMMA));
                for (String authorityKey: authorityKeyList) {
                    permissions.add(authorityKey);
                }
            }
        }

        return permissions.stream().map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    @Override
    public void download(List<RoleDto> roles, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (RoleDto role : roles) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("角色名称", role.getName());
            map.put("角色级别", role.getLevel());
            map.put("描述", role.getDescription());
            map.put("创建日期", role.getCreateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public void verification(Set<Long> ids) {
        if (userRepository.countByRoles(ids) > 0) {
            throw new BadRequestException("所选角色存在用户关联，请解除关联再试！");
        }
    }

    @Override
    public List<Role> findInMenuId(List<Long> menuIds) {
        return roleRepository.findInMenuId(menuIds);
    }

    @Override
    public void deleteUserRole(Role resources) {
        Set<User> list = resources.getUsers();
        if(list!=null && list.size()>0){
            //删除角色缓存
            delCaches(resources.getId(),new ArrayList<>(list));
            for (User user:list) {
                //删除用户缓存
                redisUtils.del(CacheKey.USER_ID + user.getId());
                redisUtils.del(CacheKey.USER_NAME + user.getUsername());
                //todo 若后续子用户也有角色也要删除子用户的缓存
                userRoleRepository.deleteByRoleIdAndUserId(resources.getId(),user.getId());
            }
        }
    }

    @Override
    public void addUserRole(Role resources) {
        Set<User> userSet=resources.getUsers();
        if(userSet!=null){
            List<UserRole> userRoleList=userSet.stream().map(p->{
                UserRole userRole=new UserRole();
                userRole.setRoleId(resources.getId());
                userRole.setUserId(p.getId());
                return userRole;
            }).collect(Collectors.toList());
            userRoleRepository.saveAll(userRoleList);
        }
    }

    @Override
    public void roleAddUser(UserRoleDto userRoleDto) {
        List<UserRole> list = new ArrayList<>();
        //先删除后添加
        //userRoleRepository.deleteByRoleId(userRoleDto.getRoleId());
        for(SimpleUserDto u:userRoleDto.getRoleUsers()){
            UserRole userRole =new UserRole();
            userRole.setRoleId(userRoleDto.getRoleId());
            userRole.setUserId(u.getId());
            list.add(userRole);
        }
        if(!list.isEmpty()){
            userRoleRepository.saveAll(list);
        }
    }

    @Override
    public void deleteRoleUser(Long userId, Long roleId) {
        userRoleRepository.deleteByRoleIdAndUserId(roleId,userId);
    }

    /**
     * 清理缓存
     * @param id /
     */
    public void delCaches(Long id, List<User> users) {
        users = CollectionUtil.isEmpty(users) ? userRepository.findByRoleId(id) : users;
        if (CollectionUtil.isNotEmpty(users)) {
            users.forEach(item -> userCacheClean.cleanUserCache(item.getUsername()));
            Set<Long> userIds = users.stream().map(User::getId).collect(Collectors.toSet());
            redisUtils.delByKeys(CacheKey.DATE_USER, userIds);
            redisUtils.delByKeys(CacheKey.MENU_USER, userIds);
            redisUtils.delByKeys(CacheKey.ROLE_AUTH, userIds);
        }
        redisUtils.del(CacheKey.ROLE_ID + id);
    }
}
