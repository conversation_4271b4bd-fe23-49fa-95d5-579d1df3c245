package com.wangda.oa.modules.extension.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/11/12
 * 选人组件参数
 */
@Data
public class CommonSelectBO {

    @ApiModelProperty(value = "类型(user-picker:选人,unit-user-picker:选本单位下人,unit-picker:选单位)")
    @NotBlank(message = "类型不能为空")
    private String userSelector;

}
