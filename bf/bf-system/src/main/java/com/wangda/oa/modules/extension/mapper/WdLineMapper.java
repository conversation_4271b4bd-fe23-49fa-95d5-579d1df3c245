package com.wangda.oa.modules.extension.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wangda.oa.modules.extension.domain.WdLine;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface WdLineMapper extends BaseMapper<WdLine> {

    /**
     * 大于等于传入序号的数据序号都累加1
     * @param sort 序号
     * @param type 类型
     * @param pid 父id
     */
    void updateSortBySort(@Param("sort") Integer sort, @Param("type") Integer type,@Param("pid") Long pid);
}
