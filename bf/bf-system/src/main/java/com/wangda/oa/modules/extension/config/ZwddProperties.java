package com.wangda.oa.modules.extension.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 属性文件
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "zwdd")
public class ZwddProperties {

	private String domainName;
	private String protocal;
	private String accessKey;
	private String secretKey;
	/**
	 * 应用标识
	 */
	private String applicationId;
	private String tenantId;
	private String webUrl;
	private String appUrl;
	/**
	 * 类型(zwdd:浙政钉,zydd:专有钉钉)
	 */
	private String type;
}
