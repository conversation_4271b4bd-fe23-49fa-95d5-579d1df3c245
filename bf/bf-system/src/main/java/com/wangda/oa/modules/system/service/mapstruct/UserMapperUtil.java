package com.wangda.oa.modules.system.service.mapstruct;

import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.SimpleUserDto;
import com.wangda.oa.modules.system.service.dto.UserDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Component
public class UserMapperUtil {

    @Autowired
    private UserService userService;

    public SimpleUserDto toUser(String userName) {
        if(StringUtils.isEmpty(userName)) {
            return new SimpleUserDto();
        }
        UserDto user = userService.findByName(userName);
        if(Objects.nonNull(user)) {
            SimpleUserDto simpleUserDto = SimpleUserDto.builder()
                    .id(user.getId())
                    .userName(user.getUsername())
                    .nickName(user.getNickName())
                    .extId(user.getDept().getName() + user.getId())
                    .build();
            return simpleUserDto;
        }else {
            return new SimpleUserDto();
        }
    }

    public String toName(SimpleUserDto simpleUserDto) {
        if(simpleUserDto == null) {
            return null;
        }
        return simpleUserDto.getUserName();
    }

    public Set<SimpleUserDto> toConvertToSimpleUserDto(Set<String> userNames) {
        Set<SimpleUserDto> set = new HashSet<>();
        if(CollectionUtils.isEmpty(userNames)) {
            return set;
        }
        for(String userName : userNames) {
            UserDto user = userService.findByName(userName);
            if(Objects.nonNull(user)) {
                SimpleUserDto simpleUserDto = SimpleUserDto.builder()
                        .id(user.getId())
                        .userName(user.getUsername())
                        .nickName(user.getNickName())
                        .extId(user.getDept().getName() + user.getId())
                        .build();
                set.add(simpleUserDto);
            }
        }

        return set;
    }

    public List<SimpleUserDto> toConvertToSimpleUserDto(List<String> userNames) {
        List<SimpleUserDto> set = new ArrayList<>();
        if(CollectionUtils.isEmpty(userNames)) {
            return set;
        }
        for(String userName : userNames) {
            UserDto user = userService.findByName(userName);
            if(Objects.nonNull(user)) {
                SimpleUserDto simpleUserDto = SimpleUserDto.builder()
                        .id(user.getId())
                        .userName(user.getUsername())
                        .nickName(user.getNickName())
                        .extId(user.getDept().getName() + user.getId())
                        .build();
                set.add(simpleUserDto);
            }
        }

        return set;
    }

    public Set<String> toConvertToUserName(Set<SimpleUserDto> simpleUserDtoSet) {
        Set<String> set = new HashSet<>();
        if(CollectionUtils.isEmpty(simpleUserDtoSet)) {
            return set;
        }
        for(SimpleUserDto simpleUserDto : simpleUserDtoSet) {
            set.add(simpleUserDto.getUserName());
        }
        return set;
    }

}
