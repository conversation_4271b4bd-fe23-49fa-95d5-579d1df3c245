/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.exception.EntityExistException;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.service.SysUserPlatformService;
import com.wangda.oa.modules.security.service.OnlineUserService;
import com.wangda.oa.modules.security.service.UserCacheClean;
import com.wangda.oa.modules.system.domain.ChildUser;
import com.wangda.oa.modules.system.domain.Dept;
import com.wangda.oa.modules.system.domain.DictDetail;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.repository.ChildUserRepository;
import com.wangda.oa.modules.system.repository.DeptRepository;
import com.wangda.oa.modules.system.repository.DictDetailRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.DictDetailService;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.*;
import com.wangda.oa.modules.system.service.mapstruct.UserMapper;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Jie
 * @date 2018-11-23
 */
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "user")
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final FileProperties properties;
    private final RedisUtils redisUtils;
    private final UserCacheClean userCacheClean;
    private final OnlineUserService onlineUserService;
    private final SysUserPlatformService sysUserPlatformService;
    private final ZwddProperties zwddProperties;
    private final DeptRepository deptRepository;
    private final DictDetailRepository dictDetailRepository;
    private final DictDetailService dictDetailService;
    private final ChildUserRepository childUserRepository;

    @Override
    public Object queryAll(UserQueryCriteria criteria, Pageable pageable) {
        Page<User> page = userRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
            Predicate predicate = QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            if (CollUtil.isNotEmpty(criteria.getDeptIds())) {
                Join<Object, Object> concurDept = root.join("concurDept", JoinType.LEFT);
                Path<Object> id = concurDept.get("id");
                Predicate in = id.in(criteria.getDeptIds());
                criteriaQuery.distinct(true);
                return criteriaBuilder.or(predicate, in);
            }
            return predicate;
        }, pageable);
        Map<String, Object> map = PageUtil.toPage(page.map(userMapper::toDto));
        //查询第三方用户
        List<UserDto> list = (List<UserDto>) map.get("content");
        if(CollUtil.isNotEmpty(list)) {
            List<String> usernameList = list.stream().map(userDto -> {
                return userDto.getUsername();
            }).collect(Collectors.toList());
            Map<String, SysUserPlatform> userPlatformMap = new HashMap<>();
            List<SysUserPlatform> userPlatformList = sysUserPlatformService.queryByUsernameInAndType(usernameList, zwddProperties.getType());
            Optional.ofNullable(userPlatformList).orElseGet(Lists::newArrayList).forEach(sysUserPlatform -> {
                userPlatformMap.put(sysUserPlatform.getUserName(), sysUserPlatform);
            });
            SysUserPlatform sysUserPlatform = null;
            for (UserDto u : list) {
                sysUserPlatform = userPlatformMap.get(u.getUsername());
                if (sysUserPlatform != null) {
                    u.setPlatformUserId(sysUserPlatform.getPlatformUserId());
                    u.setPlatformUserName(sysUserPlatform.getPlatformUserName());
                }
            }
        }
        return map;
    }

    @Override
    public List<UserDto> queryAll(UserQueryCriteria criteria) {
        List<User> users = userRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        return userMapper.toDto(users);
    }

    @Override
    @Cacheable(key = "'id:' + #p0")
    @Transactional(rollbackFor = Exception.class)
    public UserDto findById(long id) {
        User user = userRepository.findById(id).orElseGet(User::new);
        ValidationUtil.isNull(user.getId(), "User", "id", id);
        return userMapper.toDto(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(User resources) {
        if (userRepository.findByUsername(resources.getUsername()) != null) {
            throw new EntityExistException(User.class, "username", resources.getUsername());
        }
        userRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(User resources) throws Exception {
        User user = userRepository.findById(resources.getId()).orElseGet(User::new);
        ValidationUtil.isNull(user.getId(), "User", "id", resources.getId());
        User user1 = userRepository.findByUsername(resources.getUsername());
        //User user2 = userRepository.findByEmail(resources.getEmail());

        if (user1 != null && !user.getId().equals(user1.getId())) {
            throw new EntityExistException(User.class, "username", resources.getUsername());
        }
        if (childUserRepository.findByUsername(resources.getUsername()) != null) {
            throw new EntityExistException(ChildUser.class, "username", resources.getUsername());
        }

//        if (user2 != null && !user.getId().equals(user2.getId())) {
//            throw new EntityExistException(User.class, "email", resources.getEmail());
//        }
        // 如果用户的角色改变
        if (!resources.getRoles().equals(user.getRoles())) {
            redisUtils.del(CacheKey.DATE_USER + resources.getId());
            redisUtils.del(CacheKey.MENU_USER + resources.getId());
            redisUtils.del(CacheKey.ROLE_AUTH + resources.getId());
        }
        // 如果用户名称修改
        if (!resources.getUsername().equals(user.getUsername())) {
            redisUtils.del("user::username:" + user.getUsername());
        }
        // 如果用户被禁用，则清除用户登录信息
        if (!resources.getEnabled()) {
            onlineUserService.kickOutForUsername(resources.getUsername());
        }else {
            redisUtils.del(CacheKey.FROZEN_KEY.concat(resources.getUsername()));
        }
        user.setUsername(resources.getUsername());
        user.setUserType(resources.getUserType());
        user.setEmail(resources.getEmail());
        user.setEnabled(resources.getEnabled());
        user.setRoles(resources.getRoles());
        user.setDept(resources.getDept());
        user.setJobs(resources.getJobs());
        user.setPhone(resources.getPhone());
        user.setNickName(resources.getNickName());
        user.setGender(resources.getGender());
        user.setSort(resources.getSort());
        user.setConcurDept(resources.getConcurDept());
        userRepository.save(user);
        // 清除缓存
        delCaches(user.getId(), user.getUsername());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCenter(User resources) {
        User user = userRepository.findById(resources.getId()).orElseGet(User::new);
        user.setNickName(resources.getNickName());
        user.setPhone(resources.getPhone());
        user.setGender(resources.getGender());
        userRepository.save(user);
        // 清理缓存
        delCaches(user.getId(), user.getUsername());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableUser(String username) {
        userRepository.disableUser(username);
        flushCache(username);
        redisUtils.del(CacheKey.USER_NAME + username);
    }

    @Override
    public Map findPageByRoleId(Long id, Pageable pageable) {
        Page<User> page = userRepository.findPageByRoleId(id, pageable);
        return PageUtil.toPage(page.map(userMapper::toDto));
    }

    @Override
    public List<String> findUsernameByIds(List<Long> ids) {
        ArrayList<String> usernameList = Lists.newArrayListWithCapacity(ids.size());
        for (Long id : ids) {
            Optional<User> optionalUser = userRepository.findById(id);
            optionalUser.ifPresent(user -> usernameList.add(user.getUsername()));
        }
        return usernameList;
    }

    @Override
    public Object findUsersByRoleId(UserRoleQueryCriteria criteria, Pageable pageable) {
        Page<User> page = userRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Map<String, Object> map = PageUtil.toPage(page.map(userMapper::toDto));
        return map;
    }

    @Override
    public List<UserDto> findByUserNames(List<String> userNames) {
        List<User> users = userRepository.findByUsernameIn(userNames);
        return userMapper.toDto(users);
    }

    @Override
    public List<UserDto> findByUserNickNameLike(String nickName, Boolean enabled) {
        List<User> users = null;
        if (Objects.nonNull(enabled)) {
            users = userRepository.findByNickNameLikeAndEnabled(nickName, enabled);
        }else {
            users = userRepository.findByNickNameLike(nickName);
        }
        return userMapper.toDto(users);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Set<Long> ids) {
        for (Long id : ids) {
            // 清理缓存
            UserDto user = findById(id);
            delCaches(user.getId(), user.getUsername());
            //刪除绑定的浙政钉用户
            sysUserPlatformService.deleteByUserNameAndType(user.getUsername(), zwddProperties.getType());
        }
        userRepository.deleteAllByIdIn(ids);
    }

    @Override
    @Cacheable(key = "'username:' + #p0", unless = "#result == null")
    public UserDto findByName(String userName) {
        User user = userRepository.findByUsername(userName);
        return userMapper.toDto(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePass(String username, String pass) {
        userRepository.updatePass(username, pass, new Date());
        redisUtils.del("user::username:" + username);
        flushCache(username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> updateAvatar(MultipartFile multipartFile) {
        User user = userRepository.findByUsername(SecurityUtils.getCurrentUsername());
        String oldPath = user.getAvatarPath();
        File file = FileUtil.upload(multipartFile, properties.getPath().getAvatar());
        user.setAvatarPath(Objects.requireNonNull(file).getPath());
        user.setAvatarName(file.getName());
        userRepository.save(user);
        if (StringUtils.isNotBlank(oldPath)) {
            FileUtil.del(oldPath);
        }
        @NotBlank String username = user.getUsername();
        redisUtils.del(CacheKey.USER_NAME + username);
        flushCache(username);
        return new HashMap<String, String>(1) {{
            put("avatar", file.getName());
        }};
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEmail(String username, String email) {
        userRepository.updateEmail(username, email);
        redisUtils.del(CacheKey.USER_NAME + username);
        flushCache(username);
    }

    @Override
    public void download(List<UserDto> queryAll, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (UserDto userDTO : queryAll) {
            List<String> roles = userDTO.getRoles().stream().map(RoleSmallDto::getName).collect(Collectors.toList());
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("用户名", userDTO.getUsername());
            map.put("用户姓名", userDTO.getNickName());
            map.put("角色", roles);
            map.put("部门", userDTO.getDept().getName());
            map.put("岗位", userDTO.getJobs().stream().map(JobSmallDto::getName).collect(Collectors.toList()));
            map.put("邮箱", userDTO.getEmail());
            map.put("状态", userDTO.getEnabled() ? "启用" : "禁用");
            map.put("手机号码", userDTO.getPhone());
            map.put("修改密码的时间", userDTO.getPwdResetTime());
            map.put("创建日期", userDTO.getCreateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    /**
     * 清理缓存
     * @param id /
     */
    public void delCaches(Long id, String username) {
        redisUtils.del(CacheKey.USER_ID + id);
        redisUtils.del(CacheKey.USER_NAME + username);
        flushCache(username);
    }

    /**
     * 清理 登陆时 用户缓存信息
     * @param username /
     */
    private void flushCache(String username) {
        userCacheClean.cleanUserCache(username);
    }

    @Override
    public List<UserDto> findByDeptId(Long deptId, Integer userType) {
        List<User> list = userRepository.findByDeptIdAndEnabledAndUserType(deptId, true, userType);
        List<UserDto> userDtos = userMapper.toDto(list);
        return userDtos;
    }

    @Override
    public List<UserDto> findByDeptIds(List<Long> deptIds, Integer userType) {
        List<User> list = userRepository.findByDeptIdInAndEnabledAndUserTypeOrderBySortAsc(deptIds, true, userType);
        List<UserDto> userDtos = userMapper.toDto(list);
        return userDtos;
    }

    @Override
    public List<JSONObject> getUnitUsersTree() {
        List<JSONObject> roots = new ArrayList<>();

        List<User> list = userRepository.findByEnabledAndUserType(true, 2);
        Map<Long, List<JSONObject>> collect = list.stream().map(user -> {
            JSONObject userJson = new JSONObject();
            userJson.put("userId", user.getId());
            userJson.put("value", user.getUsername());
            userJson.put("text", user.getNickName());
            userJson.put("deptId", user.getDept().getId());
            return userJson;
        }).collect(Collectors.groupingBy(userJson -> userJson.getLong("deptId")));


        Optional<Dept> jyj = deptRepository.findById(20020L);
        if (jyj.isPresent()) {
            JSONObject sd = new JSONObject();
            sd.put("text", jyj.get().getName());
            sd.put("value", "sd");
            roots.add(sd);

            List<Dept> sdDepts = deptRepository.findByPidAndEnabled(20020L);
            List<JSONObject> sdChildren = sdDepts.stream().map(dept -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("text", dept.getName());
                jsonObject.put("value", dept.getId());
                return jsonObject;
            }).collect(Collectors.toList());
            sd.put("children", sdChildren);
            buildTree(sdChildren, collect);
        }
        Optional<Dept> gx = deptRepository.findById(20021L);
        if (gx.isPresent()) {
            JSONObject school = new JSONObject();
            school.put("text", gx.get().getName());
            school.put("value", "sd1");
            roots.add(school);

            List<Dept> sdDepts = deptRepository.findByPidAndEnabled(20021L);
            List<JSONObject> sdChildren = sdDepts.stream().map(dept -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("text", dept.getName());
                jsonObject.put("value", dept.getId());
                return jsonObject;
            }).collect(Collectors.toList());
            school.put("children", sdChildren);
            buildTree(sdChildren, collect);
        }
        JSONObject cs = new JSONObject();
        cs.put("text", "处室");
        cs.put("value", "cs");
        roots.add(cs);

        JSONObject zf = new JSONObject();
        zf.put("text", "厅办公室");
        zf.put("value", "zf");
        roots.add(zf);


        List<Dept> csDepts = deptRepository.findByPidAndEnabled(10000L);
        List<JSONObject> csChildren = csDepts.stream().filter(dept -> {
            String name = dept.getName();
            return !"厅领导".equals(name)
                    && !"离退休".equals(name)
                    && !"值班室".equals(name)
                    && !"临时".equals(name)
                    && !"各市、县（市、区）教育局".equals(name)
                    && !"各高等学校".equals(name);
        }).map(dept -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("text", dept.getName());
            jsonObject.put("value", dept.getId());
            return jsonObject;
        }).collect(Collectors.toList());
        cs.put("children", csChildren);
        buildTree(csChildren, collect);

//        List<Dept> depts = deptRepository.findByPidAndEnabled(10000L);
//        List<JSONObject> zfChildren = depts.stream().map(dept -> {
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("text", dept.getName());
//            jsonObject.put("value", dept.getId());
//            return jsonObject;
//        }).collect(Collectors.toList());
//        zf.put("children",zfChildren);

//        List<User> list1 = userRepository.findByEnabledAndUserType(true, 1);
//        Map<Long, List<JSONObject>> collect1 = list1.stream().map(user -> {
//            JSONObject userJson = new JSONObject();
//            userJson.put("userId", user.getId());
//            userJson.put("value", user.getUsername());
//            userJson.put("text", user.getNickName());
//            userJson.put("deptId", user.getDept().getId());
//            return userJson;
//        }).collect(Collectors.groupingBy(userJson -> userJson.getLong("deptId")));
//        buildTree(zfChildren, collect1);

        // 从字典管理获取人
        List<JSONObject> userJsonList = new ArrayList<>();
        List<DictDetail> duty_record_user = dictDetailRepository.findByDictName("duty_record_user");
        duty_record_user.stream().forEach(dictDetail -> {
            String value = dictDetail.getValue();
            List<User> users = userRepository.findByNickNameAndEnabled(value, true);
            if (users.size() > 0) {
                User user = users.get(0);
                JSONObject userJson = new JSONObject();
                userJson.put("userId", user.getId());
                userJson.put("value", user.getUsername());
                userJson.put("text", user.getNickName());
                userJson.put("deptId", user.getDept().getId());
                userJsonList.add(userJson);
            }
        });
        zf.put("children", userJsonList);
        return roots;
    }

    private void buildTree(List<JSONObject> sdChildren, Map<Long, List<JSONObject>> map) {
        sdChildren.stream().forEach(jsonObject -> {
            Long value = jsonObject.getLong("value");
            String text = jsonObject.getString("text");
            List<Dept> depts = deptRepository.findByPidAndEnabled(value);
            List<JSONObject> userList = map.get(value);
            List<JSONObject> children = new ArrayList<>();
            if (depts.size() > 0) {
                children = depts.stream().map(dept -> {
                    JSONObject json = new JSONObject();
                    json.put("text", dept.getName());
                    json.put("value", dept.getId());
                    return json;
                }).collect(Collectors.toList());

                buildTree(children, map);
            }

            jsonObject.put("children", children);
            if (userList != null) {
                children.addAll(userList);
                JSONObject json = userList.get(0);
                if (json.getString("text").equals(text)) {
                    // 部门名称和单位账号同名时，使用单位账号替换部门节点
                    jsonObject.remove("children");
                    jsonObject.put("value", json.getString("value"));
                    jsonObject.put("userId", json.getLong("userId"));
                    jsonObject.put("deptId", json.getString("deptId"));
                }
            }

        });
    }

    @Override
    public List<User> findByRoleAuthorityKey(String authorityKey) {
        return userRepository.findUsersByAuthorityKey(authorityKey);
    }

    @Override
    public List<Map<String, Object>> findAllByRoleId(Long roleId) {
        List<Map<String, Object>> list = userRepository.findUsersByRoleId(roleId);
        return list;
    }

    @Override
    public List<User> findByEnabledAndUserType(Boolean enabled, Integer userType) {
        return userRepository.findByEnabledAndUserType(enabled, userType);
    }

    @Override
    public List<Map> findByEnabledAndUserTypeList(Boolean enabled, Integer userType, String nickName) {
        List<User> list;
        if (StringUtils.isNotBlank(nickName)) {
            list = userRepository.findByEnabledAndUserTypeAndNickNameLike(enabled, userType, "%" + nickName + "%");
        }else {
            list = userRepository.findByEnabledAndUserType(enabled, userType);
        }
        List<Map> mapList = list.stream().map(temp -> {
            Map map = new HashMap(3);
            map.put("name", temp.getNickName());
            map.put("username", temp.getUsername());
            map.put("id", temp.getId());
            map.put("hasChildren", false);
            map.put("leaf", true);
            map.put("subCount", 0);
            return map;
        }).collect(Collectors.toList());
        return mapList;
    }


    @Override
    public List<UserDto> findSpecialUserByUserName(List<String> userNames) {
        List<User> users = userRepository.findByUsernameIn(userNames);
        List<DictDetailDto> dictDetailList = dictDetailService.getDictByName("getSpecialDept");
        Map<String, String> map = dictDetailList.stream().collect(Collectors.toMap(DictDetailDto::getLabel, DictDetailDto::getValue));
        for (User user : users) {
            String value = map.get(user.getUsername());
            if (value != null) {
                user.getDept().setName(value);
            }
        }
        CollUtil.sortByProperty(users, "dept.deptSort");
        return userMapper.toDto(users);
    }

    @Override
    public Boolean checkWhetherUnitAccount() {
        Integer count = userRepository.checkWhetherUnitAccount(SecurityUtils.getCurrentUserId(), "普通教育厅高校用户");
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePhone(String username, String phone) {
        userRepository.updatePhone(username, phone);
        redisUtils.del(CacheKey.USER_NAME + username);
        flushCache(username);
    }

    @Override
    public UserDto findByPhone(String phone) {
        User user = userRepository.findFirstByPhone(phone);
        return userMapper.toDto(user);
    }

}
