package com.wangda.oa.modules.system.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleUserDto {

    private Long id;

    private String userName;

    private String nickName;

    private String taskId;

    // 部门名称+用户ID
    private String extId;

    public SimpleUserDto(Long id, String userName, String nickName) {
        this.id = id;
        this.userName = userName;
        this.nickName = nickName;
    }
}
