package com.wangda.oa.modules.extension.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/12 上午11:41
 */
@Data
public class CommonControlsBO {

    @ApiModelProperty(value = "类型(0:推荐,1:条线,2:常用组,3:组织架构,4:查询全部组织（实际为人）)")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "推荐类型")
    private String recommendedType;

    @ApiModelProperty(value = "环节(推荐使用)")
    private String link;

    @ApiModelProperty(value = "默认人id集合(比如流程某个环节设置的默认接收人)")
    private List<Long> defaultIds;

    @ApiModelProperty(value = "查询常用组使用,不传查询superAdmin的常用组，传则查询对应用户常用组")
    private Long userId;

    @ApiModelProperty(value = "部门ids,若只查询当前部门底下数据则加入")
    private List<Long> deptIds;

}
