/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.wangda.boot.platform.base.ResultJson;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.exception.EntityExistException;
import com.wangda.oa.modules.extension.config.ZwddProperties;
import com.wangda.oa.modules.extension.domain.SysUserPlatform;
import com.wangda.oa.modules.extension.service.SysUserPlatformService;
import com.wangda.oa.modules.security.service.OnlineUserService;
import com.wangda.oa.modules.system.domain.ChildUser;
import com.wangda.oa.modules.system.domain.User;
import com.wangda.oa.modules.system.domain.vo.ChildUserVO;
import com.wangda.oa.modules.system.repository.ChildUserRepository;
import com.wangda.oa.modules.system.repository.UserRepository;
import com.wangda.oa.modules.system.service.ChildUserService;
import com.wangda.oa.modules.system.service.dto.ChildUserDto;
import com.wangda.oa.modules.system.service.dto.ChildUserQueryCriteria;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.modules.system.service.dto.UserQueryCriteria;
import com.wangda.oa.modules.system.service.mapstruct.ChildUserMapper;
import com.wangda.oa.modules.system.service.mapstruct.UserMapper;
import com.wangda.oa.utils.*;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @data 2022/3/31 16:11
 */
@Service
@RequiredArgsConstructor
@CacheConfig(cacheNames = "childUser")
public class ChildUserServiceImpl implements ChildUserService {

    private final UserRepository mainUserRepository;
    private final ChildUserRepository userRepository;
    private final ChildUserMapper userMapper;
    private final UserMapper mainUserMapper;
    private final RedisUtils redisUtils;
    private final OnlineUserService onlineUserService;
    private final SysUserPlatformService sysUserPlatformService;
    private final ZwddProperties zwddProperties;

    @Override
    public Object queryAll(ChildUserQueryCriteria criteria, Pageable pageable) {
        Page<ChildUser> page = userRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        Map<String, Object> map = PageUtil.toPage(page.map(userMapper::toDto));
        //查询第三方用户
        List<ChildUserDto> list = (List<ChildUserDto>) map.get("content");

        if(CollUtil.isNotEmpty(list)) {
            List<String> usernameList = list.stream().map(userDto -> {
                return userDto.getUsername();
            }).collect(Collectors.toList());
            Map<String, SysUserPlatform> userPlatformMap = new HashMap<>();
            List<SysUserPlatform> userPlatformList = sysUserPlatformService.queryByUsernameInAndType(usernameList, zwddProperties.getType());
            Optional.ofNullable(userPlatformList).orElseGet(Lists::newArrayList).forEach(sysUserPlatform -> {
                userPlatformMap.put(sysUserPlatform.getUserName(), sysUserPlatform);
            });
            SysUserPlatform sysUserPlatform = null;
            for(ChildUserDto u : list) {
                sysUserPlatform = userPlatformMap.get(u.getUsername());
                if(sysUserPlatform != null) {
                    u.setPlatformUserId(sysUserPlatform.getPlatformUserId());
                    u.setPlatformUserName(sysUserPlatform.getPlatformUserName());
                }
            }
        }
        return map;
    }

    @Override
    public List<ChildUserDto> queryAll(UserQueryCriteria criteria) {
        List<ChildUser> users = userRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder));
        return userMapper.toDto(users);
    }

    @Override
    public ResultJson<List<ChildUserDto>> getChildUserListByPid(ChildUserVO vo) {
        List<ChildUser> list = userRepository.findByDeptUsername(vo.getDeptUsername());
        List<ChildUserDto> dtoList = userMapper.toDto(list);
        return ResultJson.generateResult(dtoList);
    }

    @Override
    public void download(List<ChildUserDto> queryAll, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for(ChildUserDto userDTO : queryAll) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("用户名", userDTO.getUsername());
            map.put("昵称", userDTO.getNickName());
            map.put("状态", userDTO.getEnabled() ? "启用" : "禁用");
            map.put("创建日期", userDTO.getCreateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePass(String username, String pass) {
        userRepository.updatePass(username, pass, new Date());
        redisUtils.del("childUser::username:" + username);
    }

    @Override
    public void createAssociatedAccount(ChildUserDto resources) {
        User user = mainUserRepository.findByUsername(resources.getUsername());
        if(user == null) {
            throw new BadRequestException("子用户不存在!");
        }
        if(userRepository.findByUsername(resources.getUsername()) != null) {
            throw new BadRequestException("账号" + resources.getUsername() + "已被单位账号" + resources.getDeptUsername() + "绑定过!");
        }
        UserDto userDto = mainUserMapper.toDto(user);
        ChildUser childUser = new ChildUser();
        childUser.setUsername(userDto.getUsername());
        childUser.setNickName(userDto.getNickName());
        childUser.setPassword(userDto.getPassword());
        childUser.setEnabled(true);
        childUser.setAssociatedType(true);
        childUser.setDeptUsername(resources.getDeptUsername());
        userRepository.save(childUser);
    }


    @Override
    @Cacheable(key = "'id:' + #p0")
    @Transactional(rollbackFor = Exception.class)
    public ChildUserDto findById(long id) {
        ChildUser user = userRepository.findById(id).orElseGet(ChildUser::new);
        ValidationUtil.isNull(user.getId(), "User", "id", id);
        return userMapper.toDto(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ChildUser resources) {
        if(mainUserRepository.findByUsername(resources.getUsername()) != null) {
            throw new EntityExistException(User.class, "username", resources.getUsername());
        }
        if(userRepository.findByUsername(resources.getUsername()) != null) {
            throw new EntityExistException(ChildUser.class, "username", resources.getUsername());
        }
        resources.setAssociatedType(false);
        userRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ChildUser resources) throws Exception {
        ChildUser user = userRepository.findById(resources.getId()).orElseGet(ChildUser::new);
        ValidationUtil.isNull(user.getId(), "User", "id", resources.getId());
        ChildUser user1 = userRepository.findByUsername(resources.getUsername());

        if(user1 != null && !user.getId().equals(user1.getId())) {
            throw new EntityExistException(ChildUser.class, "username", resources.getUsername());
        }
        //若不是关联账号要看用户表名称是否重复
        if(user.getAssociatedType() && mainUserRepository.findByUsername(resources.getUsername()) != null) {
            throw new EntityExistException(User.class, "username", resources.getUsername());
        }

        // 如果用户名称修改
        if(!resources.getUsername().equals(user.getUsername())) {
            redisUtils.del("childUser::username:" + user.getUsername());
        }
        // 如果用户被禁用，则清除用户登录信息
        if(!resources.getEnabled()) {
            onlineUserService.kickOutForUsername(resources.getUsername());
        }
        user.setUsername(resources.getUsername());
        user.setEnabled(resources.getEnabled());
        user.setNickName(resources.getNickName());
        user.setAssociatedType(resources.getAssociatedType());
        userRepository.save(user);
        // 清除缓存
        delCaches(user.getId(), user.getUsername());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Set<Long> ids) {
        for(Long id : ids) {
            // 清理缓存
            ChildUserDto user = findById(id);
            delCaches(user.getId(), user.getUsername());
            //刪除绑定的浙政钉用户
            sysUserPlatformService.deleteByUserNameAndType(user.getUsername(), zwddProperties.getType());
        }
        userRepository.deleteAllByIdIn(ids);
    }

    @Override
    @Cacheable(key = "'username:' + #p0", unless = "#result == null")
    public ChildUserDto findByName(String userName) {
        ChildUser user = userRepository.findByUsername(userName);
        return userMapper.toDto(user);
    }

    /**
     * 清理缓存
     * @param id /
     */
    public void delCaches(Long id, String username) {
        redisUtils.del(CacheKey.CHILD_USER_ID + id);
        redisUtils.del(CacheKey.CHILD_USER_NAME + username);
    }
}
