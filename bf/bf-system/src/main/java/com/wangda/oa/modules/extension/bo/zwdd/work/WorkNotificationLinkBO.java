package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2021/5/24 下午7:16
 */
@Data
@Builder
@ApiModel(description="工作通知链接消息")
public class WorkNotificationLinkBO {

    @ApiModelProperty(value = "消息点击链接地址，当发送消息为小程序时支持小程序跳转链接")
    private String messageUrl;
    @ApiModelProperty(value = "图片地址")
    private String picUrl;
    @ApiModelProperty(value = "消息标题，建议100字符以内")
    private String title;
    @ApiModelProperty(value = "消息描述，建议500字符以内")
    private String text;
    @Tolerate
    public WorkNotificationLinkBO(){}
}
