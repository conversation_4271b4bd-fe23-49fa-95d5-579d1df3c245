package com.wangda.oa.modules.extension.dto.org;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/9 下午7:08
 */
@Data
public class LineTreeDto {

    @ApiModelProperty(value = "类型(0:人员,1:部门)")
    private Integer type;

    @ApiModelProperty(value = "实体id(根据类型不同存储不同id)")
    private Long keyId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "岗位")
    private String post;

    @ApiModelProperty(value = "所属人员或者组织")
    private List<LineTreeDto> children;
}
