/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.service.impl;

import com.wangda.oa.modules.system.domain.BusinessLog;
import com.wangda.oa.modules.system.repository.BusinessLogRepository;
import com.wangda.oa.modules.system.service.BusinessLogService;
import com.wangda.oa.modules.system.service.dto.BusinessLogCriteria;
import com.wangda.oa.modules.system.service.mapstruct.BusinessLogMapper;
import com.wangda.oa.utils.PageUtil;
import com.wangda.oa.utils.QueryHelp;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @data 2022/4/6 11:11
 */
@Service
@RequiredArgsConstructor
public class BusinessLogServiceImpl implements BusinessLogService {

    private final BusinessLogRepository businessLogRepository;

    private final BusinessLogMapper businessLogMapper;

    @Override
    public Object queryAll(BusinessLogCriteria criteria, Pageable pageable) {
        Page<BusinessLog> page = businessLogRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(businessLogMapper::toDto));
    }

    @Override
    public void create(BusinessLog resources) {
        businessLogRepository.save(resources);
    }
}
