package com.wangda.oa.modules.extension.dto.org;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/26 下午6:26
 */
@Data
public class OrgPhoneListDto {
    @ApiModelProperty(value = "部门id")
    private Long id;
    @ApiModelProperty(value = "用户名")
    private String userName;
    @ApiModelProperty(value = "树id")
    private Long treeId;
    @ApiModelProperty(value = "部门名称")
    private String nickName;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "type(0:人,1:部门)")
    private Integer type;
    @ApiModelProperty(value = "序号")
    private Integer sort;
    @ApiModelProperty(value = "父id")
    private Long pid;
    @ApiModelProperty(value = "部门名称")
    private String deptName;
    @ApiModelProperty(value = "所属人员或者组织")
    private List<OrgPhoneListDto> children;
}
