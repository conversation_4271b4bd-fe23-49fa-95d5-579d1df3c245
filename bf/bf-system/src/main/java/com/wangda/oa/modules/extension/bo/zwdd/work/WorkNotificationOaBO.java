package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/24 下午7:16
 */
@Data
@Builder
@ApiModel(description="工作通知OA消息")
public class WorkNotificationOaBO {

    @ApiModelProperty(value = "消息点击链接地址，当发送消息为小程序时支持小程序跳转链接")
    private String message_url;
    @ApiModelProperty(value = "PC端点击消息时跳转到的地址")
    private String pc_message_url;
    @ApiModelProperty(value = "消息头部内容")
    private Head head;
    @ApiModelProperty(value = "消息体")
    private Body body;
    @Data
    class Head{
        @ApiModelProperty(value = "消息头部的背景颜色。长度限制为8个英文字符，其中前6位表示颜色值，后2为表示透明度。")
        private String bgcolor;
        @ApiModelProperty(value = "消息的头部标题 (向普通会话发送时有效，向企业会话发送时会被替换为微应用的名字)，长度限制为最多10个字符")
        private String text;
    }
    @Data
    class Body{
        @ApiModelProperty(value = "消息体的标题，建议50个字符以内")
        private String title;
        @ApiModelProperty(value = "消息体的表单，最多显示6个，超过会被隐藏")
        private List<Form> from;
        @ApiModelProperty(value = "单行富文本信息")
        private Rich rich;
        @ApiModelProperty(value = "消息体的内容，最多显示3行")
        private String content;
        @ApiModelProperty(value = "消息体中的图片，支持图片资源@mediaId")
        private String image;
        @ApiModelProperty(value = "自定义的附件数目。此数字仅供显示，钉钉不作验证")
        private String file_count;
        @ApiModelProperty(value = "自定义的作者名字")
        private String author;
        @Data
        class Form{
           @ApiModelProperty(value = "消息体的关键字")
           private String key;
           @ApiModelProperty(value = "消息体的关键字对应的值")
           private String value;
        }
        @Data
        class Rich{
           @ApiModelProperty(value = "单行富文本信息的数目")
           private String num;
           @ApiModelProperty(value = "单行富文本信息的单位")
           private String unit;
        }
    }
    @Tolerate
    WorkNotificationOaBO(){}
}
