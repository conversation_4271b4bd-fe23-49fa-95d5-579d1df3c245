package com.wangda.oa.modules.extension.bo;

import com.wangda.oa.annotation.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/8 下午2:41
 */
@Data
@Builder
public class PositionListBO {

    @ApiModelProperty(value = "部门id")
    @Query(type=Query.Type.EQUAL)
    private Long deptId;

    @ApiModelProperty(value = "类别")
    @Query(type=Query.Type.EQUAL)
    private String type;

    @ApiModelProperty(value = "用户名")
    @Query(type=Query.Type.EQUAL)
    private String name;

    @ApiModelProperty(value = "部门id集合")
    @Query(type=Query.Type.IN, propName = "deptId")
    private List<Long> deptIds;

    @Tolerate
    PositionListBO(){}
}
