package com.wangda.oa.modules.security.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/14 上午9:20
 */
@Data
@ApiModel(description="app登录返回信息")
public class SysAppLoginDto {
    @ApiModelProperty(value = "(类型 1:返回token,2:有多个用户,返回uid,3:不存在用户,返回uid)")
    private int type;
    @ApiModelProperty(value = "value")
    private Object value;
    @ApiModelProperty(value = "用户对象")
    private JwtUserDto user;
}
