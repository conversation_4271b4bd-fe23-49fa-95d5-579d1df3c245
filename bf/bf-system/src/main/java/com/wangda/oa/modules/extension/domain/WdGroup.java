package com.wangda.oa.modules.extension.domain;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "WD_GROUP")
public class WdGroup extends BaseDomain {

    @Column(name = "name")
    @ApiModelProperty(value = "名称(人名或者组织名)")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Column(name = "user_id")
    @ApiModelProperty(value = "人id")
    private Long userId;

    @Column(name = "username")
    @ApiModelProperty(value = "用户名")
    private String username;

    @Column(name = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @Column(name = "type")
    @ApiModelProperty(value = "类型(0:组,1:人)")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Column(name = "pid")
    @ApiModelProperty(value = "父类id,顶级父类id为0")
    @NotNull(message = "父类id不能为空")
    private Long pid;

}
