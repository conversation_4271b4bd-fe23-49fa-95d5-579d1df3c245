package com.wangda.oa.modules.system.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleChildUserDto {

    private Long id;

    private String userName;

    private String nickName;

    private String taskId;

    private SimpleUserDto userDto;

    public SimpleChildUserDto(Long id, String userName, String nickName,SimpleUserDto simpleUserDto) {
        this.id = id;
        this.userName = userName;
        this.nickName = nickName;
        this.userDto=simpleUserDto;
    }
}
