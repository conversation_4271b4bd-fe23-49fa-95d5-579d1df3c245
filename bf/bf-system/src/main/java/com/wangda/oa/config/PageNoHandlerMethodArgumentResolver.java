package com.wangda.oa.config;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.core.MethodParameter;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableArgumentResolver;
import org.springframework.data.web.PageableHandlerMethodArgumentResolver;
import org.springframework.data.web.SortArgumentResolver;
import org.springframework.data.web.SortHandlerMethodArgumentResolver;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR>
 * /**
 *
 * *从web请求中提取分页信息，从而允许将{@link Pageable}实例注入控制器
 *
 * *方法。可以配置要分析的请求属性。默认配置使用请求参数
 *
 *使用 {@link #DEFAULT_PAGE_PARAMETER}.
 *
 * *
 */
public class PageNoHandlerMethodArgumentResolver extends PageableHandlerMethodArgumentResolver implements PageableArgumentResolver {

    private static final SortHandlerMethodArgumentResolver DEFAULT_SORT_RESOLVER = new SortHandlerMethodArgumentResolver();

    private SortArgumentResolver sortResolver;
    // 前端可能传过来pageNo 而不是JPA默认的page的参数
    private static String DEFAULT_PAGE_PARAMETER = "pageNo";
    private String pageParameterName = DEFAULT_PAGE_PARAMETER;

    public PageNoHandlerMethodArgumentResolver(SortHandlerMethodArgumentResolver sortResolver) {
        this((SortArgumentResolver) sortResolver);
    }

    /**
     * Constructs an instance of this resolver with the specified {@link SortArgumentResolver}.
     *
     * @param sortResolver the sort resolver to use
     * @since 1.13
     */
    public PageNoHandlerMethodArgumentResolver(@Nullable SortArgumentResolver sortResolver) {
        this.sortResolver = sortResolver == null ? DEFAULT_SORT_RESOLVER : sortResolver;
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return Pageable.class.equals(methodParameter.getParameterType());
    }

    @Override
    public Pageable resolveArgument(MethodParameter methodParameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        String pageNo = webRequest.getParameter(getParameterNameToUse(getPageParameterName(), methodParameter));
        // 判断前端传递来的参数是pageNo还是page
        boolean isPageNo = ObjectUtil.isNotNull(pageNo);
        if(isPageNo){
            //因为 PageableHandlerMethodArgumentResolver是单例对象在多线程环境下成员变量有问题所以没有调用父类，这里new了一个分页解析器
            PageableHandlerMethodArgumentResolver pageableHandlerMethodArgumentResolver = new PageableHandlerMethodArgumentResolver(this.sortResolver);
            pageableHandlerMethodArgumentResolver.setPageParameterName("pageNo");
            pageableHandlerMethodArgumentResolver.setOneIndexedParameters(true);
            return pageableHandlerMethodArgumentResolver.resolveArgument(methodParameter, mavContainer, webRequest, binderFactory);
        }else{
            Pageable pageable = super.resolveArgument(methodParameter, mavContainer, webRequest, binderFactory);
            return pageable;
        }

    }

    public String getPageParameterName() {
        return pageParameterName;
    }
}
