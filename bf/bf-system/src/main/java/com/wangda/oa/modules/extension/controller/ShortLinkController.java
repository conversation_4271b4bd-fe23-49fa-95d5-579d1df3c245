package com.wangda.oa.modules.extension.controller;

import com.wangda.boot.platform.enums.ResultCodeEnum;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.modules.extension.bo.ShortLinkQueryCriteria;
import com.wangda.oa.modules.extension.domain.ShortLink;
import com.wangda.oa.modules.extension.service.ShortLinkService;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * 短链接
 */
@Controller
@RequestMapping("/d")
public class ShortLinkController {

    @Resource
    private ShortLinkService shortLinkService;

    @ApiOperation("短链接生成")
    @GetMapping("/create")
    @ResponseBody
    public String create(String url, Long expiryDay) {
        if(Objects.isNull(expiryDay)) {
            expiryDay = 0L;
        }
        return shortLinkService.create(url, expiryDay);
    }

    @ApiOperation("短链接跳转")
    @AnonymousAccess
    @GetMapping("/{code}")
    public ResponseEntity redirect(@PathVariable String code, HttpServletResponse response) throws Exception {
        ShortLink shortLink = shortLinkService.get(code);
        if(shortLink == null){
            return ResponseEntity.status(ResultCodeEnum.INTERFACE_ADDRESS_INVALID.getCode()).body("链接不存在或已失效");
        }
        response.sendRedirect(shortLink.getLongUrl());
        return null;
    }

    @ApiOperation("获取长连接")
    @GetMapping("/get/{code}")
    public ResponseEntity<Object> getUrl(@PathVariable String code) {
        ShortLink shortLink = shortLinkService.get(code);
        if(shortLink == null){
            return ResponseEntity.status(ResultCodeEnum.INTERFACE_ADDRESS_INVALID.getCode()).body("链接不存在或已失效");
        }
        return ResponseEntity.ok(shortLink.getLongUrl());
    }

    @ApiOperation("短链接列表")
    @GetMapping("/queryShortLink")
    public ResponseEntity<Object> queryShortLink(ShortLinkQueryCriteria criteria, Pageable pageable) {
        Page page = shortLinkService.queryAll(criteria, pageable);
        return ResponseEntity.ok(page);
    }

    @ApiOperation("短链接保存")
    @PostMapping("/save")
    @ResponseBody
    public ResponseEntity save(@Validated @RequestBody ShortLink shortLink) {
        shortLinkService.save(shortLink);
        return ResponseEntity.ok("保存成功");
    }

    @Log("删除短链接")
    @ApiOperation("删除短链接")
    @PostMapping("/delete")
    public ResponseEntity<Object> delete(@RequestBody Set<Long> ids){
        shortLinkService.delete(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
