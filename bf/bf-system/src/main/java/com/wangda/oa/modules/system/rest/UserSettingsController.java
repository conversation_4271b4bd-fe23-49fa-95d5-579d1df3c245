package com.wangda.oa.modules.system.rest;

import com.wangda.oa.annotation.Log;
import com.wangda.oa.modules.system.domain.UserSettings;
import com.wangda.oa.modules.system.service.UserSettingsService;
import com.wangda.oa.modules.system.service.dto.UserSettingsQueryCriteria;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @program: oa-mgt-server
 * @description: 用户个性化配置
 * @author: maogy
 * @create: 2021-12-03 17:24
 **/
@Api(tags = "用户配置")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/usersettings")
public class UserSettingsController {
    private final UserSettingsService userSettingsService;

    @ApiOperation("分页查询")
    @GetMapping("/queryPage")
    public ResponseEntity<Object> queryPage(UserSettingsQueryCriteria queryCriteria, Pageable pageable) {
        return new ResponseEntity(userSettingsService.queryPage(queryCriteria, pageable), HttpStatus.OK);
    }


    @Log("新增用户配置")
    @ApiOperation("新增用户配置")
    @PostMapping("/add")
    public ResponseEntity<UserSettings> add(@RequestBody @ApiParam(name = "新增物品对象", value = "传入json格式", required = true) UserSettings userSettings) {
        return new ResponseEntity(userSettingsService.add(userSettings), HttpStatus.OK);
    }


    @Log("修改用户配置")
    @ApiOperation("修改用户配置")
    @PostMapping("/update")
    public ResponseEntity<UserSettings> update(@RequestBody @ApiParam(name = "修改物品对象", value = "传入json格式", required = true) UserSettings userSettings) {
        return new ResponseEntity(userSettingsService.add(userSettings), HttpStatus.OK);
    }


    @Log("配置删除")
    @ApiOperation("配置删除")
    @PostMapping("deleteByIds")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "删除id", dataType = "int")
    })
    public ResponseEntity deleteById(@RequestBody Long[] ids) {
        userSettingsService.deleteByIds(ids);
        return new ResponseEntity(HttpStatus.OK);
    }

    @ApiOperation("根据用户名查找")
    @PostMapping("findByUserName")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名", dataType = "string")
    })
    public ResponseEntity<UserSettings> findByUserName(String userName) {
        return new ResponseEntity(userSettingsService.findByUserName(userName), HttpStatus.OK);
    }
}
