/*
 *  Copyright 2019-2020 Zheng <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.wangda.oa.modules.system.utils;

/**
 * 常用静态常量
 * <AUTHOR> <PERSON>
 * @date 2018-12-26
 */
public class SystemConstant {

    /**
     * redis-推荐人文件名
     */
    public static final String recommend_file = "recommend:";

    /**
     * 通用控件-类型-推荐
     */
    public static final int COMMON_CONTROLS_TYPE_RECOMMENDED = 0;


    /**
     * 通用控件-类型-条线
     */
    public static final int COMMON_CONTROLS_TYPE_LINE = 1;


    /**
     * 通用控件-类型-常用组
     */
    public static final int COMMON_CONTROLS_TYPE_GROUP = 2;


    /**
     * 通用控件-类型-组织架构
     */
    public static final int COMMON_CONTROLS_TYPE_OGR = 3;

    /**
     * 通用控件-类型-人的组织架构
     */
    public static final int COMMON_CONTROLS_TYPE_ORG_PERPLE = 4;

    /**
     * 通讯录通用控件-类型-推荐
     */
    public static final int COMMON_PHONE_CONTROLS_TYPE_OGR = 0;

    public static final int NUMBER_ZERO = 0;

    /**
     * 1
     */
    public static final int NUMBER_ONE = 1;

    /**
     * 9
     */
    public static final int NUMBER_NINE = 9;

    /**
     * 10
     */
    public static final int NUMBER_TEN = 10;

    public static final int NUMBER_MAX_HUNDRED = 999;

    /**
     * 默认接口token有效期限，1周
     */
    public static final long DATA_TOKEN_OVER_DUETIME = 1000 * 60 * 60 * 24 * 7L;
    public static final long DATA_TOKEN_OVER_DUETIME_ONE = 1000 * 60 * 60 * 8L;

    /**
     * 默认key
     */
    public static final String DEFAULT_KEY = "SUPERADMIN_DEFAULT";

    /**
     * 模拟登陆-默认密码
     */
    public static final String SIMULATION_PASSWORD = "hzwd123456";
}
