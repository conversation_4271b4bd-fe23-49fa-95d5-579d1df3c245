package com.wangda.oa.modules.extension.bo.zwdd.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/7/19 上午10:35
 */
@Data
@ApiModel(description="我发起的对外接口")
public class TaskUserNameListBO {
    @ApiModelProperty(value = "发起人username",required = true)
    private String username;
    @ApiModelProperty(value = "页面大小",required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "完成时间end")
    private String finishEndTime;
    @ApiModelProperty(value = "创建时间start")
    private String createStartTime;
    @ApiModelProperty(value = "创建时间end")
    private String createEndTime;
    @ApiModelProperty(value = "页码",required = true)
    private Integer pageNo;
    @ApiModelProperty(value = "应用key")
    private String appKey;
    @ApiModelProperty(value = "完成时间end")
    private String finishStartTime;
    @ApiModelProperty(value = "关键字")
    private String keyword;
    @ApiModelProperty(value = "只有我发起的对外接口有用。状态(0:待处理,1:取消,2:完成)")
    private Integer status;

}
