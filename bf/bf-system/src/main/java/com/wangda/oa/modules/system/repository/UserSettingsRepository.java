package com.wangda.oa.modules.system.repository;

import com.wangda.oa.modules.system.domain.UserSettings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
* Description: 用户个性化配置持久层
* @return:
* @Date: 2021/12/3 17:16
* @Author: maogy
*/
public interface UserSettingsRepository extends JpaRepository<UserSettings, Long>, JpaSpecificationExecutor<UserSettings> {

    /**
     * 批量删除
     * @param ids
     * @return
     */
    int deleteAllByIdIn(Long[] ids);

    /**
    * Description: 根据用户名查找数据
    * @param userName
    * @return: com.wangda.oa.modules.system.domain.UserSettings
    * @Date: 2021/12/3 19:19
    * @Author: maogy
    * @throws:
    */
    UserSettings findFirstByUserName(String userName);
}
