package com.wangda.oa.modules.system.service.dto;

import com.wangda.oa.base.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 操作日志表
 * <AUTHOR>
 * @data 2022/4/6 09:26
 */
@Data
public class BusinessLogDto extends BaseDTO {

    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "操作类别")
    private String operationType;

    @ApiModelProperty(value = "业务类别")
    private String businessType;

    @ApiModelProperty(value = "业务id")
    private String businessId;

    @ApiModelProperty(value = "描述")
    private String represent;

    @ApiModelProperty(value = "用户对象")
    private SimpleChildUserDto user;


}
