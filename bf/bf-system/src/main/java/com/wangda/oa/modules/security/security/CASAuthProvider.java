package com.wangda.oa.modules.security.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

public class CASAuthProvider implements AuthenticationProvider {

    @Autowired
    @Qualifier("userDetailsService")
    UserDetailsService userDetailsService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        CASAuthToken authenticationToken = (CASAuthToken) authentication;
        Object principal = authenticationToken.getPrincipal();
        UserDetails userDetails = userDetailsService.loadUserByUsername((String) principal);
        CASAuthToken result = new CASAuthToken(userDetails.getAuthorities(), userDetails);
        return result;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return (CASAuthToken.class.isAssignableFrom(authentication));
    }
}
