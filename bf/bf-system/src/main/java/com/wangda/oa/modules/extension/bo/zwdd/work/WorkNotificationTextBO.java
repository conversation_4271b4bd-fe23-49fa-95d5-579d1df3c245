package com.wangda.oa.modules.extension.bo.zwdd.work;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * <AUTHOR>
 * @date 2021/5/24 下午7:16
 */
@Data
@Builder
@ApiModel(description="工作通知文本类型")
public class WorkNotificationTextBO {

    @ApiModelProperty(value = "消息内容，建议500字符以内")
    private String content;

    @Tolerate
    public WorkNotificationTextBO(){}
}
