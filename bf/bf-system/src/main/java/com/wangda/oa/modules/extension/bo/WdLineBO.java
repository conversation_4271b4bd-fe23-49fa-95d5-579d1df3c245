package com.wangda.oa.modules.extension.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class WdLineBO{

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "类型(0:人员,1:部门)")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty(value = "实体id(根据类型不同存储不同id)")
    @NotNull(message = "实体id不能为空")
    private Long keyId;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "父类id")
    @NotNull(message = "父类id不能为空")
    private Long pid;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;

}
