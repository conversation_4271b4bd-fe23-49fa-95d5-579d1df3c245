<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wangda.oa.modules.extension.mapper.WdSysOptionMapper" >
    <resultMap id="UserDataDto" type="com.wangda.oa.modules.extension.dto.UserDataDto">
        <id column="user_id" javaType="long" jdbcType="INTEGER" property="userId"/>
        <result column="dept_id" javaType="long" jdbcType="INTEGER" property="deptId" />
        <result column="username" javaType="string" jdbcType="VARCHAR" property="username" />
        <result column="nick_name" javaType="string" jdbcType="VARCHAR" property="nickName" />
        <result column="email" javaType="string" jdbcType="VARCHAR" property="email" />
        <result column="phone" javaType="string" jdbcType="VARCHAR" property="phone" />
        <result column="gender" javaType="string" jdbcType="VARCHAR" property="gender" />
        <result column="is_admin" javaType="boolean" jdbcType="BIT" property="isAdmin" />
        <result column="create_time" javaType="java.sql.Date" jdbcType="DATE" property="createTime" />
        <result column="post" javaType="string" jdbcType="VARCHAR" property="post" />
        <result column="sort" javaType="INTEGER" jdbcType="INTEGER" property="sort" />
        <result column="deptName" javaType="string" jdbcType="VARCHAR" property="deptName" />

        <collection property="concurDept" ofType="long" javaType="arraylist" >

            <constructor>
                <arg column="concur_dept"/>
            </constructor>
        </collection>
    </resultMap>
    <select id="getUserListByEnabled" resultMap="UserDataDto">
        select us.*, de.dept_id concur_dept,dept.name as deptName from sys_user us left join sys_users_depts de on us.user_id = de.user_id left join sys_dept dept on dept.dept_id = de.dept_id where us.enabled=1 order by us.sort
    </select>
    <select id="getLeadershipList" resultType="com.wangda.oa.modules.extension.dto.LeadershipListDto">
        SELECT distinct
            a.*
        FROM
            sys_user a
            LEFT JOIN sys_dept_user_position b ON a.user_id = b.user_id
        WHERE
            a.enabled = 1
            AND b.type = 'hallLeadership'
            <if test="username!=null and username!=''">
              and a.username=#{username}
            </if>
        ORDER BY
            b.sort
    </select>
    <select id="getUserList" resultType="com.wangda.oa.modules.extension.dto.org.UserListDto">
        select user_id as id,nick_name,username as userName from sys_user where enabled=1
        <if test="name!=null and name!=''">
            and nick_name like CONCAT(CONCAT('%', #{name}), '%')
        </if>
    </select>
    <select id="getUserPhoneList" resultType="com.wangda.oa.modules.extension.dto.org.UserPhoneListDto">
        select user_id as id,nick_name,username as userName,phone from sys_user where enabled=1
        <if test="name!=null and name!=''">
            and nick_name like CONCAT(CONCAT('%', #{name}), '%')
        </if>
    </select>
    <select id="getUserAndFirstJobList" resultType="com.wangda.oa.modules.extension.dto.UserDataDto">
        SELECT
            a.user_id,
            a.nick_name,
            WMSYS.WM_CONCAT(c.name
            )AS post
        FROM
            sys_user a
            LEFT JOIN sys_users_jobs b ON a.user_id = b.user_id  and a.enabled=1
            LEFT JOIN sys_job c ON c.job_id = b.job_id and c.enabled=1
        GROUP BY
            a.user_id,
            a.nick_name
    </select>
</mapper>
