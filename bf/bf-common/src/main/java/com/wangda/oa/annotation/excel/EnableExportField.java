package com.wangda.oa.annotation.excel;

import com.wangda.oa.utils.enums.ColorEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EnableExportField {
    int colWidth() default 100;
    String colName();
    String useGetMethod() default "";
    ColorEnum cellColor() default ColorEnum.BLUE;
}
