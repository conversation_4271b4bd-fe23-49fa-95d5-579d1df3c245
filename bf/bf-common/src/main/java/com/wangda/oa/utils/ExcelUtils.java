package com.wangda.oa.utils;

import com.alibaba.fastjson.util.TypeUtils;
import com.wangda.oa.annotation.excel.EnableExport;
import com.wangda.oa.annotation.excel.EnableExportField;
import com.wangda.oa.annotation.excel.EnableSelectList;
import com.wangda.oa.annotation.excel.ImportIndex;
import com.wangda.oa.utils.enums.ColorEnum;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class ExcelUtils {

    public static final Map<Integer, Map<String, String>> ALL_SELECT_LIST_MAP = new HashMap<>();

    public static List<?> parseExcelToList(InputStream excel, Class clazz) {
        List<Object> res = new ArrayList<>();
        InputStream is = null;
        Sheet sheet = null;
        try {
            is = excel;
            if (is != null) {
                Workbook workbook = WorkbookFactory.create(is);
                sheet = workbook.getSheetAt(0);
                if (sheet != null) {
                    int i = 2;
                    String[] values;
                    Row row = sheet.getRow(i);
                    while (row != null) {
                        int cellNum = row.getPhysicalNumberOfCells();
                        values = new String[cellNum];
                        for (int j = 0; j < cellNum; j++) {
                            Cell cell = row.getCell(j);
                            if (cell != null) {
                                cell.setCellType(CellType.STRING);
                                String value = cell.getStringCellValue();
                                values[j] = value;
                            }
                        }
                        Field[] fields = clazz.getDeclaredFields();
                        Object obj = clazz.newInstance();
                        for (Field field : fields) {
                            if (field.isAnnotationPresent(ImportIndex.class)) {
                                ImportIndex annotation = field.getAnnotation(ImportIndex.class);
                                int index = annotation.index();
                                String useSetMethodName = annotation.useSetMethodName();
                                String value = values[index];
                                if (field.isAnnotationPresent(EnableSelectList.class)) {
                                    value = getKeyByValue(ALL_SELECT_LIST_MAP.get(index), value);
                                }
                                if (StringUtils.isNotEmpty(useSetMethodName)) {
                                    Object val = TypeUtils.cast(value, field.getType(), null);
                                    field.setAccessible(true);
                                    Method method = clazz.getMethod(useSetMethodName, new Class[]{field.getType(), Object.class});
                                    method.setAccessible(true);
                                    method.invoke(obj, new Object[]{field.get(obj), val});
                                } else {
                                    field.setAccessible(true);
                                    Object val = TypeUtils.cast(value, field.getType(), null);
                                    field.set(obj, value);
                                }
                            }
                        }
                        res.add(obj);
                        row = sheet.getRow(++i);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    private static String getKeyByValue(Map<String, String> map, String value) {
        if (map != null) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (StringUtils.isNotEmpty(entry.getValue()) && value.equals(entry.getValue())) {
                    return entry.getKey();
                }
            }
        } else {
            return value;
        }
        return null;
    }

    public static void exportExcel(OutputStream os, List dataList, Class clazz, Map<Integer, Map<String, String>> selectListMap, String title) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet();
        sheet.setDefaultRowHeight((short) (20 * 20));
        if (clazz.isAnnotationPresent(EnableExport.class)) {
            EnableExport enableExport = (EnableExport) clazz.getAnnotation(EnableExport.class);
            List<String> columns = new ArrayList<>();
            List<Field> fields = new ArrayList<>();
            List<ColorEnum> colorEnums = new ArrayList<>();
            for (Field field : clazz.getDeclaredFields()) {
                if (field.isAnnotationPresent(EnableExportField.class)) {
                    EnableExportField enableExportField = (EnableExportField) field.getAnnotation(EnableExportField.class);
                    columns.add(enableExportField.colName());
                    fields.add(field);
                    colorEnums.add(enableExportField.cellColor());
                }
            }
            for (int i = 0; i < fields.size(); i++) {
                Field field = fields.get(i);
                sheet.setColumnWidth(i, field.getAnnotation(EnableExportField.class).colWidth() * 20);
            }
            XSSFRow row = null;
            XSSFCell cell = null;
            String fileName = enableExport.fileName();
            if (StringUtils.isNotEmpty(title)) {
                fileName = title;
            }
            createTitle(workbook, row, cell, sheet, columns.size() - 1, fileName, enableExport.cellColor());
            createHeadRow(workbook, row, cell, sheet, columns, colorEnums);
            try {
                XSSFCellStyle cellStyle = getBasicCellStyle(workbook);
                int i = 0;
                for (Object o : dataList) {
                    row = sheet.createRow(i + 2);
                    for (int j = 0; j < fields.size(); j++) {
                        Field field = fields.get(j);
                        field.setAccessible(true);
                        Object value = field.get(o);
                        EnableExportField enableExportField = field.getAnnotation(EnableExportField.class);
                        String useGetMethod = enableExportField.useGetMethod();
                        if (StringUtils.isNotEmpty(useGetMethod)) {
                            Method method = clazz.getMethod(useGetMethod, new Class[]{field.getType()});
                            method.setAccessible(true);
                            value = method.invoke(o, new Object[]{value});
                        }
                        if (field.isAnnotationPresent(EnableSelectList.class)) {
                            if (selectListMap != null && selectListMap.get(j) != null) {
                                value = selectListMap.get(j).get(value);
                            }
                        }
                        setCellValue(value, cell, row, cellStyle, j);
                    }
                    i++;
                }
                createDataValidation(sheet, selectListMap);
                workbook.write(os);
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private static void createDataValidation(XSSFSheet sheet, Map<Integer, Map<String, String>> selectListMap) {
        if (selectListMap!=null) {
            for (Map.Entry<Integer, Map<String, String>> entry : selectListMap.entrySet()) {
                Integer key = entry.getKey();
                Map<String, String> value = entry.getValue();
                if (value.size()>0) {
                    int i = 0;
                    String[] values = new String[value.size()];
                    for (Map.Entry<String, String> stringEntry : value.entrySet()) {
                        values[i++] = stringEntry.getValue();
                    }
                    CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(2, 65535, key, key);
                    DataValidationHelper dataValidationHelper = sheet.getDataValidationHelper();
                    DataValidationConstraint explicitListConstraint = dataValidationHelper.createExplicitListConstraint(values);
                    DataValidation validation = dataValidationHelper.createValidation(explicitListConstraint, cellRangeAddressList);
                    validation.setSuppressDropDownArrow(true);
                    validation.setShowErrorBox(true);
                    validation.setEmptyCellAllowed(true);
                    validation.setShowPromptBox(true);
                    validation.createPromptBox("提示","只能选择下拉框中的数据");
                    sheet.addValidationData(validation);
                }
            }
        }
    }

    private static void setCellValue(Object value, XSSFCell cell, XSSFRow row, XSSFCellStyle cellStyle, int j) {
        String s = value!=null?String.valueOf(value):"";
        cell = row.createCell(j);
        if (isNumeric(s)) {
            cell.setCellStyle(cellStyle);
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue(Double.valueOf(s));
        } else {
            cell.setCellStyle(cellStyle);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(s);
        }
    }

    private static boolean isNumeric(String s) {
        Pattern pattern = Pattern.compile("[-+]?(([0-9]+)([.]([0-9]+))?|([.]([0-9]+))?)$");
        if (StringUtils.isNotEmpty(s)) {
            Matcher matcher = pattern.matcher(s);
            if (matcher.matches()) {
                if (!s.contains(".") && s.startsWith("0")) {
                    return false;
                }
                return true;
            }
        }
        return false;
    }

    private static void createHeadRow(XSSFWorkbook workbook, XSSFRow row, XSSFCell cell, XSSFSheet sheet, List<String> columns, List<ColorEnum> colorEnums) {
        row = sheet.createRow(1);
        for (int i = 0; i < columns.size(); i++) {
            cell = row.createCell(i);
            cell.setCellStyle(getTitleCellStyle(workbook, colorEnums.get(i)));
            cell.setCellType(CellType.STRING);
            cell.setCellValue(columns.get(i));
        }
    }

    private static void createTitle(XSSFWorkbook workbook, XSSFRow row, XSSFCell cell, XSSFSheet sheet, int i, String fileName, ColorEnum cellColor) {
        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, 0, i);
        sheet.addMergedRegion(cellRangeAddress);

        RegionUtil.setBorderTop(BorderStyle.THIN, cellRangeAddress, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, cellRangeAddress, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, cellRangeAddress, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, cellRangeAddress, sheet);

        row = sheet.getRow(0);
        cell = row.getCell(0);
        cell.setCellType(CellType.STRING);
        cell.setCellStyle(getTitleCellStyle(workbook, cellColor));
        cell.setCellValue(fileName);
    }

    private static CellStyle getTitleCellStyle(XSSFWorkbook workbook, ColorEnum cellColor) {
        XSSFCellStyle cellStyle = getBasicCellStyle(workbook);
        cellStyle.setFillForegroundColor(cellColor.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    private static XSSFCellStyle getBasicCellStyle(XSSFWorkbook workbook) {
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setWrapText(true);
        return cellStyle;
    }
}
