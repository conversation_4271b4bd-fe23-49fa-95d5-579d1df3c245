package com.wangda.oa.annotation.excel;

import com.wangda.oa.utils.enums.ColorEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface EnableExport {
    String fileName();

    ColorEnum cellColor() default ColorEnum.BLUE;
}
