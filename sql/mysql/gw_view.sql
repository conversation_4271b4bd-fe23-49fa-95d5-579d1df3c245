CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`%` SQL SECURITY DEFINER VIEW `gw_view`
AS SELECT
              `a07_document_fw`.`id` AS `id`,
              `a07_document_fw`.`bt` AS `bt`,
              `a07_document_fw`.`bpm_status` AS `bpm_status`,
              `a07_document_fw`.`create_by` AS `cjr`,
              `a07_document_fw`.`create_time` AS `create_time`,'documentPost' AS `module_type`,
              `a07_document_fw`.`bpm_instance_id` AS `bpm_instance_id`
   FROM `a07_document_fw`
   union all select `a07_document_sw`.`id` AS `id`,`a07_document_sw`.`bt` AS `bt`,`a07_document_sw`.`bpm_status` AS `bpm_status`,`a07_document_sw`.`create_by` AS `cjr`,`a07_document_sw`.`create_time` AS `create_time`,'documentAddressee' AS `module_type`,`a07_document_sw`.`bpm_instance_id` AS `bpm_instance_id` from `a07_document_sw`
order by `create_time` desc;
