INSERT INTO "SYS_QUARTZ_JOB"("JOB_ID", "BEAN_NAME", "CRON_EXPRESSION", "IS_PAUSE", "JOB_NAME", "METHOD_NAME", "PARAMS", "DESCRIPTION", "PERSON_IN_CHARGE", "EMAIL", "SUB_TASK", "PAUSE_AFTER_FAILURE", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('2', 'testTask', '0/5 * * * * ?', NULL, '测试1', 'run1', 'test', '带参测试，多参使用json', '测试', NULL, NULL, NULL, NULL, 'admin', TO_DATE('2019-08-22 14:08:29', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-05-24 13:58:33', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_QUARTZ_JOB"("JOB_ID", "BEAN_NAME", "CRON_EXPRESSION", "IS_PAUSE", "JOB_NAME", "METHOD_NAME", "PARAMS", "DESCRIPTION", "PERSON_IN_CHARGE", "EMAIL", "SUB_TASK", "PAUSE_AFTER_FAILURE", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('3', 'testTask', '0/5 * * * * ?', NULL, '测试', 'run', NULL, '不带参测试', 'Zheng Jie', NULL, '5,6', 1, NULL, 'admin', TO_DATE('2019-09-26 16:44:39', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-05-24 14:48:12', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_QUARTZ_JOB"("JOB_ID", "BEAN_NAME", "CRON_EXPRESSION", "IS_PAUSE", "JOB_NAME", "METHOD_NAME", "PARAMS", "DESCRIPTION", "PERSON_IN_CHARGE", "EMAIL", "SUB_TASK", "PAUSE_AFTER_FAILURE", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('5', 'Test', '0/5 * * * * ?', NULL, '任务告警测试', 'run', NULL, '测试', 'test', NULL, NULL, 1, 'admin', 'admin', TO_DATE('2020-05-05 20:32:41', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-05-05 20:36:13', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_QUARTZ_JOB"("JOB_ID", "BEAN_NAME", "CRON_EXPRESSION", "IS_PAUSE", "JOB_NAME", "METHOD_NAME", "PARAMS", "DESCRIPTION", "PERSON_IN_CHARGE", "EMAIL", "SUB_TASK", "PAUSE_AFTER_FAILURE", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('6', 'testTask', '0/5 * * * * ?', NULL, '测试3', 'run2', NULL, '测试3', 'Zheng Jie', NULL, NULL, 1, 'admin', 'admin', TO_DATE('2020-05-05 20:35:41', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-05-05 20:36:07', 'SYYYY-MM-DD HH24:MI:SS'));
