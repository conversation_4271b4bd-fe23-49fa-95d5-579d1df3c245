INSERT INTO "SYS_ROLE"("ROLE_ID", "NAME", "ROLE_LEVEL", "DESCRIPTION", "DATA_SCOPE", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('1', '超级管理员', '1', '-', '自定义', NULL, 'admin', TO_DATE('2018-11-23 11:04:37', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-08-06 16:10:24', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_ROLE"("ROLE_ID", "NAME", "ROLE_LEVEL", "DESCRIPTION", "DATA_SCOPE", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('2', '普通用户', '2', '-', '自定义', NULL, 'admin', TO_DATE('2018-11-23 13:09:06', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-09-05 10:45:12', 'SYYYY-MM-DD HH24:MI:SS'));

--SYS_DEPT
INSERT INTO "SYS_DEPT"("DEPT_ID", "PID", "SUB_COUNT", "NAME", "DEPT_SORT", "ENABLED", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('1', NULL, '0', '运维测试部', '4', 1, 'admin', 'admin', TO_DATE('2019-03-25 09:20:44', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-05-17 14:27:27', 'SYYYY-MM-DD HH24:MI:SS'));
INSERT INTO "SYS_DEPT"("DEPT_ID", "PID", "SUB_COUNT", "NAME", "DEPT_SORT", "ENABLED", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('2', NULL, '0', '浙江省', '0', 1, 'admin', 'admin', TO_DATE('2019-03-25 11:04:50', 'SYYYY-MM-DD HH24:MI:SS'), TO_DATE('2020-06-08 12:08:56', 'SYYYY-MM-DD HH24:MI:SS'));

--SYS_JOB
INSERT INTO "SYS_JOB"("JOB_ID", "NAME", "ENABLED", "JOB_SORT", "CREATE_BY", "UPDATE_BY", "CREATE_TIME", "UPDATE_TIME") VALUES ('1', '运维测试', 1, '3', NULL, NULL, TO_DATE('2019-03-29 14:52:28', 'SYYYY-MM-DD HH24:MI:SS'), NULL);

--SYS_ROLES_DEPTS
INSERT INTO "SYS_ROLES_DEPTS"("ROLE_ID", "DEPT_ID") VALUES ('2', '1');
INSERT INTO "SYS_ROLES_DEPTS"("ROLE_ID", "DEPT_ID") VALUES ('1', '1');
