package com.hzwangda.aigov.docconvert.aspose.service.impl;

import cn.hutool.core.io.IoUtil;
import com.aspose.words.Document;
import com.aspose.words.FontSettings;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.hzwangda.aigov.docconvert.aspose.config.AsposeProperties;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;
import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.hzwangda.aigov.docconvert.common.service.SysStorageConversionService;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service("formatConvertServiceImpl")
@ConditionalOnProperty(value = "docconvert.version", havingValue = "aspose-word", matchIfMissing = true)
@RequiredArgsConstructor
public class AsposeWordFormatConvertServiceImpl implements FormatConvertService {

    private final AsposeProperties asposeProperties;
    private final FileProperties fileProperties;
    private final StorageManageService storageManageService;
    private final IStorageService storageService;
    private final SysStorageConversionService storageConversionService;

    @PostConstruct
    private void loadLicense() throws Exception {
        InputStream is = null;

        String licenseFilePath = asposeProperties.getLicenseFilePath();
        if (StringUtils.isBlank(licenseFilePath)) {
            return;
        } else if (licenseFilePath.startsWith("classpath:")) {
            is = this.getClass().getResourceAsStream(licenseFilePath.substring("classpath:".length()));
        } else if (licenseFilePath.startsWith("file:")) {
            is = new FileInputStream(licenseFilePath.substring("file:".length()));
        } else {
            is = new FileInputStream(licenseFilePath);
        }

        License aposeLic = new License();
        aposeLic.setLicense(is);

        String fontsDirPath = asposeProperties.getFontsDirPath();
        if (Objects.nonNull(fontsDirPath) && fontsDirPath.length()>0) {
            FontSettings.getDefaultInstance().setFontsFolder(fontsDirPath, true);
        }
    }

    @Override
    public boolean convertToFormats(Long storageId, FileConversionEnum[] formats) {
        if (ArrayUtils.isEmpty(formats)) {
            // 无需格式转化，视为成功
            return true;
        }

        LocalStorageDto storageDto = storageManageService.findById(storageId);
        if (Objects.isNull(storageDto)) {
            return false;
        }

        InputStream is = storageService.getInputStream(storageDto.getPath());

        try {
            Document orgiDoc = new Document(is);
            orgiDoc.acceptAllRevisions(); // 去除批注

            // 清稿
            if (ArrayUtils.contains(formats, FileConversionEnum.DOC)) {
                saveAs(orgiDoc, storageDto, conversionMap.get("docx"));
            }

            // PDF格式
            if (ArrayUtils.contains(formats, FileConversionEnum.PDF)) {
                saveAs(orgiDoc, storageDto, conversionMap.get("pdf"));
            }

            // TXT格式
            if (ArrayUtils.contains(formats, FileConversionEnum.TXT)) {
                saveAs(orgiDoc, storageDto, conversionMap.get("txt"));
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(is);
        }

        return false;
    }

    @Override
    public boolean officeClean(Long storageId) {
        return convertToFormats(storageId, new FileConversionEnum[]{
                FileConversionEnum.DOC, FileConversionEnum.PDF, FileConversionEnum.TXT});
    }

    @Override
    public boolean convertToCleanDoc(Long storageId) {
        LocalStorageDto storageDto = storageManageService.findById(storageId);
        if (Objects.isNull(storageDto)) {
            return false;
        }

        InputStream is = storageService.getInputStream(storageDto.getPath());

        try {
            Document orgiDoc = new Document(is);
            orgiDoc.acceptAllRevisions(); // 去除批注

            // 清稿
            saveAs(orgiDoc, storageDto, conversionMap.get("docx"));

            return true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(is);
        }

        return false;
    }

    @Override
    public boolean convertToPdf(Long storageId, String username) {
        return false;
    }

    @Override
    public String wrapHeader(Long docStorageId, Long headerTemplateStorageId) {
        return null;
    }

    @Override
    public String mergeDoc(Long firstDocStorageId, Long secondDocStorageId) {
        return null;
    }

    @Override
    public String renderBookmarks(Long docStorageId, Map<String, String> bookmarkData) {
        return null;
    }

    @Override
    public boolean checkConvertTask(String taskId) {
        return false;
    }

    private void saveAs(Document orgiDoc, LocalStorageDto storageDto, ConversionType conversionType) throws Exception {
        String docBasePath = storageService.isLocalStorageMode() ? fileProperties.getPath().getPath() : FileUtil.getTmpDir().getPath();
        String docRelativeFilePath = FileUtil.newRelativeFilePath(storageDto.getName(),conversionType.getSuffix());
        String docAbsoluteFilePath = docBasePath + docRelativeFilePath;
        orgiDoc.save(docAbsoluteFilePath, conversionType.getSaveFormat());

        LocalStorage docStorage = storageService.create(storageDto.getName(), new File(docAbsoluteFilePath));

        // 若是非本地存储的（如OSS），删除临时文件。
        if (!storageService.isLocalStorageMode()) {
            FileUtil.del(docAbsoluteFilePath);
        }

        SysStorageConversion docConv = new SysStorageConversion();
        docConv.setOriginalStorageId(storageDto.getId());
        docConv.setConversionStorageId(docStorage.getId());
        docConv.setConversionType(conversionType.getConversionType());
        storageConversionService.save(docConv);
    }

    private static Map<String, ConversionType> conversionMap = new HashMap<String, ConversionType>(){
        {
            put("docx", new ConversionType("docx", FileConversionEnum.DOC.getName(), SaveFormat.DOCX));
            put("pdf", new ConversionType("pdf", FileConversionEnum.PDF.getName(), SaveFormat.PDF));
            put("txt", new ConversionType("txt", FileConversionEnum.TXT.getName(), SaveFormat.TEXT));
        }
    };

    @Data
    @AllArgsConstructor
    private static class ConversionType {
        private String suffix;
        private String conversionType;
        private int saveFormat;
    }

}
