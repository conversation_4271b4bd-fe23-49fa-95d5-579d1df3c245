package com.hzwangda.aigov.docconvert.client;

import com.hzwangda.aigov.docconvert.common.enums.FileType;
import com.hzwangda.aigov.docconvert.common.util.CaseInsensitiveMap;
import com.hzwangda.aigov.docconvert.common.util.HttpKit;
import com.hzwangda.aigov.docconvert.common.util.LoadProperties;
import com.hzwangda.aigov.docconvert.common.util.StrKit;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.CRC32;

public class DocConvertClient{

	private static String QUEUE_SERVLET_URL= LoadProperties.loadProperties().getProperty("DocConvert.QueueServletURL");

	public static String applyServletUrl() {
		String queueServletURL = QUEUE_SERVLET_URL + "?Service=GetServer&ServiceUser=&ServicePwd=";
		String server = HttpKit.getString(queueServletURL);
		if (StrKit.isNotEmpty(server)) {
			return "http://" + server + "/Services/DocConvert.jsp";
		} else {
			throw new RuntimeException("申请文档格式转化服务器地址失败！");
		}
	}

	/**
	 * 调用文件格式转化服务,成功返回OK,错误返回错误原因描述
	 * @param servletUrl
	 * @param srcFile
	 * @param destFileMapx
	 * @param format
	 * @return
	 */
	public static String convertFormat(String servletUrl, String srcFile, final Map<String, String> destFileMapx, int format) {
		//表单参数
		Map<String, String> map = new HashMap<String, String>();
		map.put("Format", String.valueOf(format));
		//上传文件
		Map<String, File> fileMap = new HashMap<String, File>();
		File file = new File(srcFile);
		fileMap.put("file", file);

		String sResult = HttpKit.upload(servletUrl, map, fileMap, new DocConvertHttpResponseCallback(destFileMapx));

		return sResult;
	}
	
	public static String convertFormat(String srcFile, final Map<String, String> destFileMapx, int format) {
		// 尝试获取已转化的相同文件
		if (quickConvertFormat(new File(srcFile), destFileMapx, format)) {
			return "OK";
		}
		String servletUrl = applyServletUrl();
		return convertFormat(servletUrl, srcFile, destFileMapx, format);
	}
	
	/**
	 * TODO 异步调用文件格式转化，应增加回调接口，用于异步处理返回值
	 * @param srcFile
	 * @param destFileMapx
	 * @param format
	 * @return
	 */
	public static String asyncConvertFormat(final String srcFile, final Map<String, String> destFileMapx, final int format) {
		final String servletUrl = applyServletUrl();
		if (StrKit.isNotEmpty(servletUrl)) {
			(new Thread() {
				@Override
				public void run() {
					convertFormat(servletUrl, srcFile, destFileMapx, format);
				}
			}).start();
			return "OK";
		} else {
			return "接口配置有误";
		}
	}

	/**
	 * 根据文件大小和CRC32校验码，快速检索是否已存在已转化或正在转化的相同文件，若相同则直接获取
	 * @param file
	 * @param destFileMapx
	 * @param format
	 * @return
	 */
	private static boolean quickConvertFormat(File file, final Map<String, String> destFileMapx, int format) {
		try {
			// 查询缓存存储位置
			String queryServletUrl = QUEUE_SERVLET_URL + "?Service=QueryCacheStoreServer&ServiceUser=&ServicePwd=";
			Map<String, String> params = new HashMap<String, String>();
			params.put("FileSize", String.valueOf(file.length()));
			params.put("CRC32", String.valueOf(getCRC32(file)));
			params.put("ConvertFormat", String.valueOf(format));
			
			while (true) {
				String sResult = HttpKit.getString(queryServletUrl, params);
				if (sResult != null) {
					String[] arrResult = StrKit.splitEx(sResult, "|");
					if (arrResult.length == 3) {
						if ("Success".equals(arrResult[0])) { // 已存在相同文件缓存
							String cacheStoreServer = arrResult[1]; // 缓存存储服务器
							String cachePickCode = arrResult[2]; // 缓存提取码
							String getDataServletUrl = "http://" + cacheStoreServer + "/Services/DocConvert.jsp?Service=GetCache&PickCode=" + cachePickCode;
							String getCacheResult = HttpKit.get(getDataServletUrl, null, new DocConvertHttpResponseCallback(destFileMapx));
							if ("OK".equals(getCacheResult)) {
								return true;
							}
						} else if ("Converting".equals(arrResult[0])) { // 已有相同文件正在转化中，则等待这文件转化结束并取其缓存
							Thread.sleep(1000);
							continue;
						}
					}
				}
				break;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	public static long getCRC32(File file) throws IOException {
		CRC32 crc32 = new CRC32();
		InputStream fis = new FileInputStream(file);
	    byte[] buffer = new byte[4096];
	    int numRead = 0;
	    while ((numRead = fis.read(buffer)) > 0) {
	    	crc32.update(buffer, 0, numRead);
	    }
	    fis.close();
		return crc32.getValue();
	}

	public static void main(String[] args) {
		String servletUrl = applyServletUrl(); // "http://localhost:8099/Services/DocConvert.jsp";
		String basePath = "~/file/";
		String srcFile = basePath + "1.doc";
		int format = FileType.DOC + FileType.PDF;
		Map<String, String> destFileMapx = new CaseInsensitiveMap<String>();
		destFileMapx.put("doc", basePath + "2.doc");
		destFileMapx.put("pdf", basePath + "2.pdf");
		long startTimeMillis = System.currentTimeMillis();
		String sResult = convertFormat(servletUrl, srcFile, destFileMapx, format);
		System.out.println("=========== " + sResult + " =========== 共花费：" + (System.currentTimeMillis() - startTimeMillis) / 1000.0 + "秒");
	}
}
