package com.hzwangda.aigov.docconvert.client;

import com.hzwangda.aigov.docconvert.common.util.HttpMultiFileCallback;
import lombok.extern.slf4j.Slf4j;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;

import java.io.*;
import java.util.Enumeration;
import java.util.Map;

@Slf4j
public class DocConvertHttpResponseCallback extends HttpMultiFileCallback {
	
	public DocConvertHttpResponseCallback(Map<String, String> destFileMapx) {
		super(destFileMapx);
	}

	public DocConvertHttpResponseCallback(Map<String, String> destFileMapx, Map<String, String> fieldMapx) {
		super(destFileMapx, fieldMapx);
	}
	
	@Override
	protected void dealSavedFile(String fieldName, String filePath) {
		if ("html".equals(fieldName)) {
			String zipPath = filePath.substring(0, filePath.lastIndexOf(".")) + ".zip";
			new File(filePath).renameTo(new File(zipPath));
			
			String htmlFileName = filePath.substring(filePath.lastIndexOf("/")+1);
			unzip4Html(zipPath, zipPath.substring(0, zipPath.lastIndexOf("/")), htmlFileName);
			
			new File(zipPath).delete();
		}
	}
	
	private boolean unzip4Html(String srcFileName, String destPath, String newHtmlFileName) {
		try {
			ZipFile zipFile = new ZipFile(srcFileName);
			Enumeration<?> e = zipFile.getEntries();
			ZipEntry zipEntry = null;
			(new File(destPath)).mkdirs();
			while (e.hasMoreElements()) {
				zipEntry = (ZipEntry) e.nextElement();
				log.info("正在解压 " + zipEntry.getName());
				if (zipEntry.isDirectory()) {
					(new File(destPath + File.separator + zipEntry.getName())).mkdirs();
				} else {
					String fileName = zipEntry.getName();
					if (fileName.endsWith(".html") && fileName.indexOf("/")<0) {
						fileName = newHtmlFileName;
					}
					File f = new File(destPath + File.separator + fileName);
					f.getParentFile().mkdirs();
					InputStream in = zipFile.getInputStream(zipEntry);
					OutputStream out = new BufferedOutputStream(new FileOutputStream(f));
					byte buf[] = new byte[1024];
					int c;
					while ((c = in.read(buf)) != -1)
						out.write(buf, 0, c);
					out.close();
					in.close();
				}
			}
			zipFile.close();
			log.info("解压完毕！");
		} catch (Exception ex) {
			System.out.println(ex.getMessage());
			return false;
		}
		return true;
	}

}
