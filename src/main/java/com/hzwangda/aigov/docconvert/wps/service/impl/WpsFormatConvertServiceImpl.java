package com.hzwangda.aigov.docconvert.wps.service.impl;

import cn.hutool.json.JSONObject;
import com.hzwangda.aigov.docconvert.common.domain.dto.ConvertInfoDto;
import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;
import com.hzwangda.aigov.docconvert.common.enums.FileConvertType;
import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.hzwangda.aigov.officeonline.wps.propertie.WpsProperties;
import com.hzwangda.aigov.officeonline.wps.util.WpsTokenUtil;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.utils.RedisUtils;
import com.wangda.oa.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.*;

@Service("formatConvertServiceImpl")
@ConditionalOnProperty(value = "docconvert.version", havingValue = "wps", matchIfMissing = false)
public class WpsFormatConvertServiceImpl implements FormatConvertService {

    @Autowired
    private WpsProperties wpsProperties;

    @Autowired
    private LocalStorageRepository localStorageRepository;

    @Autowired
    @Qualifier("wpsRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public boolean convertToPdf(Long storageId, String username) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/cps/v1/office/convert")
                .queryParam("app_token", WpsTokenUtil.getAppToken())
                .build()
                .toUri();

        LocalStorage localStorage = localStorageRepository.findById(storageId).orElse(new LocalStorage());

        String taskId = createTaskId(storageId, FileConvertType.CONVERTTOPDF, username);

        Map<String, String> req = new HashMap<>();
        req.put("task_id", taskId);
        req.put("doc_url", getDownloadFileUrl(localStorage.getId()));
        req.put("doc_filename", localStorage.getId() + "." + localStorage.getSuffix());
        req.put("target_file_format", "pdf");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(req, headers);
        String response = restTemplate.postForObject(url, requestEntity, String.class);
        JSONObject jsonObject = new JSONObject(response);

        return jsonObject.getInt("result") == 0;
    }

    @Override
    public String wrapHeader(Long fileId, Long headerTemplateFileId) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/cps/v2/office/wrapheader")
                .queryParam("app_token", WpsTokenUtil.getAppToken())
                .build()
                .toUri();

        LocalStorage fileStorage = localStorageRepository.findById(fileId).orElse(new LocalStorage());
        LocalStorage headerTemplateFileStorage = localStorageRepository.findById(headerTemplateFileId).orElse(new LocalStorage());

        String taskId = createTaskId(fileId, FileConvertType.OTHER, SecurityUtils.getCurrentUsername());

        List sampleList = new ArrayList<Map<String, Object>>();
        sampleList.add(new HashMap<String, Object>() {
            {
                put("bookmark", "正文");
                put("type", "DOCUMENT");
                put("sample_url", getDownloadFileUrl(fileStorage.getId()));
                put("sample_filename", fileStorage.getId() + "." + fileStorage.getSuffix());
            }
        });

        Map<String, Object> req = new HashMap<>();
        req.put("task_id", taskId);
        req.put("template_url", getDownloadFileUrl(headerTemplateFileStorage.getId()));
        req.put("template_filename", headerTemplateFileStorage.getId() + "." + headerTemplateFileStorage.getSuffix());
        req.put("scene_id", "gwgl");
        req.put("sample_list", sampleList);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(req, headers);
        String response = restTemplate.postForObject(url, requestEntity, String.class);
        JSONObject jsonObject = new JSONObject(response);

        return taskId;
    }

    @Override
    public String mergeDoc(Long firstFileId, Long secondFileId) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/cps/v2/office/merge")
                .queryParam("app_token", WpsTokenUtil.getAppToken())
                .build()
                .toUri();

        LocalStorage firstFileStorage = localStorageRepository.findById(firstFileId).orElse(new LocalStorage());
        LocalStorage secondFileStorage = localStorageRepository.findById(secondFileId).orElse(new LocalStorage());

        String taskId = createTaskId(firstFileId, FileConvertType.OTHER, SecurityUtils.getCurrentUsername());

        List mergedFileList = new ArrayList<Map<String, Object>>();
        mergedFileList.add(new HashMap<String, Object>() {
            {
                put("doc_url", getDownloadFileUrl(firstFileStorage.getId()));
                put("doc_filename", firstFileStorage.getId() + "." + firstFileStorage.getSuffix());
            }
        });
        mergedFileList.add(new HashMap<String, Object>() {
            {
                put("doc_url", getDownloadFileUrl(secondFileStorage.getId()));
                put("doc_filename", secondFileStorage.getId() + "." + secondFileStorage.getSuffix());
            }
        });

        Map<String, Object> req = new HashMap<>();
        req.put("task_id", taskId);
        req.put("scene_id", "gwgl");
        req.put("merged_file_list", mergedFileList);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(req, headers);
        String response = restTemplate.postForObject(url, requestEntity, String.class);
        System.out.println(response);

        return taskId;
    }

    @Override
    public String renderBookmarks(Long docStorageId, Map<String, String> bookmarkData) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/cps/v2/office/wrapheader")
                .queryParam("app_token", WpsTokenUtil.getAppToken())
                .build()
                .toUri();

        LocalStorage fileStorage = localStorageRepository.findById(docStorageId).orElse(new LocalStorage());

        //以文件id作为转化任务的id
        String taskId = createTaskId(docStorageId, FileConvertType.OTHER, SecurityUtils.getCurrentUsername());

        List sampleList = new ArrayList<Map<String, Object>>();
        bookmarkData.forEach((key, value) -> {
            sampleList.add(new HashMap<String, Object>() {
                {
                    put("bookmark", key);
                    put("type", "TEXT");
                    put("text", value);
                }
            });
        });

        Map<String, Object> req = new HashMap<>();
        req.put("task_id", taskId);
        req.put("template_url", getDownloadFileUrl(fileStorage.getId()));
        req.put("template_filename", fileStorage.getId() + "." + fileStorage.getSuffix());
        req.put("scene_id", "gwgl");
        req.put("sample_list", sampleList);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(req, headers);
        String response = restTemplate.postForObject(url, requestEntity, String.class);
        System.out.println(response);

        return taskId;
    }

    @Override
    public boolean convertToFormats(Long storageId, FileConversionEnum[] formats) {
        // TODO 未实现按格式进行指定转化功能，目前该方法会同步转化生成清稿和pdf文件
        return officeClean(storageId);
    }

    @Override
    public boolean convertToCleanDoc(Long storageId) {
        // TODO 未实现只转化清稿文件功能，目前该方法会同步转化pdf文件
        return officeClean(storageId);
    }

    @Override
    public boolean officeClean(Long storageId) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/cps/v2/office/operate")
                .queryParam("app_token", WpsTokenUtil.getAppToken())
                .build()
                .toUri();

        LocalStorage localStorage = localStorageRepository.findById(storageId).orElse(new LocalStorage());

        String taskId = createTaskId(storageId, FileConvertType.OFFICECLEAN, SecurityUtils.getCurrentUsername());


        List steps = new ArrayList<Map<String, Object>>();
        steps.add(new HashMap<String, Object>() {
            {
                put("operate", "OFFICE_CLEAN");
                put("args", new HashMap<String, Object>());
            }
        });

        Map<String, Object> req = new HashMap<>();
        req.put("task_id", taskId);
        req.put("doc_url", getDownloadFileUrl(localStorage.getId()));
        req.put("doc_filename", localStorage.getId() + "." + localStorage.getSuffix());
        req.put("scene_id", "gwgl");
        req.put("steps", steps);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(req, headers);
        String response = restTemplate.postForObject(url, requestEntity, String.class);
        JSONObject jsonObject = new JSONObject(response);

        return jsonObject.getInt("result") == 0;
    }

    public String getDownloadUrl(String taskId, String result) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/cps/v1/task/query")
                .queryParam("app_token", WpsTokenUtil.getAppToken())
                .build()
                .toUri();

        Map<String, String> req = new HashMap<>();
        req.put("task_id", taskId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(req, headers);
        String res = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class).getBody();
        JSONObject jsonObject = new JSONObject(res);

        return jsonObject.getStr("download_id");
    }

    @Override
    public boolean checkConvertTask(String taskId) {
        Object object = redisUtils.get(taskId);
        if (object == null) {
            return true;
        } else {
            return false;
        }
    }

    private String createTaskId(Long storageId, String convertType, String username) {
        String taskId = UUID.randomUUID().toString();
        ConvertInfoDto convertInfoDto = new ConvertInfoDto();
        convertInfoDto.setStorageId(storageId);
        convertInfoDto.setConvertType(convertType);
        convertInfoDto.setUsername(username);
        redisUtils.set(taskId, convertInfoDto);
        return taskId;
    }

    private String getDownloadFileUrl(Long fileId) {
        return wpsProperties.getDownloadHost() + "/api/localStorage/downloadFile/" + fileId;
    }

}
