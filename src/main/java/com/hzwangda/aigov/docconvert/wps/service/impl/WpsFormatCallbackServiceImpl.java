package com.hzwangda.aigov.docconvert.wps.service.impl;

import com.hzwangda.aigov.docconvert.common.domain.dto.ConvertInfoDto;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.enums.FileConvertType;
import com.hzwangda.aigov.docconvert.common.repository.SysStorageConversionRepository;
import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.hzwangda.aigov.docconvert.wps.service.WpsFormatCallbackService;
import com.hzwangda.aigov.officeonline.common.domain.entity.SysFileVersion;
import com.hzwangda.aigov.officeonline.common.repository.SysFileVersionRepository;
import com.hzwangda.aigov.officeonline.wps.propertie.WpsProperties;
import com.hzwangda.aigov.officeonline.wps.util.WpsTokenUtil;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.File;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

@Service
@RequiredArgsConstructor
public class WpsFormatCallbackServiceImpl implements WpsFormatCallbackService {

    private final WpsProperties wpsProperties;
    private final FileProperties properties;
    private final FormatConvertService wpsFormatConvertService;
    private final IStorageService storageService;
    private final StorageManageService storageManageService;
    private final SysStorageConversionRepository sysStorageConversionRepository;
    private final SysFileVersionRepository fileVersionRepository;

    @Autowired
    @Qualifier("wpsRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private RedisUtils redisUtils;

    private void downloadFile(String downloadId, String savePath){
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/cps/v1/download/file/{downloadId}")
                .queryParam("app_token", WpsTokenUtil.getAppToken())
                .build()
                .expand(downloadId)
                .toUri();

        //定义请求头的接收类型
        RequestCallback requestCallback = request -> {
            HttpHeaders headers = request.getHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
        };
        //对响应进行流式处理而不是将其全部加载到内存中
        restTemplate.execute(url, HttpMethod.GET, requestCallback, clientHttpResponse -> {
            Files.copy(clientHttpResponse.getBody(), Paths.get(savePath));
            return null;
        });
    }

    private Long saveFileInfoToDb(File docFile, LocalStorageDto mainLocalStorage, String conversionType){
        LocalStorage docStorage = storageService.create(mainLocalStorage.getName(), docFile);
        Long docFileId = docStorage.getId();

        SysStorageConversion docStorageConversion = new SysStorageConversion();
        docStorageConversion.setConversionStorageId(docFileId);
        docStorageConversion.setOriginalStorageId(mainLocalStorage.getId());
        docStorageConversion.setConversionType(conversionType);
        sysStorageConversionRepository.save(docStorageConversion);

        return docFileId;
    }

    private Long saveFileVersionToDb(File docFile, LocalStorageDto mainLocalStorage, String username){
        LocalStorage docStorage = storageService.create(mainLocalStorage.getName(), docFile);
        Long docFileId = docStorage.getId();

        SysFileVersion fileVersion = fileVersionRepository.findByStorageId(mainLocalStorage.getId());
        SysFileVersion latestFileVersion;
        if (fileVersion == null) {
            latestFileVersion = new SysFileVersion();
            latestFileVersion.setMainStorageId(mainLocalStorage.getId());
            latestFileVersion.setFileVersion(0);
        }else{
            latestFileVersion = fileVersionRepository.findFirstByMainStorageIdOrderByFileVersionDesc(fileVersion.getMainStorageId());
        }

        SysFileVersion newFileVersion = new SysFileVersion();
        newFileVersion.setStorageId(docFileId);
        newFileVersion.setMainStorageId(latestFileVersion.getMainStorageId());
        newFileVersion.setFileVersion(latestFileVersion.getFileVersion() + 1);
        newFileVersion.setSaveType("manual");
        newFileVersion.setSaveBy(username);
        newFileVersion.setSaveTime(new Date());
        fileVersionRepository.save(newFileVersion);

        return docFileId;
    }


    @Override
    public void download(String taskId, String downloadId) {
        ConvertInfoDto convertInfoDto = (ConvertInfoDto) redisUtils.get(taskId);

        LocalStorageDto localStorage = storageManageService.findById(convertInfoDto.getStorageId());
        // 保证源文件存在
        if (localStorage != null) {
            if (convertInfoDto.getConvertType().equals(FileConvertType.OFFICECLEAN)) {
                // 文件为花脸稿,下载清稿
                Date date = Calendar.getInstance().getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
                String fileName = localStorage.getName().split("\\.")[0] + "-" + format.format(date) + "." + localStorage.getSuffix();
                String type = FileUtil.getFileType(localStorage.getSuffix());
                String path = properties.getPath().getPath() + type + File.separator;
                String targetPath = path + fileName;

                downloadFile(downloadId, targetPath);

                File docFile = new File(targetPath);
                Long docFileId = saveFileInfoToDb(docFile, localStorage, "doc");

                // 清稿下载成功后创建转化pdf服务
                wpsFormatConvertService.convertToPdf(docFileId, convertInfoDto.getUsername());

            } else if(convertInfoDto.getConvertType().equals(FileConvertType.CONVERTTOPDF)){
                // 文件为清稿，下载pdf
                SysStorageConversion sysStorageConversion = sysStorageConversionRepository.findByConversionStorageId(localStorage.getId());
                LocalStorageDto docStorage = storageManageService.findById(sysStorageConversion.getOriginalStorageId());

                Date date = Calendar.getInstance().getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
                String fileName = docStorage.getName().split("\\.")[0] + "-" + format.format(date) + ".pdf";
                String type = FileUtil.getFileType("pdf");
                String path = properties.getPath().getPath() + type + File.separator;
                String targetPath = path + fileName;

                downloadFile(downloadId, targetPath);

                File pdfFile = new File(targetPath);
                saveFileInfoToDb(pdfFile, docStorage, "pdf");
            } else if(convertInfoDto.getConvertType().equals(FileConvertType.OTHER)){
                // 处理一键成文和套红的回调
                Date date = Calendar.getInstance().getTime();
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
                String fileName = localStorage.getName().split("\\.")[0] + "-" + format.format(date) + "." + localStorage.getSuffix();
                String type = FileUtil.getFileType(localStorage.getSuffix());
                String path = properties.getPath().getPath() + type + File.separator;
                String targetPath = path + fileName;

                downloadFile(downloadId, targetPath);

                File docFile = new File(targetPath);
                saveFileVersionToDb(docFile, localStorage, convertInfoDto.getUsername());
            }
            redisUtils.del(taskId);
        }
    }
}
