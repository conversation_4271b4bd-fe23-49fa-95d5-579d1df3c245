package com.hzwangda.aigov.docconvert.wps.controller;

import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.hzwangda.aigov.docconvert.wps.domain.request.RenderBookmarksRequest;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@Api(tags = "WPS在线编辑调用地址接口")
@RequestMapping("/api/wps/formatConvert")
public class FormatConvertController {

    private final FormatConvertService wpsFormatService;

    /**
     * 清稿
     * @param storageId
     * @return
     */
    @GetMapping("/officeClean")
    public ResponseEntity<Object> officeClean(Long storageId){
        boolean operateResult = wpsFormatService.officeClean(storageId);
        Map<String, Object> respMap = new HashMap<>();
        respMap.put("result", operateResult);
        return new ResponseEntity<>(respMap, HttpStatus.OK);
    }

    /**
     * 多书签套用，用于一键成文、套红头等业务场景
     * @param fileId
     * @param headerTemplateFileId
     * @return
     */
    @PostMapping("/wrapHeader")
    public ResponseEntity<Object> wrapHeader(Long fileId, Long headerTemplateFileId){
        String taskId = wpsFormatService.wrapHeader(fileId, headerTemplateFileId);
        Map<String, Object> respMap = new HashMap<>();
        respMap.put("taskId", taskId);
        return new ResponseEntity<>(respMap, HttpStatus.OK);
    }

    /**
     * 合并文档，用于文档没有红头书签时无法使用wrapHeader来套红时，通过合并文档来替代实现套红
     * @param firstFileId
     * @param secondFileId
     * @return
     */
    @PostMapping("/mergeDoc")
    public ResponseEntity<Object> mergeDoc(Long firstFileId, Long secondFileId) {
        String taskId = wpsFormatService.mergeDoc(firstFileId, secondFileId);
        Map<String, Object> respMap = new HashMap<>();
        respMap.put("taskId", taskId);
        return new ResponseEntity<>(respMap, HttpStatus.OK);
    }

    /**
     * 替换书签，用于一键成文
     * @param data
     * @return
     */
    @PostMapping("renderBookmarks")
    public ResponseEntity<Object> renderBookmarks(@RequestBody RenderBookmarksRequest data){
        String taskId = wpsFormatService.renderBookmarks(data.getDocStorageId(), data.getBookmarkData());
        Map<String, Object> respMap = new HashMap<>();
        respMap.put("taskId", taskId);
        return new ResponseEntity<>(respMap, HttpStatus.OK);
    }

    /**
     * 检查转化文件是否下载
     * @param taskId
     * @return
     */
    @GetMapping ("/checkConvertTask")
    public ResponseEntity<Object> checkConvertTask(String taskId){
        boolean isDownload = wpsFormatService.checkConvertTask(taskId);
        Map<String, Object> respMap = new HashMap<>();
        respMap.put("result", isDownload);
        return new ResponseEntity<>(respMap, HttpStatus.OK);
    }



}
