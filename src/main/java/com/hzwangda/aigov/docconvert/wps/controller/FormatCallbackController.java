package com.hzwangda.aigov.docconvert.wps.controller;

import cn.hutool.json.JSONObject;
import com.hzwangda.aigov.docconvert.wps.service.WpsFormatCallbackService;
import com.wangda.oa.annotation.AnonymousAccess;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @description: 格式处理回掉接口
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "WPS文件转化相关回调接口")
@RequestMapping("/api/wps/local/cps")
public class FormatCallbackController {

    private final WpsFormatCallbackService wpsFormatCallbackService;

    @PostMapping("/callback")
    @AnonymousAccess
    public ResponseEntity<Object> callBack(@RequestBody Map<String,String> map){
        String taskId = map.get("task_id");
        String result = map.get("result");
        JSONObject jsonObject = new JSONObject(result);
        String downloadId = jsonObject.getStr("download_id");
        wpsFormatCallbackService.download(taskId, downloadId);
        return new ResponseEntity<>("", HttpStatus.OK);
    }

}
