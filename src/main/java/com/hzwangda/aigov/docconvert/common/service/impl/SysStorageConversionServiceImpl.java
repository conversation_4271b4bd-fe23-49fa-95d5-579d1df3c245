package com.hzwangda.aigov.docconvert.common.service.impl;

import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.repository.SysStorageConversionRepository;
import com.hzwangda.aigov.docconvert.common.service.SysStorageConversionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SysStorageConversionServiceImpl implements SysStorageConversionService {

    private final SysStorageConversionRepository sysStorageConversionRepository;

    /* 根据原附件查询转化数据
     * @param originalStorageId
     * @return
     */
    public List<SysStorageConversion> findByOriginalStorageId(Long originalStorageId){
        return sysStorageConversionRepository.findByOriginalStorageId(originalStorageId);
    }

    @Override
    public boolean addSysStorageConversion(String storageId) {
        return false;
    }

    @Override
    public SysStorageConversion save(SysStorageConversion storageConversion) {
        return sysStorageConversionRepository.save(storageConversion);
    }

    @Override
    public Long findOriginalStorageIdByConversionStorageId(Long conversionStorageId) {
        SysStorageConversion conv = sysStorageConversionRepository.findByConversionStorageId(conversionStorageId);
        if (conv!=null) {
            return conv.getOriginalStorageId();
        } else {
            return null;
        }
    }

}
