package com.hzwangda.aigov.docconvert.common.domain.mapstruct;

import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionDto;
import com.hzwangda.aigov.docconvert.common.domain.mapstruct.util.StorageConversionUtil;
import com.wangda.oa.base.BaseMapper;
import com.wangda.oa.domain.LocalStorage;
import org.mapstruct.*;

@Mapper(componentModel = "spring", uses = {StorageConversionUtil.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StorageConversionMapper extends BaseMapper<StorageConversionDto, LocalStorage> {

    @AfterMapping
    default void generateUrl(LocalStorage entity, @MappingTarget StorageConversionDto dto) {
        StringBuilder url = new StringBuilder();
        url.append("/api/localStorage/previewFile/" + entity.getId());
        dto.setUrl(url.toString());
    }

    @Override
    @Mappings({
            @Mapping(target = "conversionStorage", source = "id"),
            @Mapping(target = "storageId", source = "id")
    })
    StorageConversionDto toDto(LocalStorage entity);

}
