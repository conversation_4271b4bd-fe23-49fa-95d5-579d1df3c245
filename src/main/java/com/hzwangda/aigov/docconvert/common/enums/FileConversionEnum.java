package com.hzwangda.aigov.docconvert.common.enums;

/**
 * <AUTHOR>
 * @date 2021/8/2 上午11:08
 */
public enum FileConversionEnum {
    DOC("doc", ".doc", "清稿"),
    TXT("txt", ".txt", "TXT文本"),
    PDF("pdf", ".pdf", "PDF格式"),
    OFD("ofd", ".ofd", "OFD格式"),
    SIGN("sign", null, "签章文件"),
    ORGI("orgi", null, "花脸稿");

    private String name;
    private String suffix;
    private String value;

    public String getName() {
        return name;
    }

    public String getSuffix() {
        return suffix;
    }

    public String getValue() {
        return value;
    }

    FileConversionEnum(String name, String suffix, String value) {
        this.name = name;
        this.suffix = suffix;
        this.value = value;
    }

}
