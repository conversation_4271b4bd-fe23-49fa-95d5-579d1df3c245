package com.hzwangda.aigov.docconvert.common.service;

import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;

import java.util.Map;

public interface FormatConvertService {

    /**
     * 格式转化成指定格式
     * @return
     */
    boolean convertToFormats(Long storageId, FileConversionEnum[] formats);

    /**
     * @deprecated
     * 原清稿是会同时转doc和pdf，现在采用convertToFormats替代
     * @return
     */
    boolean officeClean(Long storageId);

    /**
     * 清稿
     * @return
     */
    public boolean convertToCleanDoc(Long storageId);

    /**
     * 转化pdf
     * @return
     */
    boolean convertToPdf(Long storageId, String username);

    /**
     * 正文套红头
     * @param docStorageId 正文文件id，正文文件必须有书签"红头"
     * @param headerTemplateStorageId 红头模板文件id
     * @return
     */
    String wrapHeader(Long docStorageId, Long headerTemplateStorageId);

    /**
     * 文档合并，会以第一个文档的页边距等设置为准。可用于正文没有书签情况下的套红
     * @param firstDocStorageId
     * @param secondDocStorageId
     * @return
     */
    String mergeDoc(Long firstDocStorageId, Long secondDocStorageId);

    /**
     * 替换书签，用于一键成文等业务场景
     * @param docStorageId
     * @param bookmarkData
     * @return
     */
    String renderBookmarks(Long docStorageId, Map<String, String> bookmarkData);

    /**
     * 查询转化文件是否下载成功
     * @param taskId
     * @return
     */
    boolean checkConvertTask(String taskId);

}
