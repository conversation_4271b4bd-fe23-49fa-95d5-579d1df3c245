package com.hzwangda.aigov.docconvert.common.domain.entity;

import com.wangda.boot.platform.base.BaseDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date  2021/7/23
 * 附件转换关系表
**/

@Entity
@Table(name = "sys_storage_conversion")
@Data
public class SysStorageConversion extends BaseDomain {

    @Column(name = "original_storage_id")
    @ApiModelProperty(value = "原附件id")
    private Long originalStorageId;

    @Column(name = "conversion_storage_id")
    @ApiModelProperty(value = "转换附件id")
    private Long conversionStorageId;

    @Column(name = "conversion_type")
    @ApiModelProperty(value = "转换格式类型")
    private String conversionType;

}
