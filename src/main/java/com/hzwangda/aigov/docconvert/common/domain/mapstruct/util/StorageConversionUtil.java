package com.hzwangda.aigov.docconvert.common.domain.mapstruct.util;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionInfoDto;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;
import com.hzwangda.aigov.docconvert.common.service.SysStorageConversionService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class StorageConversionUtil {

    private final SysStorageConversionService storageConversionService;
    private final StorageManageService storageService;

    public Map<String, StorageConversionInfoDto> findConvertedFile(Long storageId) {
        Map<String, StorageConversionInfoDto> conversionStorage = Maps.newHashMap();

        List<SysStorageConversion> list = storageConversionService.findByOriginalStorageId(storageId);
        for(SysStorageConversion conversion : list) {
            LocalStorageDto localStorageDto=storageService.findById(conversion.getConversionStorageId());
            StorageConversionInfoDto storageConversionInfoDto=new StorageConversionInfoDto();
            if(localStorageDto.getId()!=null){
                // LocalStorage表存在，就视为存在文件，不对物理文件进行检查
                BeanUtils.copyProperties(localStorageDto,storageConversionInfoDto);
                storageConversionInfoDto.setConversionStatus(1);
            }  else if(DateUtil.compare(new Date(), DateUtil.offsetMinute(conversion.getCreateDate(),5))>=0){
                // 超过5分钟
                storageConversionInfoDto.setConversionStatus(-1);
            }  else {
                // 进行中
                storageConversionInfoDto.setConversionStatus(0);
            }
            conversionStorage.put(conversion.getConversionType(),storageConversionInfoDto);
        }
        //源文件(花脸稿)放入map
        StorageConversionInfoDto storageConversionInfoDto=new StorageConversionInfoDto();
        LocalStorageDto localStorageDto=storageService.findById(storageId);
        BeanUtils.copyProperties(localStorageDto,storageConversionInfoDto);
        storageConversionInfoDto.setConversionStatus(1);
        conversionStorage.put(FileConversionEnum.ORGI.getName(),storageConversionInfoDto);
        return conversionStorage;
    }

}
