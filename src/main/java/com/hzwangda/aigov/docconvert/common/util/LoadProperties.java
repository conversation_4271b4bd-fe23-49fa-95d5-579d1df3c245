package com.hzwangda.aigov.docconvert.common.util;

import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ClassPathResource;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Data
 * @description
 */
public class LoadProperties {

    @Bean
    public static Properties loadProperties() {
        YamlPropertiesFactoryBean yaml = new YamlPropertiesFactoryBean();
        //yaml.setResources(new FileSystemResource("classpath:config/user.yml"));//File路径引入
        yaml.setResources(new ClassPathResource("config/application.yml"));//class路径引入
        return yaml.getObject();
    }

}
