package com.hzwangda.aigov.docconvert.common.repository;

import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/23 上午11:42
 **/
public interface SysStorageConversionRepository extends JpaRepository<SysStorageConversion, Long>, JpaSpecificationExecutor<SysStorageConversion> {

    /**
     * 根据原附件查询转化数据
     * @param originalStorageId
     * @return
     */
    List<SysStorageConversion> findByOriginalStorageId(Long originalStorageId);

    /**
     * 根据原附件删除转换数据
     * @param originalStorageId
     */
    @Transactional(rollbackFor = Exception.class)
    void deleteByOriginalStorageId(Long originalStorageId);

    SysStorageConversion findByOriginalStorageIdAndConversionType(Long id, String doc);

    /**
     * 根据原文件id查次文件是不是被转化文件
     * @param id
     * @return
     */
    SysStorageConversion findByConversionStorageId(Long id);
}
