package com.hzwangda.aigov.docconvert.common.controller;

import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionDto;
import com.hzwangda.aigov.docconvert.common.service.FileConversionService;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import com.wangda.oa.exception.BadRequestException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Api(tags = "附件转换控制层")
@RequestMapping("/api/docconvert")
@CrossOrigin
@Slf4j
public class FileConversionController {

    private final FileConversionService conversionService;

    /**
     * 查询文件的转换文件是否已转换完成
     * @param storageId 文件id
     * @return
     */
    @PostMapping(value = "/fileConversionInfo")
    @ApiOperation("查询文件的转换文件是否已转换完成")
    public ResponseEntity<Object> fileConversionInfo(@RequestParam("storageId") Long storageId) {
        if(storageId == null || storageId.longValue() == 0) {
            throw new BadRequestException("文件id为空");
        }

        StorageConversionDto storageConversionDto = conversionService.queryFileConversionInfo(storageId);
        return new ResponseEntity<>(storageConversionDto, HttpStatus.OK);
    }

    /**
     * 游离文件定时删除
     * @return
     */
    @ApiOperation(value = "游离文件定时删除", notes = "游离文件定时删除")
    @AnonymousPostMapping(value = "/freeFileDelete")
    public ResponseEntity<Object> freeFileDelete() {
        return null;
    }
}
