package com.hzwangda.aigov.docconvert.common.controller;

import cn.hutool.core.util.ArrayUtil;
import com.hzwangda.aigov.docconvert.common.domain.dto.ConvertFormatDto;
import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;
import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@Api(tags = "附件转换控制层")
@RequestMapping("/api/docconvert")
@CrossOrigin
@Slf4j
public class FileConvertController {

    private final FormatConvertService formatService;

    @AnonymousPostMapping(value = "/convertFormat")
    @Log("文件转化")
    @ApiOperation("文件转化")
    public ResponseEntity<Object> convertFormat(@RequestBody ConvertFormatDto convertFormatDto) {
        if(ArrayUtil.isEmpty(convertFormatDto.getConvertTypes())) {
            convertFormatDto.setConvertTypes(new FileConversionEnum[]{FileConversionEnum.DOC, FileConversionEnum.PDF, FileConversionEnum.TXT});
        }
        boolean operateResult = formatService.convertToFormats(convertFormatDto.getStorageId(), convertFormatDto.getConvertTypes());
        Map<String, Object> respMap = new HashMap<>();
        respMap.put("result", operateResult);
        return new ResponseEntity<>(respMap, HttpStatus.OK);
    }
}
