package com.hzwangda.aigov.docconvert.common.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StorageConversionInfoDto{

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "真实文件名")
    private String realName;

    @ApiModelProperty(value = "文件名")
    private String name;

    @ApiModelProperty(value = "后缀")
    private String suffix;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "大小")
    private String size;

    @ApiModelProperty(value = "URL")
    private String url;

    @ApiModelProperty(value = "文件状态(1:成功/-1:失败/0:转化中)")
    private Integer conversionStatus;
}
