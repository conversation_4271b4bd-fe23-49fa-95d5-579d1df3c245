package com.hzwangda.aigov.docconvert.common.service.impl;

import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionDto;
import com.hzwangda.aigov.docconvert.common.domain.mapstruct.StorageConversionMapper;
import com.hzwangda.aigov.docconvert.common.service.FileConversionService;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.repository.LocalStorageRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FileConversionImpl implements FileConversionService {

    private final LocalStorageRepository localStorageRepository;
    private final StorageConversionMapper storageConversionMapper;

    public StorageConversionDto queryFileConversionInfo(Long storageId) {
        LocalStorage localStorage=localStorageRepository.findById(Long.valueOf(storageId)).orElseGet(LocalStorage::new);
        StorageConversionDto storageConversionDto = storageConversionMapper.toDto(localStorage);
        return storageConversionDto;
    }
}
