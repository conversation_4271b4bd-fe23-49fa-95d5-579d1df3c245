package com.hzwangda.aigov.docconvert.common.service;

import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/23 上午11:58
 **/
public interface SysStorageConversionService {

    /**
     * 根据原附件查询转化数据
     * @param originalStorageId
     * @return
     */
    List<SysStorageConversion> findByOriginalStorageId(Long originalStorageId);

    /**
     * 根据原附件id新增转换文件
     * @param storageId 原附件id
     * @return
     */
    @Deprecated
    boolean addSysStorageConversion(String storageId);

    /**
     * 保存转化对象
     * @param storageConversion
     * @return
     */
    SysStorageConversion save(SysStorageConversion storageConversion);

    Long findOriginalStorageIdByConversionStorageId(Long conversionStorageId);
}
