package com.hzwangda.aigov.seal.controller;

import cn.hutool.json.JSONUtil;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;
import com.hzwangda.aigov.docconvert.common.service.SysStorageConversionService;
import com.hzwangda.aigov.seal.domain.dto.SignFileUploadExtraParamDto;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

@RestController
@RequiredArgsConstructor
@Api(tags = "签章服务")
@RequestMapping("/api/seal")
@CrossOrigin
public class SealController {

    private final IStorageService storageService;
    private final StorageManageService storageManageService;
    private final SysStorageConversionService storageConversionService;

    @AnonymousPostMapping({"/uploadSignFile"})
    @Log("上传签章文件")
    @ApiOperation("上传签章文件")
    public ResponseEntity<Object> uploadSignFile(String extraParam, MultipartFile file) {
        SignFileUploadExtraParamDto uploadParam = JSONUtil.toBean(extraParam, SignFileUploadExtraParamDto.class, true);
        if (Objects.isNull(uploadParam)) {
            return new ResponseEntity("参数extraParam格式不正确", HttpStatus.BAD_REQUEST);
        }

        Long origStorageId = uploadParam.getOrigStorageId();
        Long origialStorageId = storageConversionService.findOriginalStorageIdByConversionStorageId(origStorageId);
        if (origialStorageId == null) {
            origialStorageId = origStorageId;
            // return new ResponseEntity("原始文件不存在", HttpStatus.BAD_REQUEST);
        }

        LocalStorageDto origStorage = storageManageService.findById(origialStorageId);
        LocalStorage signStorage = storageService.create(origStorage.getName(), file);
        SysStorageConversion origConv = new SysStorageConversion();
        origConv.setOriginalStorageId(origStorage.getId());
        origConv.setConversionStorageId(signStorage.getId());
        origConv.setConversionType(FileConversionEnum.SIGN.getName());
        this.storageConversionService.save(origConv);
        return new ResponseEntity(origConv, HttpStatus.OK);
    }

}
