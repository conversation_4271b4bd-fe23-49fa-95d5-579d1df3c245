package com.hzwangda.aigov.officeonline.wps.service.impl;

import com.google.common.collect.Maps;
import com.hzwangda.aigov.officeonline.common.domain.entity.SysFileVersion;
import com.hzwangda.aigov.officeonline.common.repository.SysFileVersionRepository;
import com.hzwangda.aigov.officeonline.common.service.FileVersionService;
import com.hzwangda.aigov.officeonline.common.service.OfficeTemplateService;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileHistoryResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.WebOfficeResponse;
import com.hzwangda.aigov.officeonline.wps.propertie.WpsProperties;
import com.hzwangda.aigov.officeonline.wps.service.WebOfficeService;
import com.hzwangda.aigov.officeonline.wps.service.WpsFileService;
import com.hzwangda.aigov.officeonline.wps.util.SecurityTokenUtil;
import com.hzwangda.aigov.officeonline.wps.util.WpsFileUtil;
import com.hzwangda.aigov.officeonline.wps.util.WpsSignatureUtil;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.repository.LocalStorageRepository;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class WebOfficeServiceImpl implements WebOfficeService {

    private final WpsProperties wpsProperties;
    private final StorageManageService storageManageService;

    private final WpsFileService wpsFileService;
    private final OfficeTemplateService officeTemplateService;
    private final FileVersionService fileVersionService;
    private final SysFileVersionRepository fileVersionRepository;
    private final LocalStorageRepository localStorageRepository;

    @Override
    public WebOfficeResponse buildWpsUrlByFileId(Long fileId, String permission) {
        SysFileVersion orgiFileVersion = fileVersionRepository.findByStorageId(fileId);
        if (orgiFileVersion != null) {
            fileId = orgiFileVersion.getMainStorageId();
        }

        LocalStorageDto localStorageDto = storageManageService.findById(fileId);

        if (localStorageDto != null) {
            Map<String, String> values = Maps.newHashMap();
            values.put("_w_appid", wpsProperties.getAppId());
            values.put("_w_permission", permission);

            String keyValueStr = WpsSignatureUtil.getKeyValueStr(values);
            String signature = WpsSignatureUtil.getSignature(values, wpsProperties.getAppSecret());
            String fileTypeCode = WpsFileUtil.getFileTypeCode(localStorageDto.getSuffix());

            String url = wpsProperties.getDomain() + fileTypeCode + "/" + fileId + "?" + keyValueStr + "_w_signature=" + signature;

            String token = SecurityTokenUtil.getToken();

            return new WebOfficeResponse(url, token, fileId.toString());
        }
        return null;
    }

    @Override
    public WebOfficeResponse buildWpsUrlByNew(String fileType) {
        Map<String, String> values = Maps.newHashMap();
        values.put("_w_appid", wpsProperties.getAppId());

        String keyValueStr = WpsSignatureUtil.getKeyValueStr(values);
        String signature = WpsSignatureUtil.getSignature(values, wpsProperties.getAppSecret());
        String fileTypeCode = WpsFileUtil.getFileTypeCode(fileType);

        String url = wpsProperties.getDomain() + fileTypeCode + "/new/0" + "?" + keyValueStr + "_w_signature=" + signature;

        String token = SecurityTokenUtil.getToken();

        return new WebOfficeResponse(url, token, "0");
    }

    @Override
    public WebOfficeResponse newFileByFileType(String fileType) {
        return null;
    }

    @Override
    public WebOfficeResponse newFileByTemplateId(String templateId) {
        return null;
    }

    @Override
    public FileHistoryResponse queryFileHistories(Long versionFileId) {
        SysFileVersion versionFile = fileVersionRepository.findById(versionFileId).orElse(null);
        Long mainFileId = versionFile!=null? versionFile.getMainStorageId() : versionFileId;
        return wpsFileService.queryFileHistories(mainFileId, Pageable.unpaged());
    }

    @Override
    public Long getNewFileId(){
        String username = SecurityUtils.getCurrentUsername();

        // 初始模板
        // TODO： 需修改成对应模版文件
        Long storageId = officeTemplateService.getDefaultTemplateStorageId();
        LocalStorage localStorage = localStorageRepository.findById(storageId).orElse(new LocalStorage());
        if(localStorage.getId() != null) {
            LocalStorage newLocalStorage = new LocalStorage();
            newLocalStorage.setName(localStorage.getName());
            newLocalStorage.setPath(localStorage.getPath());
            newLocalStorage.setRealName(localStorage.getRealName());
            newLocalStorage.setSize(localStorage.getSize());
            newLocalStorage.setSuffix(localStorage.getSuffix());
            newLocalStorage.setType(localStorage.getType());
            newLocalStorage.setVersion(localStorage.getVersion());
            LocalStorage save = localStorageRepository.save(newLocalStorage);
            Long newFileId = save.getId();

            fileVersionService.newZeroVersion(newFileId, username);

            return newFileId;
        }
        return null;
    }

}
