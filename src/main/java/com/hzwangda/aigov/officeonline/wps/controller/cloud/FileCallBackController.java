package com.hzwangda.aigov.officeonline.wps.controller.cloud;

import com.hzwangda.aigov.officeonline.wps.domain.request.FileHistoryRequest;
import com.hzwangda.aigov.officeonline.wps.domain.request.RenameRequest;
import com.hzwangda.aigov.officeonline.wps.domain.request.UserInfoRequest;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileHistoryResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileInfoResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileSaveResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileVersionResponse;
import com.hzwangda.aigov.officeonline.wps.service.WpsFileService;
import com.wangda.oa.annotation.AnonymousAccess;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021-09-29
 * WPS在线编辑文件相关回调接口
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "WPS在线编辑文件相关回调接口")
@RequestMapping("/api/wps/cloud/v1/3rd/file")
public class FileCallBackController {

    private final WpsFileService wpsFileService;

    /**
     * 获取文件元数据
     * 文件编辑或预览需访问：https://wwo.wps.cn/office/<:type>/<:fileid>?_w_appid=xxx&_w_signature=xxx&…(对接模块需要的自定义参数)
     * 访问后，WPS服务器将回调该接口
     */
    @GetMapping("/info")
    @AnonymousAccess
    public ResponseEntity<Object> getFileInfo(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @RequestParam(name = "_w_permission", defaultValue = "read", required = false) String permission // 用户操作权限，write：可编辑，read：预览
    ) {
        FileInfoResponse fileInfoResponse = wpsFileService.getFileInfo(fileId, permission, token);
        return new ResponseEntity<>(fileInfoResponse, HttpStatus.OK);
    }

    /**
     * 上传文件新版本
     * 当文档在线编辑并保存之后，调用该方法，同时版本号进行相应的变更。
     */
    @PostMapping("/save")
    @AnonymousAccess
    public ResponseEntity<Object> save(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestHeader("x-weboffice-save-type") String saveType, // 自动保存(auto)/手动保存(manual)
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @RequestParam("file") MultipartFile file
    ) {
        FileSaveResponse fileSaveResponse = wpsFileService.saveFile(fileId, token, saveType, file);
        return new ResponseEntity<>(fileSaveResponse, HttpStatus.OK);
    }

    @PostMapping("/new")
    @AnonymousAccess
    public ResponseEntity<Object> newFile(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @RequestBody MultipartFile file
    ) {
        return new ResponseEntity<>("", HttpStatus.OK);
    }

    /**
     * 通知此文件目前有哪些人正在协作
     * 此接口可以根据需要实现，若不实现直接返回 http 响应码 200
     * 目前直接返回200
     */
    @PostMapping("/online")
    @AnonymousAccess
    public ResponseEntity<Object> online(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @RequestBody UserInfoRequest userInfoRequest
    ) {
        return new ResponseEntity<>("", HttpStatus.OK);
    }

    /**
     * 文件重命名
     * 暂时用不上，接口未实现
     */
    @PutMapping("/rename")
    @AnonymousAccess
    public ResponseEntity<Object> rename(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @RequestBody RenameRequest renameRequest
    ) {
        wpsFileService.rename(fileId, renameRequest.getName());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 获取所有历史版本文件信息
     * 获取当前文档所有历史版本的文件信息，以数组的形式，按版本号从大到小的顺序返回响应。（会影响历史版本相关的功能）
     */
    @PostMapping("/history")
    @AnonymousAccess
    public ResponseEntity<Object> history(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @RequestBody FileHistoryRequest fileHistoryRequest
    ) {
        FileHistoryResponse response = wpsFileService.queryFileHistories(Long.valueOf(fileHistoryRequest.getId()), PageRequest.of(fileHistoryRequest.getOffset() / fileHistoryRequest.getCount(), fileHistoryRequest.getCount()));
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * 获取特定版本的文件信息
     */
    @GetMapping("/version/{version}")
    @AnonymousAccess
    public ResponseEntity<Object> fileVersion(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @PathVariable Integer version
    ) {
        FileVersionResponse res = wpsFileService.getFileInfoByVersion(fileId, version);
        return new ResponseEntity<>(res, HttpStatus.OK);
    }

}
