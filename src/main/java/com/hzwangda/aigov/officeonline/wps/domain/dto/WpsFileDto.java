package com.hzwangda.aigov.officeonline.wps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class WpsFileDto {

    private String id; //文件id,字符串长度小于40

    private String name; //文件名(必须带文件后缀)

    private int version; //当前版本号，必须大于 0，同时位数小于 11

    private int size; //文件大小，单位为B(文件真实大小，否则会出现异常)

    private String creator;

    @JSONField(name = "create_time", format = "unixtime")
    private Date createTime; //创建时间，时间戳，单位为秒

    private String modifier;

    @JSONField(name = "modify_time", format = "unixtime")
    private Date modifyTime; //修改时间，时间戳，单位为秒

    @JSONField(name = "download_url")
    private String downloadUrl; //文档下载地址

    @JSONField(name = "preview_pages")
    private int previewPages = 0;

    @JSONField(name = "user_acl")
    private UserAcl userAcl = new UserAcl();

    private Watermark watermark = new Watermark();

    @Getter
    @Setter
    public class UserAcl {
        private int rename = 1; //重命名权限，1为打开该权限，0为关闭该权限，默认为0
        private int history = 1; //历史版本权限，1为打开该权限，0为关闭该权限,默认为1
        private int copy = 1; //复制
        private int export = 0; //导出PDF
        private int print = 1; //打印
    }

    @Getter
    @Setter
    public class Watermark {
        private int type = 0; //水印类型， 0为无水印； 1为文字水印
        private String value; //文字水印的文字，当type为1时此字段必选
        private String fillstyle; //水印的透明度，非必选，有默认值
        private String font; //水印的字体，非必选，有默认值
        private String rotate; //水印的旋转度，非必选，有默认值
        private String horizontal; //水印水平间距，非必选，有默认值
        private String vertical; //水印垂直间距，非必选，有默认值
    }

}
