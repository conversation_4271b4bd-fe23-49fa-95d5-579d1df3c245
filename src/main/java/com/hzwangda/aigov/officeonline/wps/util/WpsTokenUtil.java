package com.hzwangda.aigov.officeonline.wps.util;

import com.hzwangda.aigov.officeonline.wps.domain.dto.WpsTokenDto;
import com.hzwangda.aigov.officeonline.wps.service.WpsLocalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class WpsTokenUtil {

    @Autowired
    private WpsLocalService wpsLocalService;

    private static WpsTokenDto token = null;

    private static WpsTokenUtil my;

    @PostConstruct
    public void init() {
        my = this;
        my.wpsLocalService = this.wpsLocalService;
    }

    public static String getAppToken() {
        if (token == null || token.getExpiresIn() - System.currentTimeMillis() < 1000 * 60 * 120) { // token离失效不足2小时，则进行刷新
            token = my.wpsLocalService.getToken();
        }
        return token.getAppToken();
    }

}
