package com.hzwangda.aigov.officeonline.wps.controller.local;

import com.hzwangda.aigov.officeonline.wps.domain.dto.WpsUserDto;
import com.hzwangda.aigov.officeonline.wps.domain.request.UserInfoRequest;
import com.hzwangda.aigov.officeonline.wps.domain.response.UserInfoResponse;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021-09-29
 * WPS在线编辑用户相关回调接口
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/wps/local/v1/3rd/user")
public class UserCallBackLocalController {

    private final UserService userService;

    /**
     * 获取用户信息
     */
    @PostMapping("/info")
    @AnonymousAccess
    public ResponseEntity<Object> info(
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_third_appid") String appId,
            @RequestBody UserInfoRequest userInfoRequest
    ) {
        UserInfoResponse userInfoResponse =  new UserInfoResponse();
        String[] usernames = userInfoRequest.getIds();
        for (String username : usernames) {
            UserDto user = userService.findByName(username);

            WpsUserDto wpsUser = new WpsUserDto();
            if (user!=null) {
                wpsUser.setId(user.getUsername());
                wpsUser.setName(user.getNickName());
            }  else {
                wpsUser.setId(username);
                wpsUser.setName(username);
            }

            userInfoResponse.getUsers().add(wpsUser);
        }

        return ResponseEntity.ok(userInfoResponse);
    }

}
