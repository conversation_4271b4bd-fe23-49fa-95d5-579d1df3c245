package com.hzwangda.aigov.officeonline.wps.service;

import com.hzwangda.aigov.officeonline.wps.domain.response.FileHistoryResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.WebOfficeResponse;

public interface WebOfficeService {

    WebOfficeResponse buildWpsUrlByFileId(Long fileId, String permission);

    WebOfficeResponse buildWpsUrlByNew(String fileType);

    WebOfficeResponse newFileByFileType(String fileType);

    WebOfficeResponse newFileByTemplateId(String templateId);

    FileHistoryResponse queryFileHistories(Long mainFileId);

    Long getNewFileId();
}
