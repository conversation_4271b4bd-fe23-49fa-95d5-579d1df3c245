package com.hzwangda.aigov.officeonline.wps.service.impl;

import cn.hutool.core.io.file.FileNameUtil;
import com.hzwangda.aigov.officeonline.common.domain.entity.SysFileVersion;
import com.hzwangda.aigov.officeonline.common.repository.SysFileVersionRepository;
import com.hzwangda.aigov.officeonline.common.service.FileVersionService;
import com.hzwangda.aigov.officeonline.wps.domain.dto.*;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileHistoryResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileInfoResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileSaveResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileVersionResponse;
import com.hzwangda.aigov.officeonline.wps.propertie.WpsProperties;
import com.hzwangda.aigov.officeonline.wps.service.WpsFileService;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.modules.system.service.UserService;
import com.wangda.oa.modules.system.service.dto.UserDto;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.service.mapstruct.LocalStorageMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class WpsFileServiceImpl implements WpsFileService {

    private final WpsProperties wpsProperties;
    private final IStorageService storageService;
    private final StorageManageService storageManageService;
    private final FileVersionService fileVersionService;
    private final LocalStorageMapper localStorageMapper;
    private final SysFileVersionRepository fileVersionRepository;
    private final UserService userService;

    @Override
    public FileInfoResponse getFileInfo(Long fileId, String permission, String securityToken) {
        String username = securityToken;

        // 获取当前版本信息，若不存在则自动产生0版本
        SysFileVersion currentVersion = fileVersionService.getCurrentVersionByFileId(fileId);
        if (Objects.isNull(currentVersion)) {
            currentVersion = fileVersionService.newZeroVersion(fileId, username);
        }

        LocalStorageDto localStorageDto = storageManageService.findById(fileId);

        FileInfoResponse fileInfoResponse = new FileInfoResponse();

        WpsFileDto resFile = fileInfoResponse.getFile();
        resFile.setId(fileId.toString());
        resFile.setVersion(currentVersion.getFileVersion());

        if (resFile.getVersion()==0) {
            resFile.setCreator(securityToken);
            resFile.setCreateTime(new Date());
            resFile.setModifier(securityToken);
            resFile.setModifyTime(new Date());
        } else {
            SysFileVersion firstVersion = currentVersion.getFileVersion()<=1 ? currentVersion : fileVersionService.getFirstVersionByMainFileId(currentVersion.getMainStorageId());
            resFile.setCreator(firstVersion.getSaveBy());
            resFile.setCreateTime(firstVersion.getSaveTime());
            resFile.setModifier(currentVersion.getSaveBy());
            resFile.setModifyTime(currentVersion.getSaveTime());
        }

        // 文件名必须带后缀
        if (FileNameUtil.getSuffix(localStorageDto.getName()).equalsIgnoreCase(localStorageDto.getSuffix())) {
            resFile.setName(localStorageDto.getName());
        } else {
            resFile.setName(localStorageDto.getName() + "." + localStorageDto.getSuffix());
        }

        // 文件尺寸必须准确
        resFile.setSize(Long.valueOf(storageService.size(localStorageDto.getPath())).intValue());
        resFile.setDownloadUrl(getDownloadFileUrl(localStorageDto.getId()));

        WpsUserWithPermissionDto resUser = fileInfoResponse.getUser();
        String nickName = userService.findByName(username).getNickName();
        resUser.setId(username);
        resUser.setName(nickName);
        resUser.setAvatarUrl("");
        resUser.setPermission(permission);

        return fileInfoResponse;
    }

    @Override
    public FileVersionResponse getFileInfoByVersion(Long fileId, int version) {
        SysFileVersion firstVersion = fileVersionService.getFirstVersionByFileId(fileId);

        SysFileVersion fileVersion = fileVersionRepository.findByMainStorageIdAndFileVersion(firstVersion.getMainStorageId(), version);
        LocalStorageDto localStorageDto = storageManageService.findById(fileVersion.getStorageId());

        WpsFileVersionDto fileVersionDto = new WpsFileVersionDto();
        fileVersionDto.setId(fileVersion.getStorageId().toString());
        fileVersionDto.setVersion(fileVersion.getFileVersion());

        fileVersionDto.setModifier(fileVersion.getSaveBy());
        fileVersionDto.setModifyTime(fileVersion.getSaveTime());

        fileVersionDto.setCreator(firstVersion.getSaveBy());
        fileVersionDto.setCreateTime(firstVersion.getSaveTime());

        // 文件名必须带后缀
        if (FileNameUtil.getSuffix(localStorageDto.getName()).equalsIgnoreCase(localStorageDto.getSuffix())) {
            fileVersionDto.setName(localStorageDto.getName());
        } else {
            fileVersionDto.setName(localStorageDto.getName() + "." + localStorageDto.getSuffix());
        }

        // 文件尺寸必须准确
        fileVersionDto.setSize(Long.valueOf(storageService.size(localStorageDto.getPath())).intValue());
        fileVersionDto.setDownloadUrl(getDownloadFileUrl(localStorageDto.getId()));

        FileVersionResponse res = new FileVersionResponse();
        res.setFile(fileVersionDto);
        return res;
    }

    @Override
    public FileSaveResponse saveFile(Long fileId, String username, String saveType, MultipartFile file) {
        String name = file.getOriginalFilename();
        LocalStorage localStorage = storageService.create(name, file);

        SysFileVersion latestFileVersion = fileVersionService.getLatestVersionByFileId(fileId);
        if (latestFileVersion == null) {
            latestFileVersion = new SysFileVersion();
            latestFileVersion.setMainStorageId(fileId);
            latestFileVersion.setFileVersion(0);
        }

        SysFileVersion fileVersion = new SysFileVersion();
        fileVersion.setStorageId(localStorage.getId());
        fileVersion.setMainStorageId(latestFileVersion.getMainStorageId());
        fileVersion.setFileVersion(latestFileVersion.getFileVersion() + 1);
        fileVersion.setSaveType(saveType);
        fileVersion.setSaveBy(username);
        fileVersion.setSaveTime(new Date());
        fileVersionRepository.save(fileVersion);

        FileSaveResponse fileSaveResponse = new FileSaveResponse();
        WpsFileSimpleDto wpsFile = fileSaveResponse.getFile();
        wpsFile.setId(localStorage.getId().toString());
        wpsFile.setName(localStorage.getRealName());
        wpsFile.setVersion(fileVersion.getFileVersion());
        wpsFile.setSize(Long.valueOf(storageService.size(localStorage.getPath())).intValue());
        wpsFile.setDownloadUrl(getDownloadFileUrl(localStorage.getId()));

        return fileSaveResponse;
    }

    @Override
    public FileHistoryResponse queryFileHistories(Long fileId, Pageable pageable) {
        FileHistoryResponse res = new FileHistoryResponse();

        SysFileVersion firstFileVersion = fileVersionService.getFirstVersionByFileId(fileId);


        List<SysFileVersion> fileVersions = fileVersionRepository.queryHistories(firstFileVersion.getMainStorageId(), pageable);
        for (SysFileVersion fileVersion : fileVersions) {
            LocalStorageDto localStorageDto = storageManageService.findById(fileVersion.getStorageId());

            WpsFileHistoryDto historyDto = new WpsFileHistoryDto();
            historyDto.setId(fileVersion.getStorageId().toString());
            historyDto.setVersion(fileVersion.getFileVersion());

            WpsUserDto modifier = new WpsUserDto();
            modifier.setId(fileVersion.getSaveBy());
            String saveNickname = fileVersion.getSaveBy();
            UserDto saveUser = userService.findByName(fileVersion.getSaveBy());
            if(saveUser!= null){
                saveNickname = saveUser.getNickName();
            }
            modifier.setName(saveNickname);
            historyDto.setModifier(modifier);
            historyDto.setModifyTime(fileVersion.getSaveTime());

            WpsUserDto creator = new WpsUserDto();
            creator.setId(firstFileVersion.getSaveBy());
            String createNickname = firstFileVersion.getSaveBy();
            UserDto createUser = userService.findByName(firstFileVersion.getSaveBy());
            if(createUser != null){
                createNickname = createUser.getNickName();
            }
            creator.setName(createNickname);
            historyDto.setCreator(creator);
            historyDto.setCreateTime(firstFileVersion.getSaveTime());

            // 文件名必须带后缀
            if (FileNameUtil.getSuffix(localStorageDto.getName()).equalsIgnoreCase(localStorageDto.getSuffix())) {
                historyDto.setName(localStorageDto.getName());
            } else {
                historyDto.setName(localStorageDto.getName() + "." + localStorageDto.getSuffix());
            }

            // 文件尺寸必须准确
            historyDto.setSize(Long.valueOf(storageService.size(localStorageDto.getPath())).intValue());
            historyDto.setDownloadUrl(getDownloadFileUrl(localStorageDto.getId()));

            res.getHistories().add(historyDto);
        }
        return res;
    }

    @Override
    public void rename(Long fileId, String newName) {
        SysFileVersion latestVersion = fileVersionService.getLatestVersionByFileId(fileId);
        Long latestVersionFileId = latestVersion != null ? latestVersion.getStorageId() : fileId;

        LocalStorageDto localStorageDto = storageManageService.findById(latestVersionFileId);
        LocalStorage localStorage = localStorageMapper.toEntity(localStorageDto);
        localStorage.setName(newName);
        storageManageService.update(localStorage);
    }

    private String getDownloadFileUrl(Long fileId) {
        return wpsProperties.getDownloadHost() + "/api/localStorage/downloadFile/" + fileId;
    }

}
