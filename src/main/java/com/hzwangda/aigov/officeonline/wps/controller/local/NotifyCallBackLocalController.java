package com.hzwangda.aigov.officeonline.wps.controller.local;

import com.hzwangda.aigov.officeonline.wps.domain.request.NotifyRequest;
import com.wangda.oa.annotation.AnonymousAccess;
import lombok.extern.java.Log;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * 消息回调接口
 */
@RestController
@RequestMapping("/api/wps/local/v1/3rd/onnotify")
@Log
public class NotifyCallBackLocalController {

    /**
     * 回调通知
     */
    @PostMapping
    @AnonymousAccess
    public ResponseEntity<Object> onNotify(
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_third_appid") String appId,
            @RequestBody NotifyRequest notifyRequest
    ) {
        log.info("回调通知param:" + notifyRequest);
        return new ResponseEntity<>("", HttpStatus.OK);
    }

}
