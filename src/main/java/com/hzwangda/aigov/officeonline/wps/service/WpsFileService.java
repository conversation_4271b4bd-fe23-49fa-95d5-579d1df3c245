package com.hzwangda.aigov.officeonline.wps.service;

import com.hzwangda.aigov.officeonline.wps.domain.response.FileHistoryResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileInfoResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileSaveResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileVersionResponse;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

public interface WpsFileService {

    FileInfoResponse getFileInfo(Long mainFileId, String permission, String securityToken);

    FileVersionResponse getFileInfoByVersion(Long mainFileId, int version);

    FileSaveResponse saveFile(Long mainFileId, String token, String saveType, MultipartFile file);

    FileHistoryResponse queryFileHistories(Long mainFileId, Pageable pageable);

    void rename(Long mainFileId, String newName);

}
