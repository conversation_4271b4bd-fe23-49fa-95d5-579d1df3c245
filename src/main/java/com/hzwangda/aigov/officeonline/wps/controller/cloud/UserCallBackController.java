package com.hzwangda.aigov.officeonline.wps.controller.cloud;

import com.hzwangda.aigov.officeonline.wps.domain.dto.WpsUserDto;
import com.hzwangda.aigov.officeonline.wps.domain.request.UserInfoRequest;
import com.hzwangda.aigov.officeonline.wps.domain.response.UserInfoResponse;
import com.wangda.oa.annotation.AnonymousAccess;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021-09-29
 * WPS在线编辑用户相关回调接口
 */
@RestController
@RequestMapping("/api/wps/cloud/v1/3rd/user")
public class UserCallBackController {

    /**
     * 获取用户信息
     */
    @PostMapping("/info")
    @AnonymousAccess
    public ResponseEntity<Object> info(
            @RequestHeader("x-wps-weboffice-token") String token,
            @RequestHeader("x-weboffice-file-id") Long fileId,
            @RequestParam("_w_appid") String appId,
            @RequestParam("_w_signature") String signature,
            @RequestBody UserInfoRequest userInfoRequest
    ) {
        userInfoRequest.getIds();
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        WpsUserDto user = new WpsUserDto();
        user.setId("admin");
        user.setName("系统管理员");
        userInfoResponse.getUsers().add(user);
        return ResponseEntity.ok(userInfoResponse);
    }

}
