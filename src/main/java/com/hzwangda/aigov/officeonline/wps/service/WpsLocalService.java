package com.hzwangda.aigov.officeonline.wps.service;

import com.hzwangda.aigov.officeonline.wps.domain.dto.WpsTokenDto;
import com.hzwangda.aigov.officeonline.wps.domain.response.WebOfficeResponse;

/**
 * <AUTHOR>
 * @Date 2021/10/18 上午11:09
 **/
public interface WpsLocalService {

    /**
     * 获取编辑url
     *
     * @param fileId
     * @return
     */
    WebOfficeResponse getEditUrl(Long fileId);

    /**
     * 获取预览url
     *
     * @param fileId
     * @return
     */
    WebOfficeResponse getReadUrl(Long fileId);

    /**
     * 获取token
     *
     * @return
     */
    WpsTokenDto getToken();
}
