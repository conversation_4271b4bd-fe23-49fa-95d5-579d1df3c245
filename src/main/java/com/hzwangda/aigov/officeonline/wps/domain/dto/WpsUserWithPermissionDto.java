package com.hzwangda.aigov.officeonline.wps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class WpsUserWithPermissionDto {

    private String id; //用户id，长度小于32

    private String name; //用户名称

    private String permission; //用户操作权限，write：可编辑，read：预览

    @JSONField(name = "avatar_url")
    private String avatarUrl; //用户头像地址
}
