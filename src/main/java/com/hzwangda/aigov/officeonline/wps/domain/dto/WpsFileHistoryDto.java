package com.hzwangda.aigov.officeonline.wps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class WpsFileHistoryDto {

    private String id; //文件id,字符串长度小于40

    private String name; //文件名(必须带文件后缀)

    private int version; //当前版本号，必须大于 0，同时位数小于 11

    private int size; //文件大小，单位为B(文件真实大小，否则会出现异常)

    private WpsUserDto creator;

    @JSONField(name = "create_time", format = "unixtime")
    private Date createTime; //创建时间，时间戳，单位为秒

    private WpsUserDto modifier;

    @JSONField(name = "modify_time", format = "unixtime")
    private Date modifyTime; //修改时间，时间戳，单位为秒

    @JSONField(name = "download_url")
    private String downloadUrl; //文档下载地址

}
