package com.hzwangda.aigov.officeonline.wps.controller;

import com.hzwangda.aigov.officeonline.common.domain.entity.SysFileVersion;
import com.hzwangda.aigov.officeonline.common.service.FileVersionService;
import com.hzwangda.aigov.officeonline.wps.domain.response.FileHistoryResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.WebOfficeResponse;
import com.hzwangda.aigov.officeonline.wps.propertie.WpsProperties;
import com.hzwangda.aigov.officeonline.wps.service.WebOfficeService;
import com.hzwangda.aigov.officeonline.wps.service.WpsLocalService;
import com.wangda.oa.annotation.AnonymousAccess;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@Api(tags = "WPS在线编辑调用地址接口")
@RequestMapping("/api/wps/webOffice")
public class WebOfficeController {

    private final WpsProperties wpsProperties;

    private final WebOfficeService webOfficeService;

    private final FileVersionService fileVersionService;

    private final WpsLocalService wpsLocalService;

    /**
     * 根据文件ID获取在线编辑URL
     */
    @GetMapping("/getWpsUrlByFileId")
    public ResponseEntity<Object> getWpsUrlByFileId(HttpServletRequest request, Long fileId, String permission) {
        WebOfficeResponse webOfficeResponse;

        if("local".equalsIgnoreCase(wpsProperties.getDeployType())) {
            if("write".equals(permission)) {
                webOfficeResponse = wpsLocalService.getEditUrl(fileId);
            }else {
                webOfficeResponse = wpsLocalService.getReadUrl(fileId);
            }
        }else {
            webOfficeResponse = webOfficeService.buildWpsUrlByFileId(fileId, permission);
        }

        return new ResponseEntity<>(webOfficeResponse, HttpStatus.OK);
    }

    @GetMapping("/getWpsUrlByNew")
    public ResponseEntity<Object> getWpsUrlByNew(String fileType) {
//        WebOfficeResponse webOfficeResponse = webOfficeService.buildWpsUrlByNew(fileType);
        WebOfficeResponse webOfficeResponse;
        //TODO 新建的时候需要修改，目前固定一个文件
        Long fileId = webOfficeService.getNewFileId();
        if("local".equalsIgnoreCase(wpsProperties.getDeployType())) {
            webOfficeResponse = wpsLocalService.getEditUrl(fileId);
        }else {
            webOfficeResponse = webOfficeService.buildWpsUrlByFileId(fileId, "write");
        }
        return new ResponseEntity<>(webOfficeResponse, HttpStatus.OK);
    }

    @GetMapping("/getFileIdByFileIdAndVersion")
    public ResponseEntity<Object> getFileIdByFileIdAndVersion(Long fileId, Integer version) {
        Long currentFileId = fileVersionService.getFileIdByFileIdAndVersion(fileId, version);
        Map<String, Object> map = new HashMap<>();
        map.put("fileId", currentFileId);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    /**
     * 获取所有历史版本文件信息
     * 获取当前文档所有历史版本的文件信息，以数组的形式，按版本号从大到小的顺序返回响应。（会影响历史版本相关的功能）
     */
    @GetMapping("/fileHistories")
    @AnonymousAccess
    public ResponseEntity<Object> fileHistories(@RequestParam Long fileId) {
        FileHistoryResponse response = webOfficeService.queryFileHistories(fileId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @GetMapping(value = "/getLatestVersion")
    @ApiOperation("查询最新版本")
    public ResponseEntity<Object> getLatestVersion(Long fileId) {
        SysFileVersion sysFileVersion = fileVersionService.getLatestVersionByFileId(fileId);
        return new ResponseEntity<>(sysFileVersion, HttpStatus.OK);
    }

}
