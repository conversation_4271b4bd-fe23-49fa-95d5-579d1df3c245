package com.hzwangda.aigov.officeonline.wps.propertie;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 属性文件
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "wps")
public class WpsProperties {

    private String deployType; // cloud: 官方服务 or local: 本地化部署
    private String domain;
    private String appId;
    private String appSecret;
    private String downloadHost;

}
