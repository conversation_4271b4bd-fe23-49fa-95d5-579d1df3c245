package com.hzwangda.aigov.officeonline.wps.service.impl;

import com.hzwangda.aigov.officeonline.wps.domain.dto.WpsTokenDto;
import com.hzwangda.aigov.officeonline.wps.domain.response.InscopeTokenResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.UrlResponse;
import com.hzwangda.aigov.officeonline.wps.domain.response.WebOfficeResponse;
import com.hzwangda.aigov.officeonline.wps.propertie.WpsProperties;
import com.hzwangda.aigov.officeonline.wps.service.WpsLocalService;
import com.hzwangda.aigov.officeonline.wps.util.SecurityTokenUtil;
import com.hzwangda.aigov.officeonline.wps.util.WpsFileUtil;
import com.hzwangda.aigov.officeonline.wps.util.WpsTokenUtil;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Objects;

@Service
public class WpsLocalServiceImpl implements WpsLocalService {

    @Autowired
    private StorageManageService storageService;

    @Autowired
    private WpsProperties wpsProperties;

    @Autowired
    @Qualifier("wpsRestTemplate")
    private RestTemplate restTemplate;

    @Override
    public WebOfficeResponse getEditUrl(Long fileId) {
        LocalStorageDto localStorageDto = storageService.findById(fileId);
        if (localStorageDto != null) {
            String token = SecurityTokenUtil.getToken();

            String fileTypeCode = WpsFileUtil.getFileTypeCode(localStorageDto.getSuffix());
            URI url = UriComponentsBuilder
                    .fromHttpUrl(wpsProperties.getDomain() + "/weboffice/v1/url")
                    .queryParam("app_token", WpsTokenUtil.getAppToken())
                    .queryParam("file_id", fileId)
                    .queryParam("type", fileTypeCode)
                    .queryParam("_w_third_permission", "write")
                    .queryParam("_w_third_token", token)
                    .build().toUri();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
            UrlResponse response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, UrlResponse.class).getBody();
            if (Objects.isNull(response)) {
                return null;
            }
            System.out.println(response.getUrl());


            return new WebOfficeResponse(response.getUrl(), token, fileId.toString());
        }
        return null;
    }

    @Override
    public WebOfficeResponse getReadUrl(Long fileId) {
        LocalStorageDto localStorageDto = storageService.findById(fileId);
        if (localStorageDto != null) {
            String token = SecurityTokenUtil.getToken();
            String fileTypeCode = WpsFileUtil.getFileTypeCode(localStorageDto.getSuffix());
            URI url = UriComponentsBuilder
                    .fromHttpUrl(wpsProperties.getDomain() + "/preview/v1/url")
                    .queryParam("app_token", WpsTokenUtil.getAppToken())
                    .queryParam("file_id", fileId)
                    .queryParam("type", fileTypeCode)
                    .queryParam("_w_third_permission", "read")
                    .queryParam("_w_third_token", token)
                    .build().toUri();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
            UrlResponse response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, UrlResponse.class).getBody();
            if (Objects.isNull(response)) {
                return null;
            }
            System.out.println(response.getUrl());

            return new WebOfficeResponse(response.getUrl(), token, fileId.toString());
        }
        return null;
    }

    @Override
    public WpsTokenDto getToken() {
        URI url = UriComponentsBuilder
                .fromHttpUrl(wpsProperties.getDomain() + "/auth/v1/app/inscope/token")
                .queryParam("app_id", wpsProperties.getAppId())
                .queryParam("scope", "file_preview,file_edit,file_format_control")
                .build().toUri();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(null, headers);
        InscopeTokenResponse res = restTemplate.exchange(url, HttpMethod.GET, request, InscopeTokenResponse.class).getBody();
        System.out.println(res.getToken().getAppToken() + " " + res.getToken().getExpiresIn());
        return res.getToken();
    }
}
