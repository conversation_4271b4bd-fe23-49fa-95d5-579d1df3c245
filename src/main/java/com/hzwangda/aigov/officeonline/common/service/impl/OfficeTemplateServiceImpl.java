package com.hzwangda.aigov.officeonline.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.hzwangda.aigov.officeonline.common.domain.entity.SysOfficeTemplate;
import com.hzwangda.aigov.officeonline.common.enums.OfficeTemplateTypeEnum;
import com.hzwangda.aigov.officeonline.common.repository.SysOfficeTemplateRepository;
import com.hzwangda.aigov.officeonline.common.service.OfficeTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class OfficeTemplateServiceImpl implements OfficeTemplateService {

    private final SysOfficeTemplateRepository officeTemplateRepository;

    @Override
    public Long getDefaultTemplateStorageId() {
        SysOfficeTemplate template = officeTemplateRepository.findByIsDefault(1);

        if(Objects.isNull(template)) {
            return null;
        }

        return template.getStorageId();
    }

    @Override
    public List<SysOfficeTemplate> findAllHeaderTemplate() {
        return officeTemplateRepository.findByTemplateType(OfficeTemplateTypeEnum.RED_HEAD.getValue());
    }

    @Override
    public List<SysOfficeTemplate> findAllDeptHeaderTemplate(List<String> deptNames) {
        if(CollUtil.isNotEmpty(deptNames)) {
            return officeTemplateRepository.findByBelongToDeptIn(deptNames);
        }
        return officeTemplateRepository.findAll();
    }

}
