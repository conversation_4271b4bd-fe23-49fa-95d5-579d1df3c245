package com.hzwangda.aigov.officeonline.common.service;

import com.hzwangda.aigov.officeonline.common.domain.entity.SysFileVersion;
import com.wangda.oa.domain.LocalStorage;

public interface FileVersionService {

    Long getFileIdByFileIdAndVersion(Long fileId, Integer version);

    /**
     * 根据主文件ID获取最新文件版本
     * @param mainFileId
     * @return
     */
    SysFileVersion getLatestVersionByMainFileId(Long mainFileId);

    SysFileVersion getCurrentVersionByFileId(Long fileId);

    /**
     * 根据过程文件版本查询最新文件版本
     * @param fileId
     * @return
     */
    SysFileVersion getLatestVersionByFileId(Long fileId);

    SysFileVersion getFirstVersionByFileId(Long fileId);

    SysFileVersion getFirstVersionByMainFileId(Long mainFileId);

    SysFileVersion newZeroVersion(Long fileId, String username);

    SysFileVersion saveNewVersion(LocalStorage localStorage);
}
