package com.hzwangda.aigov.officeonline.common.domain.entity;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.CreatedBy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "sys_file_version")
@Getter
@Setter
public class SysFileVersion {
    @Id
    @Column(name = "storage_id", nullable = false)
    private Long storageId;

    @Column(name = "main_storage_id")
    private Long mainStorageId;

    @Column(name = "file_version")
    private Integer fileVersion;

    @Column(name = "save_type")
    private String saveType;

    @CreatedBy
    @Column(name = "save_by")
    @ApiModelProperty(value = "保存人", hidden = true)
    private String saveBy;

    @CreationTimestamp
    @Column(name = "save_time")
    @ApiModelProperty(value = "保存时间", hidden = true)
    @JSONField(name = "save_time", format = "unixtime")
    private Date saveTime;

}
