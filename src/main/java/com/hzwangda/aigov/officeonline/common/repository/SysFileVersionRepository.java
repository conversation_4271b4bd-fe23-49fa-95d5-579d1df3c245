package com.hzwangda.aigov.officeonline.common.repository;

import com.hzwangda.aigov.officeonline.common.domain.entity.SysFileVersion;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface SysFileVersionRepository extends JpaRepository<SysFileVersion, Long> {

    /**
     * 根据文件ID获取对应版本记录
     *
     * @param storageId
     * @return
     */
    SysFileVersion findByStorageId(Long storageId);

    SysFileVersion findByMainStorageIdAndFileVersion(Long mainStorageId, Integer fileVersion);

    /**
     * 根据主文件ID获取最新版本记录
     *
     * @param mainStorageId
     * @return
     */
    SysFileVersion findFirstByMainStorageIdOrderByFileVersionDesc(Long mainStorageId);

    @Query("select s from SysFileVersion s where s.mainStorageId = ?1 and s.fileVersion > 0 order by s.fileVersion DESC")
    List<SysFileVersion> queryHistories(Long mainStorageId, Pageable pageable);

}
