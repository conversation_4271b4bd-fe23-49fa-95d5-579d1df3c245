package com.hzwangda.aigov.officeonline.common.service.impl;

import com.hzwangda.aigov.officeonline.common.domain.entity.SysFileVersion;
import com.hzwangda.aigov.officeonline.common.repository.SysFileVersionRepository;
import com.hzwangda.aigov.officeonline.common.service.FileVersionService;
import com.wangda.oa.domain.LocalStorage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@RequiredArgsConstructor
public class FileVersionServiceImpl implements FileVersionService {

    private final SysFileVersionRepository fileVersionRepository;

    @Override
    public Long getFileIdByFileIdAndVersion(Long fileId, Integer version) {
        Long mainFileId = fileId;
        if (version != 1) {
            SysFileVersion currentFileVersion = fileVersionRepository.findByStorageId(fileId);
            if (currentFileVersion == null) return null;
            mainFileId = currentFileVersion.getMainStorageId();
        }

        SysFileVersion fileVersion = fileVersionRepository.findByMainStorageIdAndFileVersion(mainFileId, version);
        return fileVersion != null ? fileVersion.getStorageId() : null;
    }

    @Override
    public SysFileVersion getLatestVersionByMainFileId(Long mainFileId) {
        SysFileVersion sysFileVersion = fileVersionRepository.findFirstByMainStorageIdOrderByFileVersionDesc(mainFileId);
        return sysFileVersion;
    }

    @Override
    public SysFileVersion getCurrentVersionByFileId(Long fileId) {
        return fileVersionRepository.findByStorageId(fileId);
    }

    @Override
    public SysFileVersion getLatestVersionByFileId(Long fileId) {
        SysFileVersion currentFileVersion = fileVersionRepository.findByStorageId(fileId);
        if (currentFileVersion == null) return null;

        Long mainStorageId = currentFileVersion.getMainStorageId();
        SysFileVersion latestFileVersion = fileVersionRepository.findFirstByMainStorageIdOrderByFileVersionDesc(mainStorageId);
        return latestFileVersion;
    }

    @Override
    public SysFileVersion getFirstVersionByFileId(Long fileId) {
        SysFileVersion currentFileVersion = fileVersionRepository.findByStorageId(fileId);
        if (currentFileVersion == null) return null;

        if (currentFileVersion.getFileVersion() == 1) {
            return currentFileVersion;
        }

        return getFirstVersionByMainFileId(currentFileVersion.getMainStorageId());
    }

    @Override
    public SysFileVersion getFirstVersionByMainFileId(Long mainFileId) {
        return fileVersionRepository.findByMainStorageIdAndFileVersion(mainFileId, 1);
    }

    @Override
    public SysFileVersion newZeroVersion(Long fileId, String username) {
        SysFileVersion fileVersion = new SysFileVersion();
        fileVersion.setStorageId(fileId);
        fileVersion.setMainStorageId(fileId);
        fileVersion.setFileVersion(0);
        fileVersion.setSaveType("new");
        fileVersion.setSaveBy(username);
        fileVersion.setSaveTime(new Date());
        return fileVersionRepository.save(fileVersion);
    }

    @Override
    public SysFileVersion saveNewVersion(LocalStorage localStorage) {
        SysFileVersion latestFileVersion = getLatestVersionByFileId(localStorage.getId());
        if (latestFileVersion == null) {
            latestFileVersion = new SysFileVersion();
            latestFileVersion.setMainStorageId(localStorage.getId());
            latestFileVersion.setFileVersion(0);
        }

        SysFileVersion fileVersion = new SysFileVersion();
        fileVersion.setStorageId(localStorage.getId());
        fileVersion.setMainStorageId(latestFileVersion.getMainStorageId());
        fileVersion.setFileVersion(latestFileVersion.getFileVersion() + 1);
        fileVersion.setSaveType("normal");
        fileVersion.setSaveBy(localStorage.getCreateBy());
        fileVersion.setSaveTime(new Date());
        return fileVersionRepository.save(fileVersion);
    }

}
