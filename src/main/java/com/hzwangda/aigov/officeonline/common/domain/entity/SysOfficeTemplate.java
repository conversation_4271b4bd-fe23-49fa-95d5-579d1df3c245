package com.hzwangda.aigov.officeonline.common.domain.entity;

import com.wangda.oa.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@Entity
@Table(name = "sys_office_template")
public class SysOfficeTemplate extends BaseEntity implements Serializable {
    @Id
    @Column(name = "template_id", nullable = false)
    private Long templateId;

    @Column(name = "template_name")
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @Column(name = "template_type")
    @ApiModelProperty(value = "模板类型")
    private String templateType;

    @Column(name = "is_default")
    @ApiModelProperty(value = "是否空白模板")
    private Integer isDefault;

    @ApiModelProperty(value = "模板文件ID")
    private Long storageId;

    @ApiModelProperty(value = "归属部门")
    private String belongToDept;

}
