package com.hzwangda.aigov.officeonline.common.repository;

import com.hzwangda.aigov.officeonline.common.domain.entity.SysOfficeTemplate;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface SysOfficeTemplateRepository extends JpaRepository<SysOfficeTemplate, Long> {

    SysOfficeTemplate findByIsDefault(Integer isDefault);

    SysOfficeTemplate findByTemplateId(Long templateId);

    List<SysOfficeTemplate> findByTemplateType(String templateType);

    List<SysOfficeTemplate> findByBelongToDeptIn(List<String> templateType);

}
