package com.hzwangda.aigov.officeonline.common.controller;

import com.hzwangda.aigov.officeonline.common.domain.entity.SysOfficeTemplate;
import com.hzwangda.aigov.officeonline.common.service.OfficeTemplateService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@Api(tags = "公文模板管理")
@RequestMapping("/api/officeonline/officeTemplate")
public class OfficeTemplateController {

    private final OfficeTemplateService officeTemplateService;

    @GetMapping("/list")
    public ResponseEntity<Object> listHeaderTemplate() {
        List<SysOfficeTemplate> headerTemplateList = officeTemplateService.findAllHeaderTemplate();
        Map<String, Object> map = new HashMap<>(1);
        map.put("content", headerTemplateList);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    @GetMapping("/deptList")
    public ResponseEntity<Object> deptHeaderTemplateList(@RequestParam List<String> deptNames) {
        List<SysOfficeTemplate> headerTemplateList = officeTemplateService.findAllDeptHeaderTemplate(deptNames);
        Map<String, Object> map = new HashMap<>(1);
        map.put("content", headerTemplateList);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }
}
