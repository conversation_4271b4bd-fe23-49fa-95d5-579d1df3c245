package com.hzwangda.aigov.officeonline.goldgrid.controller;

import DBstep.iMsgServer2000;
import cn.hutool.core.io.IoUtil;
import com.hzwangda.aigov.docconvert.common.domain.dto.StorageConversionDto;
import com.hzwangda.aigov.docconvert.common.domain.entity.SysStorageConversion;
import com.hzwangda.aigov.docconvert.common.domain.mapstruct.StorageConversionMapper;
import com.hzwangda.aigov.docconvert.common.enums.FileConversionEnum;
import com.hzwangda.aigov.docconvert.common.repository.SysStorageConversionRepository;
import com.hzwangda.aigov.docconvert.common.service.FormatConvertService;
import com.hzwangda.aigov.officeonline.common.service.FileVersionService;
import com.wangda.oa.annotation.AnonymousAccess;
import com.wangda.oa.annotation.Log;
import com.wangda.oa.annotation.rest.AnonymousPostMapping;
import com.wangda.oa.config.FileProperties;
import com.wangda.oa.domain.LocalStorage;
import com.wangda.oa.exception.BadRequestException;
import com.wangda.oa.service.IStorageService;
import com.wangda.oa.service.StorageManageService;
import com.wangda.oa.service.dto.LocalStorageDto;
import com.wangda.oa.service.impl.LocalStorageServiceImpl;
import com.wangda.oa.utils.FileUtil;
import com.wangda.oa.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-07-30
 **/
@RestController
@RequiredArgsConstructor
@Api(tags = "金格iWebOffice服务端")
@RequestMapping("/api/iweboffice/officeServer")
@CrossOrigin
@Slf4j
public class OfficeServerController {

    private final IStorageService localStorageService;
    private final StorageManageService storageManageService;
    private final FileVersionService fileVersionService;

    private final SysStorageConversionRepository sysStorageConversionRepository;

    private final StorageConversionMapper storageConversionMapper;

    private final FileProperties fileProperties;

    private final FormatConvertService formatService;

    /**
     * 加载文件
     * @param storageId
     * @param response
     */
    @AnonymousPostMapping(value = "/loadFile")
    @Log("加载文件")
    @ApiOperation("加载文件")
    public void loadFile(@RequestParam("storageId") String storageId, HttpServletResponse response) {
        if(StringUtils.isBlank(storageId)) {
            throw new BadRequestException("文件id为空");
        }
        LocalStorageDto localStorage = storageManageService.findById(Long.valueOf(storageId));
        InputStream fis = null;
        try {
            response.setContentType("application/x-msdownload;charset=utf-8");
            response.setContentLength(Long.valueOf(localStorageService.size(localStorage.getPath())).intValue());
            fis = localStorageService.getInputStream(localStorage.getPath());
            IoUtil.copy(fis, response.getOutputStream());
            response.getOutputStream().flush();
            response.getOutputStream().close();
            response.flushBuffer();
        }catch(IOException e) {
            e.printStackTrace();
        }finally {
            IoUtil.close(fis);
        }
    }

    /**
     * 存储文件和转换文件
     * @param name     文件名称
     * @param fileData 文件对象
     * @return 文件id
     */
    @AnonymousPostMapping(value = "/saveFile")
    @Log("存储文件和转换文件")
    @ApiOperation("存储文件和转换文件")
    public ResponseEntity<StorageConversionDto> saveFile(@RequestParam String name, MultipartFile fileData) {
        if(StringUtils.isBlank(name)) {
            throw new BadRequestException("文件名为空");
        }

        LocalStorage localStorage = localStorageService.create(name, fileData);
        fileVersionService.saveNewVersion(localStorage);

        // 已不需返回复杂类型，只需要LocalStorageDto对象即可，LocalStorage缺少storageId字段，无法兼容前端
        StorageConversionDto storageConversionDto = storageConversionMapper.toDto(localStorage);

        return new ResponseEntity<>(storageConversionDto, HttpStatus.OK);
    }

    @AnonymousPostMapping(value = "/savePdf")
    @Log("保存PDF版式文件，并同时保存清稿格式")
    @ApiOperation("保存PDF版式文件，并同时保存清稿格式")
    public ResponseEntity<Object> savePdf(@RequestParam String name, @RequestParam String originalStorageId, MultipartFile fileData) {
        if(StringUtils.isBlank(name)) {
            throw new BadRequestException("文件名为空");
        }

        LocalStorage localStorage = localStorageService.create(name, fileData);

        if(localStorage != null) {
            formatService.convertToFormats(Long.valueOf(originalStorageId), new FileConversionEnum[]{
                    FileConversionEnum.DOC, FileConversionEnum.TXT});

            SysStorageConversion docStorageConversion = new SysStorageConversion();
            docStorageConversion.setConversionStorageId(localStorage.getId());
            docStorageConversion.setOriginalStorageId(Long.valueOf(originalStorageId));
            docStorageConversion.setConversionType("pdf");
            sysStorageConversionRepository.save(docStorageConversion);
            return new ResponseEntity<>(localStorage, HttpStatus.OK);
        }

        return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
    }

    @PostMapping
    @AnonymousAccess
    public void service(HttpServletRequest request, HttpServletResponse response) throws IOException {
        iMsgServer2000 MsgObj = new iMsgServer2000();
        MsgObj.Load(request);

        String mOption = MsgObj.GetMsgByName("OPTION");//请求参数
        String mRecordID;

        switch(mOption) {
            case "LOADFILE":  // 加载服务端文件
                mRecordID = MsgObj.GetMsgByName("RECORDID");//取得文档编号
                MsgObj.MsgTextClear();
                log.info("LOADFILE: " + mRecordID);

                // 从服务器上根据文件ID调入文件
                LocalStorageDto localStorage = storageManageService.findById(Long.valueOf(mRecordID));
                if(localStorage != null) {
                    File file = getFile(localStorageService, localStorage.getPath());
                    String path = file.getPath();
                    MsgObj.MsgFileLoad(path); //将文件信息打包
                    MsgObj.SetMsgByName("STATUS", "打开成功!"); //设置状态信息
                    MsgObj.MsgError(""); //清除错误信息
                }else {
                    log.error("LOADFILE: 打开文档失败 " + mRecordID);
                    MsgObj.MsgError("打开失败!"); //设置错误信息
                }
                MsgObj.Send(response); //8.1.0.2新版后台类新增的功能接口，返回信息包数据
                break;

            case "LOADFILEOLD":
                loadFile(MsgObj.GetMsgByName("storageId"), response);
                break;
            case "SAVEFILE": // 保存文件：花脸稿文件
                LocalStorage savefile = addLocalStorage(MsgObj, "doc");
                fileVersionService.saveNewVersion(savefile);

                if(savefile != null) {
                    MsgObj.SetMsgByName("STATUS", "保存成功!");                       //设置状态信息
                    //放入新增的文件id
                    MsgObj.SetMsgByName("storageId", String.valueOf(savefile.getId()));
                    MsgObj.MsgError("");                                              //清除错误信息
                }else {
                    MsgObj.MsgError("保存失败!");                                     //设置错误信息
                }
                MsgObj.MsgFileClear();                                              //清除文档内容
                MsgObj.Send(response);
                break;
            case "SAVEFILEOLD":
                //saveFile(msgObj.GetMsgByName("name"),fileData);
                break;
            case "SAVEOFD": // 保存文件：OFD格式
                mRecordID = MsgObj.GetMsgByName("RECORDID");

                LocalStorage saveOfdFile = addLocalStorage(MsgObj, "ofd");
                if(saveOfdFile != null) {
                    // 保存清稿格式
                    formatService.convertToFormats(Long.valueOf(mRecordID), new FileConversionEnum[]{
                            FileConversionEnum.DOC, FileConversionEnum.TXT});

                    SysStorageConversion docStorageConversion = new SysStorageConversion();
                    docStorageConversion.setConversionStorageId(saveOfdFile.getId());
                    docStorageConversion.setOriginalStorageId(Long.valueOf(mRecordID));
                    docStorageConversion.setConversionType("ofd");
                    sysStorageConversionRepository.save(docStorageConversion);

                    MsgObj.SetMsgByName("STATUS", "保存成功!");                       //设置状态信息
                    //放入新增的文件id
                    MsgObj.SetMsgByName("storageId", String.valueOf(saveOfdFile.getId()));
                    MsgObj.MsgError("");                                              //清除错误信息
                }else {
                    MsgObj.MsgError("保存失败!");                                     //设置错误信息
                }
                MsgObj.MsgFileClear();                                              //清除文档内容
                MsgObj.Send(response);
                break;
            case "SAVEPDF":
                mRecordID = MsgObj.GetMsgByName("RECORDID");

                LocalStorage savePdfFile = addLocalStorage(MsgObj, "pdf");
                if(savePdfFile != null) {
                    // 保存清稿格式
                    formatService.convertToFormats(Long.valueOf(mRecordID), new FileConversionEnum[]{
                            FileConversionEnum.DOC, FileConversionEnum.TXT});

                    SysStorageConversion docStorageConversion = new SysStorageConversion();
                    docStorageConversion.setConversionStorageId(savePdfFile.getId());
                    docStorageConversion.setOriginalStorageId(Long.valueOf(mRecordID));
                    docStorageConversion.setConversionType("pdf");
                    sysStorageConversionRepository.save(docStorageConversion);

                    MsgObj.SetMsgByName("STATUS", "保存成功!");                       //设置状态信息
                    //放入新增的文件id
                    MsgObj.SetMsgByName("storageId", String.valueOf(savePdfFile.getId()));
                    MsgObj.MsgError("");                                              //清除错误信息
                }else {
                    MsgObj.MsgError("保存失败!");                                     //设置错误信息
                }
                MsgObj.MsgFileClear();                                              //清除文档内容
                MsgObj.Send(response);
                break;
            case "SAVEHTML":
                System.out.println("resut:" + "hello √");
                break;
            case "SAVEIMAGE":
                System.out.println("resut:" + "hello √");
                break;
            case "LOADTEMPLATE":
                String templateStorageId = MsgObj.GetMsgByName("TEMPLATE");//取得文档编号
                log.info("LOADTEMPLATE: " + templateStorageId);

                // 从服务器上根据文件ID调入文件
                localStorage = storageManageService.findById(Long.valueOf(templateStorageId));
                if(localStorage != null) {
                    File file = getFile(localStorageService, localStorage.getPath());
                    String path = file.getPath();
                    MsgObj.MsgFileLoad(path); //将文件信息打包
                    MsgObj.SetMsgByName("STATUS", "打开模板成功!");
                    MsgObj.MsgError(""); //清除错误信息
                }else {
                    log.error("LOADTEMPLATE: 打开文档失败 " + templateStorageId);
                    MsgObj.MsgError("打开失败!"); //设置错误信息
                }
                MsgObj.Send(response);
                break;
            case "SAVETEMPLATE":
                System.out.println("resut:" + "hello √");
                break;
            case "INSERTFILE":
                mRecordID = MsgObj.GetMsgByName("RECORDID"); // 取得文档编号
                log.info("INSERTFILE: " + mRecordID);
                localStorage = storageManageService.findById(Long.valueOf(mRecordID));
                if(localStorage != null) {
                    File file = getFile(localStorageService, localStorage.getPath());
                    String path = file.getPath();
                    MsgObj.MsgFileLoad(path); //将文件信息打包
                    MsgObj.SetMsgByName("POSITION", "正文"); // 设置插入的位置[书签]
                    MsgObj.SetMsgByName("STATUS", "插入文件成功!"); // 设置状态信息
                    MsgObj.MsgError(""); //清除错误信息
                }else {
                    MsgObj.MsgError("插入文件成功!"); // 设置错误信息
                }
                MsgObj.Send(response);
                break;
            case "LOADBOOKMARKS":
                System.out.println("resut:" + "hello √");
                break;
            case "GETFILE":
                System.out.println("resut:" + "hello √");
                break;
            case "PUTFILE":

                System.out.println("resut:" + "hello √");
                break;
            case "DELFILE":
                System.out.println("resut:" + "hello √");
                break;
            case "SENDMESSAGE":
                System.out.println("resut:" + "hello √");
                break;
            case "SAVEBOOKMARKS":
                System.out.println("resut:" + "hello √");
                break;
            case "INSERTIMAGE":
                System.out.println("resut:" + "hello √");
                break;
            default:
                System.out.println("无法识别的请求指令: " + mOption);
                break;
        }
    }

    private void writeBytes(byte[] bs, String url) throws IOException {
        OutputStream out = new FileOutputStream(url);
        InputStream is = new ByteArrayInputStream(bs);
        byte[] buff = new byte[1024];
        int len;
        while((len = is.read(buff)) != -1) {
            out.write(buff, 0, len);
        }
        is.close();
        out.close();
    }

    /**
     * 新增附件表
     * @param msgObj
     * @param suffix
     * @return
     * @throws IOException
     */
    @Deprecated
    private LocalStorage addLocalStorage(iMsgServer2000 msgObj, String suffix) throws IOException {
        String mFileName = msgObj.GetMsgByName("FILENAME");                        //取得文档名称
        // int mFileSize = msgObj.MsgFileSize();                                   //取得文档大小
        byte[] mFileBody = msgObj.MsgFileBody();
        String type = com.wangda.oa.utils.FileUtil.getFileType(suffix);

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddhhmmssS");
        String nowStr = "-" + format.format(new Date());
        String name = com.wangda.oa.utils.FileUtil.getFileNameNoEx(mFileName);
        if(name.contains("\\") || name.contains("/")) {
            String[] names = name.split("\\\\|/");
            name = names[names.length - 1];
        }
        String realName = name + nowStr + "." + suffix;
        String path = fileProperties.getPath().getPath() + type + File.separator + realName;

        // 文件夹若不存在，自动创建文件夹
        FileUtil.mkdir(fileProperties.getPath().getPath() + type);

        writeBytes(mFileBody, path);

        return localStorageService.create(mFileName, new File(path));
    }

    private File getFile(IStorageService service, String key) throws IOException {
        if(service instanceof LocalStorageServiceImpl) {
            return ((LocalStorageServiceImpl) service).getFile(key);
        }else {
            InputStream is = service.getInputStream(key);

            String basePath = fileProperties.getPath().getPath();
            File targetTempFile = new File(basePath + FileUtil.newRelativeFilePath("temp", "tmp"));
            FileUtils.copyInputStreamToFile(is, targetTempFile);
            return targetTempFile;
        }
    }

}
