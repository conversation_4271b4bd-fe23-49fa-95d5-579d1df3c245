<template>
  <iframe v-if="signUrl" :src="signUrl" frameborder="0" style="width:100%; height:100%;" />
  <el-empty v-else description="加载中" />
</template>

<script>
import browser from '../../utils/browser'
import tgSignApi from '@/api/tgsign'

export default {
  data() {
    return {
      originStorageId: null,
      signUrl: null,
      tempBasePath: null,
      tempSignFilePath: null,

      targetWindow: null,
      postMessageTargetOrigin: '*' // window.location
    }
  },

  computed: {
    signFileUrl: function() {
      if (this.$route.query.fileId) {
        return window.g.apiBaseUrl + '/api/localStorage/previewFile/' + this.$route.query.fileId
      }
      return null
    }
  },

  created() {
    this.originStorageId = this.$route.query.fileId
    this.targetWindow = window.opener
  },

  mounted() {
    this.handleSeal()
    // postMessage接收子页面参数
    window.addEventListener('message', this.onReceiveMessage, false)
  },

  methods: {
    // 初始化电子签章
    initSeal() {
      tgSignApi.init().then(res => {
        if (res.errorCode === 0) {
          var result = res.result
          var obj2 = JSON.parse(result)
          var errorMsg = obj2.errorMsg
          console.log('调用从初始化接口：' + errorMsg)
        } else {
          console.log('初始化接口调用失败：' + res.errorMsg)
          this.$message({ message: '初始化电子签章组件失败，请联系系统管理员。', type: 'error' })
        }
      }).catch((err) => {
        this.$message({ message: '初始化电子签章组件失败，请检查是否正确安装签章软件。', type: 'error' })
        console.log('初始化电子签章失败', err)
      })
    },
    handleSeal() {
      this.initSeal() // 接口调用前请先初始化

      if (this.signFileUrl) {
        tgSignApi.getSystemTempPath().then(data => {
          var errorCode = data.errorCode
          var errorMsg = data.errorMsg
          if (errorCode === 0) {
            this.tempBasePath = data.result
            this.tempBasePath = this.tempBasePath.replace(/\\/g, '/')// 转义路径中的'\'字符.
            console.log('获取系统临时目录路径：' + this.tempBasePath)
            if (browser.detectOS() !== 'Win') {
              this.tempBasePath = '/' + this.tempBasePath
              console.log('国产化环境下临时路径前面需要多加一个/，：' + this.tempBasePath)
            }

            // 打开签署页
            this.tempSignFilePath = this.tempBasePath + (new Date()).getTime() + '.pdf'
            const params = encodeURI('file=' + this.signFileUrl + '&destFile=' + this.tempSignFilePath)
            this.signUrl = 'https://localhost:7688/index.html?' + params
          } else {
            console.log('获取系统临时目录路径失败' + errorMsg)
          }
        })
      } else {
        this.$message({ message: '版式文件未生成，无法进行签章。', type: 'error' })
      }
    },
    // 文件上传到服务器
    uploadSignFile() {
      this.initSeal()

      // serverUrl, filePathSrc, fileForm, extraParam
      tgSignApi.uploadSignFile(this.tempSignFilePath, this.originStorageId).then(res => {
        if (res.errorCode === 0) {
          var result = res.result
          var obj2 = JSON.parse(result)
          var errorMsg = obj2.errorMsg
          console.log('上传签章文件文件：' + errorMsg)
          this.targetWindow.postMessage({ cmd: 'ON_SIGNED' }, this.postMessageTargetOrigin)
          alert('签章文件上传成功！')
          window.close()
        } else {
          console.log('调用上传接口失败：' + res.errorMsg)
          this.$message({ message: '签章文件上传失败，请重试。', type: 'error' })
        }
      }).catch((err) => {
        this.$message({ message: '签章文件上传失败。', type: 'error' })
        console.log('签章文件上传失败', err)
      })
    },
    onReceiveMessage(event) {
      // if (this.postMessageTargetOrigin !== '*' && event.origin !== this.popup.origin) return
      console.log('签章onReceiveMessage', event.data)
      if (event.data && !event.data.cmd && event.data === '签署成功') {
        this.uploadSignFile()
      }
    }
  }
}
</script>
