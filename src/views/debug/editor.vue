<template>
  <div style="width:100%; height:100%">
    <el-row>
      <el-col :span="24" class="btnBox">
        <el-button type="primary" size="medium" @click="doSave()">保存</el-button>
      </el-col>
    </el-row>
    <div class="wpsOnlineBox">
      没有可以支持的控件，仅能进行模拟。
    </div>
  </div>
</template>

<script>

export default {
  name: 'App',
  methods: {
    doSave() {
      this.wps.save().then(result => {
        console.log('文件保存成功：', result)
        if (window.opener) {
          window.opener.postMessage(result, window.location)
        } else {
          alert('回调通知父页面失败，返回值：' + JSON.stringify(result))
        }
      })
    }
  }
}
</script>

<style lang="scss">
.btnBox{
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.wpsOnlineBox{
  height: calc(100% - 40px);
}
</style>
