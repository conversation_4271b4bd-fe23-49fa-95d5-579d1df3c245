import browser from '../../utils/browser'

export default {
  iWebOffice: {
    copyright: window.g.iWebOfficeCopyright,
    copyrightZZKK: window.g.iWebOfficeCopyrightZZKK
  },

  // 初始化金格控件
  initOcxTemplate() {
    var browserMatch = browser.detectOSBrowser()

    var str = ''
    str += '<object id="iWebOffice2015" '

    str += ' width="100%"'
    str += ' height="100%"'

    if (browserMatch.platform === 'Linux') {
      str += ' type="application/iweboffice">'
      str += '<param name="Copyright" value="' + this.iWebOffice.copyrightZZKK + '">'
    } else if (browserMatch.platform === 'Win32') {
      str += ' CLASSID="CLSID:D89F482C-5045-4DB5-8C53-D2C9EE71D025"  codebase="./iWebOffice/iWebOffice2015.cab#version=0,0,0,0"'
      str += '>'
      str += '<param name="Copyright" value="' + this.iWebOffice.copyright + '">'
    } else if (browserMatch.platform === 'Win64') {
      str += ' CLASSID="CLSID:D89F482C-5045-4DB5-8C53-D2C9EE71D024"  codebase="./iWebOffice/iWebOffice2015.cab#version=0,0,0,0"'
      str += '>'
      str += '<param name="Copyright" value="' + this.iWebOffice.copyright + '">'
    } else if (browserMatch.browser === 'chrome') {
      str += ' clsid="CLSID:D89F482C-5045-4DB5-8C53-D2C9EE71D025"'
      str += ' type="application/kg-plugin"'
      str += ' OnReady="OnReady"'
      str += ' OnCommand="OnCommand"'
      str += ' OnRightClickedWhenAnnotate="OnRightClickedWhenAnnotate"'
      str += ' OnSending="OnSending"'
      str += ' OnSendEnd="OnSendEnd"'
      str += ' OnRecvStart="OnRecvStart"'
      str += ' OnRecving="OnRecving"'
      str += ' OnRecvEnd="OnRecvEnd"'
      str += ' OnFullSizeBefore="OnFullSizeBefore"'
      str += ' OnFullSizeAfter="OnFullSizeAfter"'
      str += ' Copyright="' + this.iWebOffice.copyright + '"'
      str += '>'
    } else if (browserMatch.browser === 'firefox') {
      str += ' clsid="CLSID:D89F482C-5045-4DB5-8C53-D2C9EE71D025"'
      str += ' type="application/kg-activex"'
      str += ' OnCommand="OnCommand"'
      str += ' OnReady="OnReady"'
      str += ' OnOLECommand="OnOLECommand"'
      str += ' OnExecuteScripted="OnExecuteScripted"'
      str += ' OnQuit="OnQuit"'
      str += ' OnSendStart="OnSendStart"'
      str += ' OnSending="OnSending"'
      str += ' OnSendEnd="OnSendEnd"'
      str += ' OnRecvStart="OnRecvStart"'
      str += ' OnRecving="OnRecving"'
      str += ' OnRecvEnd="OnRecvEnd"'
      str += ' OnRightClickedWhenAnnotate="OnRightClickedWhenAnnotate"'
      str += ' OnFullSizeBefore="OnFullSizeBefore"'
      str += ' OnFullSizeAfter="OnFullSizeAfter"'
      str += ' Copyright="' + this.iWebOffice.copyright + '"'
      str += '>'
    }
    str += '<div style="margin: 120px 80px; display: block; text-align:center"><a href="' + window.g.pluginInstallPageUrl + '" style="font-size:22px">浏览器未能加载插件，请点击下载相应插件进行安装。</a></div>'
    str += '</object>'
    return str
  }
}
