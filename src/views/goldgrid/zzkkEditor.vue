<template>
  <div style="width:100%; height:100%">
    <el-empty v-if="fileLock.status" :image-size="400" class="empty" :description="fileLock.message" :image="require('@/assets/images/file.png')" />
    <template v-else>
      <el-row class="toolbarWrapper">
        <el-col :span="12" class="btnBox">
          <el-button type="primary" size="small" @click="doSave()">保存</el-button>
          <el-button type="primary" size="small" @click="doSave({autoCloseFlag: true})">保存并关闭</el-button>

          <el-button size="small" @click="wrapHeader()">套红</el-button>
          <el-button size="small" @click="doPrint()">打印</el-button>
          <el-button v-if="revisionStatus === 2" size="small" @click="toggleRevisionStatus(0)">显示修订痕迹</el-button>
          <el-button v-if="revisionStatus === 0" size="small" @click="toggleRevisionStatus(2)">隐藏修订痕迹</el-button>
          <el-button v-if="formatConvertByOcxSuccess" size="small" @click="handlePreviewPdf()">预览版式文件</el-button>
        </el-col>
        <el-col :span="12" class="message">
          <div v-if="formatConvertByOcx">保存时将会同时保存PDF版式文件，请注意检查文件排版。</div>
        </el-col>
      </el-row>
      <div class="iWebOfficeBox" v-html="ocxTemplate" />
    </template>
    <el-dialog
      title="选择红头模板"
      :visible.sync="wrapHeaderDialogVisible"
      width="460px"
    >
      <div>
        <el-radio-group v-model="headerTemplateFileId" class="redTemplate">
          <el-radio v-for="item in headerTemplateList" :key="item.storageId" :label="item.storageId">{{ item.templateName }}</el-radio>
        </el-radio-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="wrapHeaderDialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="headerTemplateFileId===0" @click="handleWrapHeader">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
const iWebOffice = {
  serverUrl: window.g.apiBaseUrl + '/api/iweboffice/officeServer'
}

import objectTemplate from './objectTemplate'
import officeTemplateApi from '@/api/officeTemplate'
import { getInfo } from '@/api/userInfo'
import sysFileLockMixins from '@/mixins/sysFileLock'

export default {
  mixins: [sysFileLockMixins],
  data() {
    return {
      fileId: null,
      formatConvertByOcx: false,
      formatConvertByOcxSuccess: false,
      ocxTemplate: objectTemplate.initOcxTemplate(),
      iWebOfficeObj: null,
      revisionStatus: 2, // 0：显示修订状态; 1：显示原始状态（修订前）; 2：显示最终状态（修订后）

      wrapHeaderDialogVisible: false,
      headerTemplateFileId: 0,
      headerTemplateList: [],

      currentUsername: null,

      targetWindow: null,
      postMessageTargetOrigin: '*' // window.location
    }
  },
  created() {
    this.fileId = this.$route.query.fileId
    this.formatConvertByOcx = this.$route.query.formatConvertByOcx === 'true'
    this.targetWindow = window.opener ? window.opener : window.parent // 兼容iframe和window.open两种方式调用
  },
  mounted() {
    this.$nextTick(function() {
      getInfo().then(res => {
        this.currentUsername = res.user.nickName
        this.initWebOfficeObject()
      })
    })
  },
  destroyed() {
  },
  methods: {
    initWebOfficeObject() {
      this.iWebOfficeObj = document.getElementById('iWebOffice2015')
      this.iWebOfficeObj.setPluginType(1)

      console.log('服务端地址', iWebOffice.serverUrl)
      this.iWebOfficeObj.WebUrl = iWebOffice.serverUrl
      this.iWebOfficeObj.ShowToolBars = 0
      this.iWebOfficeObj.ShowMenuBar = false
      this.iWebOfficeObj.MaxFileSize = 20 * 1024 // 20M

      this.iWebOfficeObj.Template = '' + this.headerTemplateFileId // Template:模板编号
      this.iWebOfficeObj.FileName = '文件' + (new Date().getTime()) // FileName:文档名称
      this.iWebOfficeObj.FileType = '.docx' // FileType:文档类型  .doc  .docx .xls  .wps

      this.iWebOfficeObj.UserName = this.currentUsername // UserName:操作用户名，痕迹保留需要
      this.iWebOfficeObj.EditType = '2,0'

      if (this.fileId) {
        this.loadWebFile()
      } else {
        this.createNew()
      }

      this.iWebOfficeObj.setToolbarAllVisible(true)
      // 作用：开启修订功能，编辑文档并隐藏痕迹
      this.iWebOfficeObj.WebTrackAndHideRevisions()
    },

    // 创建新文件
    createNew() {
      this.iWebOfficeObj.CreateFile()
    },

    // 打开服务端文件
    loadWebFile() {
      console.log('加载文件', this.fileId)
      this.iWebOfficeObj.RecordID = '' + this.fileId

      // this.iWebOfficeObj.Template = '1' // Template:模板编号
      // alert(WebOffice.Template);
      this.iWebOfficeObj.FileName = (new Date().getTime()) + '.doc' // FileName:文档名称

      // alert(WebOffice.FileName);
      this.iWebOfficeObj.FileType = '.doc' // FileType:文档类型  .doc  .xls  .wps
      // alert(WebOffice.FileType);

      if (!this.iWebOfficeObj.WebOpen()) {
        alert('文件打开失败')
        console.log('文件打开失败', this.iWebOfficeObj.Status)
      }
    },

    async doSave(options) {
      // this.iWebOfficeObj.WebUrl = iWebOffice.serverUrl
      // alert(WebOffice.WebUrl);
      // this.iWebOfficeObj.RecordID = '123456' // RecordID:本文档记录编号
      this.iWebOfficeObj.WebClearMessage()
      this.iWebOfficeObj.WebSetMsgByName('FormData', '正文')
      // var ret = this.iWebOfficeObj.WebSave()
      // alert(ret)
      //
      if (this.iWebOfficeObj.WebSave()) {
        this.fileId = this.iWebOfficeObj.WebGetMsgByName('storageId')
        this.iWebOfficeObj.RecordID = this.fileId
        console.log('文件保存成功：', this.fileId)

        // 前端控件来转化PDF和OFD
        if (this.formatConvertByOcx) {
          this.iWebOfficeObj.WebSaveOFD()
          this.iWebOfficeObj.WebSavePDF()
          this.formatConvertByOcxSuccess = true
        }

        const routeQuery = { ...this.$route.query }
        routeQuery.fileId = this.fileId
        this.$router.replace({ path: this.$route.path, query: routeQuery })

        if (this.targetWindow) {
          this.targetWindow.postMessage({ cmd: 'ON_SAVED', body: { id: this.fileId }}, this.postMessageTargetOrigin)
          this.$message('文件保存成功')
          if (options && options.autoCloseFlag) {
            this.targetWindow.postMessage({ cmd: 'ON_CLOSE' }, this.postMessageTargetOrigin)
            window.close()
          }
        } else {
          this.$message.error('回调通知父页面失败')
        }
      }
    },

    toggleRevisionStatus(newRevisionStatus) {
      this.revisionStatus = newRevisionStatus
      this.iWebOfficeObj.showRevision(this.revisionStatus)
    },

    doPrint() {
      this.iWebOfficeObj.WebOpenPrint()
    },

    wrapHeader() {
      officeTemplateApi.list({}).then(res => { this.headerTemplateList = res.content })
      this.wrapHeaderDialogVisible = true
    },
    handleWrapHeader() {
      this.doSave()

      this.iWebOfficeObj.Template = '' + this.headerTemplateFileId// Template:模板编号
      this.iWebOfficeObj.CloseDocument()
      // 关闭修订
      this.iWebOfficeObj.EditType = '1,1'
      if (this.iWebOfficeObj.WebLoadTemplate()) {
        var ret = this.iWebOfficeObj.WebInsertFile()
        if (ret) {
          console.log('套红完成')
          // var app = this.iWebOfficeObj.KGGetObject('Application')
          // app.ShowDocumentFieldTarget = false
        }
      } else {
        alert('加载红头模板失败')
      }
      // 重新打开修订
      this.iWebOfficeObj.EditType = '2,0'
      this.iWebOfficeObj.WebTrackAndHideRevisions()

      this.wrapHeaderDialogVisible = false
    },
    handlePreviewPdf() {
      window.open('#/reader?fileId=' + this.fileId + '&conversion=true')
    }
  }
}
</script>

<style lang="scss">
.btnBox{
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.iWebOfficeBox{
  height: calc(100% - 40px);
}
.redTemplate .el-radio{
  display: block;
  margin-bottom: 10px;
}
.toolbarWrapper{
  display: flex;
}
.message{
    display: flex;
    align-items: center;
    justify-content: flex-end;
  &>div{
    text-align: right;
    font-size: 12px;
    color: #E6A23C;
  }
}
.empty{
  width: 100%;
  height: 100%;
}

</style>
