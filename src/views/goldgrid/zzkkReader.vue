<template>
  <div style="width:100%; height:100%">
    <div class="iWebOfficeBox" v-html="ocxTemplate" />
  </div>
</template>

<script>
const iWebOffice = {
  serverUrl: window.g.apiBaseUrl + '/api/iweboffice/officeServer'
}

import objectTemplate from './objectTemplate'

export default {
  data() {
    return {
      ocxTemplate: objectTemplate.initOcxTemplate(),
      iWebOfficeObj: null,
      fileId: null,
      targetWindow: null,
      postMessageTargetOrigin: '*' // window.location
    }
  },
  created() {
    this.fileId = this.$route.query.fileId
    this.targetWindow = window.opener ? window.opener : window.parent // 兼容iframe和window.open两种方式调用
  },
  mounted() {
    this.$nextTick(function() {
      this.initWebOfficeObject()
    })
  },
  methods: {
    initWebOfficeObject() {
      this.iWebOfficeObj = document.getElementById('iWebOffice2015')
      this.iWebOfficeObj.setPluginType(1)

      console.log('服务端地址', iWebOffice.serverUrl)
      this.iWebOfficeObj.WebUrl = iWebOffice.serverUrl
      this.iWebOfficeObj.ShowToolBars = 2
      this.iWebOfficeObj.ShowMenuBar = false
      this.iWebOfficeObj.MaxFileSize = 20 * 1024 // 20M

      this.iWebOfficeObj.Template = '1' // Template:模板编号
      this.iWebOfficeObj.FileName = (new Date().getTime()) + '.doc' // FileName:文档名称
      this.iWebOfficeObj.FileType = '.docx' // FileType:文档类型  .doc  .docx .xls  .wps

      // this.iWebOfficeObj.UserName = 'User' // UserName:操作用户名，痕迹保留需要
      this.iWebOfficeObj.EditType = '0,0'

      if (this.fileId) {
        this.loadWebFile()
      } else {
        this.createNew()
      }

      // 作用：开启修订功能，编辑文档并隐藏痕迹
      this.iWebOfficeObj.WebTrackAndHideRevisions()
    },

    // 创建新文件
    createNew() {
      this.iWebOfficeObj.CreateFile()
    },

    // 打开服务端文件
    loadWebFile() {
      console.log('加载文件', this.fileId)
      this.iWebOfficeObj.RecordID = this.fileId

      // this.iWebOfficeObj.Template = '1' // Template:模板编号
      // alert(WebOffice.Template);
      this.iWebOfficeObj.FileName = (new Date().getTime()) + '.doc' // FileName:文档名称

      // alert(WebOffice.FileName);
      this.iWebOfficeObj.FileType = '.doc' // FileType:文档类型  .doc  .xls  .wps
      // alert(WebOffice.FileType);

      if (!this.iWebOfficeObj.WebOpen()) {
        alert('文件打开失败')
        console.log('文件打开失败', this.iWebOfficeObj.Status)
      }
    }
  }
}
</script>

<style lang="scss">
.iWebOfficeBox{
  height: calc(100%);
}
</style>
