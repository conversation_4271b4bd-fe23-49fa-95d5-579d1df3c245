<template>
  <div style="width:100%; height:100%">
    <el-empty v-if="fileLock.status" :image-size="400" class="empty" :description="fileLock.message" :image="require('@/assets/images/file.png')" />
    <template v-else>
      <el-row class="toolbarWrapper ">
        <el-col :span="12" class="btnBox">
          <el-button type="primary" size="small" :loading="loading" @click="doSave()">{{ loading?'保存中':'保存' }}</el-button>
          <el-button type="primary" size="small" :loading="loading" @click="doSave({autoCloseFlag: true})">{{ loading?'保存中':'保存并关闭' }}</el-button>

          <el-button size="small" @click="wrapHeader()">套红</el-button>
          <el-button size="small" @click="doPrint()">打印</el-button>
          <el-button v-if="revisionStatus === 2" size="small" @click="toggleRevisionStatus(0)">显示修订痕迹</el-button>
          <el-button v-if="revisionStatus === 0" size="small" @click="toggleRevisionStatus(2)">隐藏修订痕迹</el-button>
          <el-button v-if="formatConvertByOcxSuccess" size="small" @click="handlePreviewPdf()">预览版式文件</el-button>
          <el-button size="small" :loading="loading" @click="doClose()">返回</el-button>
        </el-col>
        <el-col :span="12" class="message">
          <div v-if="formatConvertByOcx">保存时将会同时保存PDF版式文件，请注意检查文件排版。</div>
        </el-col>
      </el-row>
      <el-row class="toolbarWrapper fixedBottom">
        <el-col :span="12" class="btnBox">
          <el-button type="primary" size="small" :loading="loading" @click="doSave()">{{ loading?'保存中':'保存' }}</el-button>
          <el-button type="primary" size="small" :loading="loading" @click="doSave({autoCloseFlag: true})">{{ loading?'保存中':'保存并关闭' }}</el-button>

          <el-button size="small" @click="wrapHeader()">套红</el-button>
          <el-button size="small" @click="doPrint()">打印</el-button>
          <el-button v-if="revisionStatus === 2" size="small" @click="toggleRevisionStatus(0)">显示修订痕迹</el-button>
          <el-button v-if="revisionStatus === 0" size="small" @click="toggleRevisionStatus(2)">隐藏修订痕迹</el-button>
          <el-button v-if="formatConvertByOcxSuccess" size="small" @click="handlePreviewPdf()">预览版式文件</el-button>
          <el-button size="small" :loading="loading" @click="doClose()">返回</el-button>
        </el-col>
        <el-col :span="12" class="message">
          <div v-if="formatConvertByOcx">保存时将会同时保存PDF版式文件，请注意检查文件排版。</div>
        </el-col>
      </el-row>
      <div v-show="iWebOfficeBoxShow" class="iWebOfficeBox" v-html="ocxTemplate" />
    </template>
    <el-dialog
      title="选择红头模板"
      :visible.sync="wrapHeaderDialogVisible"
      width="460px"
      @open="iWebOfficeBoxShow = false"
      @close="iWebOfficeBoxShow = true"
    >
      <div>
        <el-radio-group v-model="headerTemplateFileId" class="redTemplate">
          <el-radio v-for="item in headerTemplateList" :key="item.storageId" :label="item.storageId">{{ item.templateName }}</el-radio>
        </el-radio-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="wrapHeaderDialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="headerTemplateFileId===0" @click="handleWrapHeader">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import browser from '../../utils/browser'
import objectTemplate from './objectTemplate'
// import webOffice2015 from './webOffice2015'
import officeTemplateApi from '@/api/officeTemplate'
import { getInfo } from '@/api/userInfo'
import sysFileLockMixins from '@/mixins/sysFileLock'

const iWebOffice = {
  serverUrl: window.g.apiBaseUrl + '/api/iweboffice/officeServer',
  downPath: '\\iWebOffice2015\\down\\',
  upPath: '\\iWebOffice2015\\up\\'
}
// const WebOffice = new webOffice2015.WebOffice2015()

export default {
  mixins: [sysFileLockMixins],
  data() {
    return {
      fileId: null,
      formatConvertByOcx: false,
      formatConvertByOcxSuccess: false,
      ocxTemplate: objectTemplate.initOcxTemplate(),
      iWebOfficeObj: null,
      iWebOfficeBoxShow: true,
      revisionStatus: 2, // 0：显示修订状态; 1：显示原始状态（修订前）; 2：显示最终状态（修订后）

      wrapHeaderDialogVisible: false,
      headerTemplateFileId: 0,
      headerTemplateList: [],

      targetWindow: null,
      postMessageTargetOrigin: '*', // window.location
      loading: false
    }
  },
  created() {
    this.fileId = this.$route.query.fileId
    this.formatConvertByOcx = this.$route.query.formatConvertByOcx === 'true'
    this.targetWindow = window.opener ? window.opener : window.parent // 兼容iframe和window.open两种方式调用
  },
  mounted() {
    this.$nextTick(function() {
      getInfo().then(res => {
        this.currentUsername = res.user.nickName
        // WebOffice.setObj(document.getElementById('iWebOffice2015'))
      }).catch((err) => {
        console.log('getInfo错误', err)
        alert('用户会话失效，请重新登录。')
      })
      this.initWebOfficeObject()
    })
  },
  destroyed() {
  },
  beforeDestroy() {
    if (browser.detectOSBrowser().browser !== 'IE') {
      this.iWebOfficeObj.setPluginType(0)
    }
    if (navigator.userAgent.indexOf('Firefox') === -1) { // 火狐不能使用close方法
      this.iWebOfficeObj.Close()
    }
  },
  methods: {
    initWebOfficeObject() {
      /*
      WebOffice.WebUrl = iWebOffice.serverUrl // WebUrl:系统服务器路径，与服务器文件交互操作，如保存、打开文档，重要文件
      WebOffice.RecordID = '2' // RecordID:本文档记录编号
      WebOffice.FileName = '1638516949454.doc' // FileName:文档名称
      WebOffice.FileType = '.doc' // FileType:文档类型  .doc  .xls
      WebOffice.UserName = '体验用户57' // UserName:操作用户名，痕迹保留需要

      //   WebOffice.ShowTitleBar(false); //隐藏标题栏
      WebOffice.ShowMenuBar(true) // 隐藏工具栏菜单
      // WebOffice.ShowCustomToolbar(false); //隐藏手写签批工具栏
      WebOffice.ShowToolBars(true) // Office工具栏
      WebOffice.HookEnabled()
      WebOffice.SetCaption()
      WebOffice.SetUser('体验用户57')
      if (WebOffice.WebOpen()) { // 打开该文档    交互OfficeServer  调出文档OPTION="LOADFILE"
        WebOffice.setEditType('2') // EditType:编辑类型  方式一   WebOpen之后
        WebOffice.VBASetUserName(WebOffice.UserName) // 设置用户名
        // getEditVersion();//判断是否是永中office
        //    WebOffice.AddToolbar();//打开文档时显示手写签批工具栏
        //    WebOffice.ShowCustomToolbar(false);//隐藏手写签批工具栏
        // StatusMsg(WebOffice.Status)
      }
      */

      this.iWebOfficeObj = document.getElementById('iWebOffice2015')
      this.iWebOfficeObj.HookEnabled = browser.detectOSBrowser().browser === 'IE' // 仅IE支持，解决控件中的文档与本地启动的文档焦点切换问题，其他必须设置成false
      this.iWebOfficeObj.Style.ShowToolBars = true
      this.iWebOfficeObj.Style.ShowMenuBar = false

      if (this.loadWebFileOrCeateNew()) {
        console.log('当前操作人', this.currentUsername)
        this.iWebOfficeObj.ActiveDocument.Application.UserName = this.currentUsername
      }
    },

    loadWebFileOrCeateNew() {
      if (this.fileId) {
        if (this.loadWebFile(this.fileId)) {
          // 启用修订
          this.toggleRevisionStatus(2)
          return true
        }
      } else {
        if (this.createNew()) {
          // 不启用修订
          this.toggleRevisionStatus(1)
          return true
        }
      }
      return false
    },

    // 创建新文件
    createNew() {
      var ret = this.iWebOfficeObj.CreateNew('Word.Document')
      return ret === 0
    },

    // 打开服务端文件
    loadWebFile(storageId) {
      var httpclient = this.iWebOfficeObj.Http
      httpclient.Clear()
      httpclient.AddForm('storageId', '' + storageId ? storageId : this.fileId)

      httpclient.ShowProgressUI = false // 隐藏下载进度条
      if (httpclient.Open(1, iWebOffice.serverUrl + '/loadFile', false)) { // true 异步方式 false同步
        if (httpclient.Send()) {
          // 设置临时路径
          var fs = this.iWebOfficeObj.FileSystem // 获取file对象
          var dirPath = fs.GetSpecialFolderPath(0x1a) + iWebOffice.downPath
          var filePath = dirPath + new Date().getTime() + '.doc'
          fs.CreateDirectory(dirPath) // 创建生成指定目录
          if (httpclient.Status === 200) {
            httpclient.ResponseSaveToFile(filePath)
            httpclient.Clear()
            var mSaveResult = this.iWebOfficeObj.Open(filePath)
            console.log('打开文件', mSaveResult)
            return mSaveResult === 0
          }
        }
      }
    },

    async doSave(options) {
      this.loading = true
      var httpclient = this.iWebOfficeObj.Http
      var fs = this.iWebOfficeObj.FileSystem // 获取file对象
      var dirPath = fs.GetSpecialFolderPath(0x1a) + iWebOffice.upPath
      var filePath = dirPath + new Date().getTime() + '.doc'
      fs.CreateDirectory(dirPath) // 创建生成指定目录
      this.iWebOfficeObj.Save(filePath, null, true)

      httpclient.Clear()
      httpclient.AddForm('name', '文件' + (new Date().getTime()))
      httpclient.AddFile('fileData', filePath) // 需要上传的文件
      // httpclient.ShowProgressUI = this.ShowWindow; // 隐藏进度条
      // this.WebUrl = this.ServerUrl + this.SaveServlet;
      if (httpclient.Open(1, iWebOffice.serverUrl + '/saveFile', false)) { // true 异步方式 false同步
        if (httpclient.Send()) {
          if (httpclient.Status === 200) {
            var data = httpclient.ResponseText
            if (typeof data === 'string') {
              data = JSON.parse(data)
              this.fileId = data.storageId
              console.log('文件保存成功', this.fileId)

              if (this.formatConvertByOcx) {
                this.WebSavePDF()
                this.formatConvertByOcxSuccess = true
              }
            }
            if (this.fileId) {
              if (this.targetWindow) {
                this.targetWindow.postMessage({ cmd: 'ON_SAVED', body: { id: this.fileId }}, this.postMessageTargetOrigin)
                this.$message('文件保存成功')
                this.loading = false
                if (options && options.autoCloseFlag) {
                  this.targetWindow.postMessage({ cmd: 'ON_CLOSE' }, this.postMessageTargetOrigin)
                  this.beforeunloadHandler()
                  window.close()
                }
              } else {
                this.$message.error('回调通知父页面失败')
                this.loading = false
              }
            } else {
              this.$message({
                message: '抱歉，保存文档失败请重新尝试保存！',
                type: 'warning'
              })
              this.loading = false
            }
          }
        }
      }
    },

    toggleRevisionStatus(newRevisionStatus) {
      this.revisionStatus = newRevisionStatus

      this.iWebOfficeObj.ActiveDocument.TrackRevisions = this.revisionStatus !== 1 // 是否修订
      this.iWebOfficeObj.ActiveDocument.ShowRevisions = this.revisionStatus === 0 // 显示痕迹或隐藏
    },

    doPrint() {
      this.iWebOfficeObj.ShowDialog(4)
    },

    wrapHeader() {
      officeTemplateApi.deptTemplist({}).then(res => { this.headerTemplateList = res.content })
      this.wrapHeaderDialogVisible = true
    },
    handleWrapHeader() {
      setTimeout(() => {
        // this.doSave()
        var fs = this.iWebOfficeObj.FileSystem // 获取file对象
        var dirPath = fs.GetSpecialFolderPath(0x1a) + iWebOffice.upPath
        var tempFilePath = dirPath + new Date().getTime() + '.doc'
        fs.CreateDirectory(dirPath) // 创建生成指定目录
        this.iWebOfficeObj.Save(tempFilePath, null, true)
        console.log('保存文件到本地', tempFilePath)

        this.iWebOfficeObj.Template = '' + this.headerTemplateFileId // Template:模板编号

        // 关闭修订
        // this.iWebOfficeObj.EditType = '1,1'
        // if (this.iWebOfficeObj.WebLoadTemplate()) {

        if (navigator.userAgent.indexOf('Firefox') === -1) { // 火狐不能使用close方法
          // this.iWebOfficeObj.Close()
        }

        if (this.loadWebFile(this.iWebOfficeObj.Template)) {
          // var ret = this.iWebOfficeObj.WebInsertFile()
          var ret = this.VBAInsertFile('正文', tempFilePath)
          if (ret) {
            console.log('套红完成')
          // var app = this.iWebOfficeObj.KGGetObject('Application')
          // app.ShowDocumentFieldTarget = false
          }
        } else {
          alert('加载红头模板失败')
        }
        // 重新打开修订
        // this.iWebOfficeObj.EditType = '2,0'
        // this.iWebOfficeObj.WebTrackAndHideRevisions()
      }, 0)

      this.wrapHeaderDialogVisible = false
    },
    VBAInsertFile(Position, FileName) {
      try {
        this.iWebOfficeObj.ActiveDocument.Application.Selection.GoTo(-1, 0, 0, Position)
        this.iWebOfficeObj.Activate(true)
        this.iWebOfficeObj.ActiveDocument.Application.Selection.InsertFile(FileName, '', false, false, false)
        return true
      } catch (e) {
        alert(e)
        return false
      }
    },
    // 保存为PDF
    WebSavePDF() {
      var fs = this.iWebOfficeObj.FileSystem // 获取file对象
      var dirPath = fs.GetSpecialFolderPath(0x1a) + iWebOffice.upPath
      var filePath = dirPath + new Date().getTime() + '.pdf'
      fs.CreateDirectory(dirPath) // 创建生成指定目录

      // True  中间的0不包含书签 1转换之后会包含书签
      this.iWebOfficeObj.ActiveDocument.ExportAsFixedFormat(filePath, 17, false, 0, 0, 1, 1, 0, true, true, 1, true, true, true)

      var httpclient = this.iWebOfficeObj.Http // 设置http对象
      httpclient.Clear()
      httpclient.AddForm('name', 'PDF-' + this.fileId + '.pdf')
      httpclient.AddForm('originalStorageId', '' + this.fileId)
      httpclient.AddFile('fileData', filePath)

      if (httpclient.Open(1, iWebOffice.serverUrl + '/savePdf', false)) { // true 异步方式 false同步
        if (httpclient.Send()) {
          if (httpclient.Status === 200) {
            var data = httpclient.ResponseText
            if (typeof data === 'string') {
              console.log('PDF文件保存成功', data)
              return true
            }
          }
        }
      }

      return false
    },
    handlePreviewPdf() {
      window.open('#/reader?fileId=' + this.fileId + '&conversion=true')
    },

    async doClose() {
      this.targetWindow.postMessage({ cmd: 'ON_CLOSE' }, this.postMessageTargetOrigin)
      this.beforeunloadHandler()
      window.close()
    }
  }
}
</script>

<style lang="scss">
.btnBox{
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.iWebOfficeBox{
  height: calc(100% - 40px);
  overflow: hidden;
}
.redTemplate .el-radio{
  display: block;
  margin-bottom: 10px;
}
.toolbarWrapper{
  display: flex;
}
.message{
    display: flex;
    align-items: center;
    justify-content: flex-end;
  &>div{
    text-align: right;
    font-size: 12px;
    color: #E6A23C;
  }
}
.empty{
  width: 100%;
  height: 100%;
}
.fixedBottom{
  position: fixed;
  bottom: 0;
}
</style>
