<template>
  <div style="width:100%; height:100%">
    <div class="iWebOfficeBox" v-html="ocxTemplate" />
  </div>
</template>

<script>
const iWebOffice = {
  serverUrl: window.g.apiBaseUrl + '/api/iweboffice/officeServer',
  downPath: '\\iWebOffice2015\\down\\',
  upPath: '\\iWebOffice2015\\up\\'
}

import browser from '../../utils/browser'
import objectTemplate from './objectTemplate'

export default {
  data() {
    return {
      ocxTemplate: objectTemplate.initOcxTemplate(),
      iWebOfficeObj: null,
      fileId: null,
      targetWindow: null,
      postMessageTargetOrigin: '*' // window.location
    }
  },
  created() {
    this.fileId = this.$route.query.fileId
    this.targetWindow = window.opener ? window.opener : window.parent // 兼容iframe和window.open两种方式调用
  },
  mounted() {
    this.$nextTick(function() {
      this.initWebOfficeObject()
    })
  },
  methods: {
    initWebOfficeObject() {
      this.iWebOfficeObj = document.getElementById('iWebOffice2015')
      this.iWebOfficeObj.HookEnabled = browser.detectOSBrowser().browser === 'IE' // 仅IE支持，解决控件中的文档与本地启动的文档焦点切换问题，其他必须设置成false
      this.iWebOfficeObj.Style.ShowToolBars = false
      this.iWebOfficeObj.Style.ShowMenuBar = false
      // this.iWebOfficeObj.ActiveDocument.TrackRevisions = false // 显示标记和隐藏标记
      // this.iWebOfficeObj.ActiveDocument.ShowRevisions = false // 显示痕迹或隐藏

      if (this.fileId) {
        this.loadWebFile()
      } else {
        alert('文件不存在')
      }
    },

    // 打开服务端文件
    loadWebFile() {
      var httpclient = this.iWebOfficeObj.Http
      httpclient.Clear()
      httpclient.AddForm('storageId', '' + this.fileId)

      httpclient.ShowProgressUI = false // 隐藏下载进度条
      if (httpclient.Open(1, iWebOffice.serverUrl + '/loadFile', false)) { // true 异步方式 false同步
        if (httpclient.Send()) {
          // 设置临时路径
          var fs = this.iWebOfficeObj.FileSystem // 获取file对象
          var dirPath = fs.GetSpecialFolderPath(0x1a) + iWebOffice.downPath
          var filePath = dirPath + new Date().getTime() + '.doc'
          fs.CreateDirectory(dirPath) // 创建生成指定目录
          if (httpclient.Status === 200) {
            httpclient.ResponseSaveToFile(filePath)
            httpclient.Clear()
            var mSaveResult = this.iWebOfficeObj.Open(filePath)
            console.log(mSaveResult)
          }
        }
      }
    }
  }
}
</script>

<style lang="scss">
.btnBox{
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.iWebOfficeBox{
  height: 100%;
  overflow: hidden;
}
</style>
