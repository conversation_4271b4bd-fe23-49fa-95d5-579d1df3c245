<template>
  <div style="width:100%; height:100%">
    <div class="wpsOnlineBox">
      <div id="wpsHistory" style="width: 100%; height:100%" />
    </div>
    <div class="historyBox">
      <template>
        <el-table v-loading="historyListDataLoading" :data="historyListData" style="width: 100%">
          <el-table-column fixed prop="modify_time" label="时间" width="180" />
          <el-table-column prop="modifier.name" label="编辑人" width="120" />
          <el-table-column label="查看" width="50">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleViewVersion(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </div>
  </div>
</template>

<script>
import WebOfficeSDK from '@/../lib/jwps/web-office-sdk.es'
import wpsOnlineApi from '@/api/wpsOnline'
import onlineEditApi from '@/api/docconvert'
import moment from 'moment'

export default {
  name: 'App',
  components: { },
  data() {
    return {
      wps: null,
      fileId: 0,
      jwpsUrl: '',
      jtoken: '',
      historyListData: [],
      historyListDataLoading: true
    }
  },
  created() {
    if (!this.$route.query.fileId) {
      this.$message.error('非法参数')
      return
    }
    this.fileId = this.$route.query.fileId

    this.getWpsUrlAndOpen()
    wpsOnlineApi.fileHistories({ fileId: this.$route.query.fileId }).then(res => {
      this.historyListData = res.histories
      this.historyListData.forEach((item) => {
        item.modify_time = moment.unix(item.modify_time).format('YYYY-MM-DD HH:mm:ss')
      })

      this.historyListDataLoading = false
    })
  },
  methods: {
    getWpsUrlAndOpen() {
      onlineEditApi.fileConversionInfo({ 'storageId': this.fileId }).then(res => {
        let viewFileId = this.fileId
        if (res.conversionStorage && res.conversionStorage.doc) {
          viewFileId = res.conversionStorage.doc.id
        }
        wpsOnlineApi.getWpsUrlByFileId({ fileId: viewFileId, permission: 'read' }).then(res => {
          this.jwpsUrl = res.url
          this.jtoken = res.token
          this.openWps(this.jwpsUrl, this.jtoken)
        })
      })
    },
    async openWps(url, token) {
      this.wps = WebOfficeSDK.config({
        mode: 'normal', // simple | normal
        mount: document.querySelector('#wpsHistory'),
        wpsUrl: url,
        onHyperLinkOpen: this.onHyperLinkOpen
      })
      this.wps.setToken({ token })
    },
    // 拦截外链跳转函数
    onHyperLinkOpen(data) {
      this.wps.iframe.src = data.linkUrl
    },
    handleViewVersion(row) {
      this.$router.replace({ path: this.$route.path, query: { ...this.query, fileId: row.id }})
      this.fileId = row.id

      this.getWpsUrlAndOpen()
    }
  }
}
</script>

<style scoped lang="scss">
.wpsOnlineBox{
  float: left;
  width: calc(100% - 350px);
  height: calc(100%);
}
.historyBox {
  float: right;
  width: 350px;
  height: 100%;
  overflow-y: scroll;
}
#wps-iframe{
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    height: 100%;
    /* 防止双击缩放 */
    touch-action: manipulation;
}
</style>
