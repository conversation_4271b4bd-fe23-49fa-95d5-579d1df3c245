<template>
  <div style="width:100%; height:100%">
    <el-row>
      <el-col :span="24" class="btnBox">
        <el-button type="primary" size="small" :loading="loading" @click="doSave()">{{ loading?'保存中':'保存' }}</el-button>
        <el-button type="primary" size="small" :loading="loading" @click="doSave({autoCloseFlag: true})">{{ loading?'保存中':'保存并关闭' }}</el-button>
        <el-button v-if="isMobile" type="default" size="small" @click="doClose()">返回</el-button>
        <el-button v-if="!isMobile && wpsDeployType==='local'" type="default" size="small" @click="wrapHeader()">套红头</el-button>
        <el-button v-if="!isMobile && wpsDeployType==='local'" type="default" size="small" @click="renderBookmarks()">一键成文</el-button>
        <el-button size="small" @click="doPrint()">打印</el-button>
        <el-button v-if="revisionStatus === 2" size="small" @click="toggleRevisionStatus(0)">显示修订痕迹</el-button>
        <el-button v-if="revisionStatus === 0" size="small" @click="toggleRevisionStatus(2)">隐藏修订痕迹</el-button>
      </el-col>
    </el-row>
    <div class="wpsOnlineBox">
      <div id="viewFile" style="width: 100%; height:100%" />
    </div>

    <el-dialog
      title="选择红头模板"
      :visible.sync="wrapHeaderDialogVisible"
      width="460px"
    >
      <div>
        <el-radio-group v-model="headerTemplateFileId" class="redTemplate">
          <el-radio v-for="item in headerTemplateList" :key="item.storageId" :label="item.storageId">{{ item.templateName }}</el-radio>
        </el-radio-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="wrapHeaderDialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="headerTemplateFileId===0" @click="handleWrapHeader">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WebOfficeSDK from '@/../lib/jwps/web-office-sdk.es'
import wpsOnlineApi from '@/api/wpsOnline'
import officeTemplateApi from '@/api/officeTemplate'

const bookmarkRules = window.g.defaultBookmarkRules
const wpsDeployType = window.g.wps && window.g.wps.deployType ? window.g.wps.deployType : 'cloud'

export default {
  name: 'App',
  data() {
    return {
      wps: null,
      fileId: 0,
      jwpsUrl: '',
      jtoken: '',
      revisionStatus: 2, // 0：显示修订状态; 1：显示原始状态（修订前）; 2：显示最终状态（修订后）
      showHistoryPanel: false,
      isMobile: false,
      wpsDeployType: wpsDeployType,
      checkConvertTaskMessager: null,
      checkConvertTaskId: null,
      wrapHeaderDialogVisible: false,
      headerTemplateFileId: 0,
      headerTemplateList: [],
      targetWindow: null,
      postMessageTargetOrigin: '*', // window.location
      loading: false
    }
  },
  created() {
    // 是否显示历史版本面板
    this.showHistoryPanel = this.$route.query.showHistoryPanel === 'true'
    this.isMobile = this.$route.query.isMobile === 'true'
    this.targetWindow = this.isMobile ? window.parent : window.opener

    if (!this.$route.query.fileId) {
      wpsOnlineApi.getWpsUrlByNew({ fileType: 'doc' }).then(
        res => {
          this.$router.replace({ path: this.$route.path, query: { ...this.$route.query, ...{ fileId: res.fileId }}})
          this.fileId = res.fileId
          this.jwpsUrl = res.url
          this.jtoken = res.token
          this.openWps(this.jwpsUrl, this.jtoken)
        }
      )
      return
    }
    wpsOnlineApi.getWpsUrlByFileId({ fileId: this.$route.query.fileId, permission: 'write' }).then(res => {
      this.fileId = res.fileId
      this.jwpsUrl = res.url
      this.jtoken = res.token
      this.openWps(this.jwpsUrl, this.jtoken)
    })
  },
  mounted() {
    window.addEventListener('message', this.onReceiveMessage, false)
  },
  methods: {
    async openWps(url, token) {
      this.wps = WebOfficeSDK.config({
        mode: 'normal', // simple | normal
        mount: document.querySelector('#viewFile'),
        wpsUrl: url,
        cooperUserAttribute: {
          isCooperUsersAvatarVisible: false // 是否显示协作用户头像
        },
        commandBars: [
          {
            cmbId: 'HeaderLeft', // 组件 ID
            attributes: {
              visible: false, // 隐藏组件
              enable: false // 禁用组件，组件显示但不响应点击事件
            }
          },
          {
            cmbId: 'FloatQuickHelp', // 组件 ID
            attributes: {
              visible: false, // 隐藏组件
              enable: false // 禁用组件，组件显示但不响应点击事件
            }
          }
        ],
        onHyperLinkOpen: this.onHyperLinkOpen
      })
      this.wps.setToken({ token })

      // 文件状态改变时回调
      this.wps.ApiEvent.AddApiEventListener('fileStatus', (data) => {
        console.log('文件状态改变：', data)
      })
      // 文件打开成功或者失败时的事件回调
      this.wps.ApiEvent.AddApiEventListener('fileOpen', (data) => {
        console.log('文件打开：', data)
      })
      // 模态框打开和关闭时触发
      this.wps.ApiEvent.AddApiEventListener('ModelVisible', (data) => {
        console.log('ModelVisible: ', data)
      })
      // 错误发生时的事件回调
      this.wps.ApiEvent.AddApiEventListener('error', (err) => {
        console.log('发生错误：', err)
      })

      await this.wps.ready()
      const app = this.wps.Application
      // 将当前文档的编辑状态切换成修订模式
      app.ActiveDocument.TrackRevisions = true

      // 获取节对象
      const wpsView = await app.ActiveDocument.ActiveWindow.View
      // 设置修订状态为最终状态
      wpsView.RevisionsView = 0
      // 设置修订状态为 最终状态
      wpsView.ShowRevisionsAndComments = false

      // 修订框隐藏
      if (this.isMobile) {
        const wpsRevision = await app.ActiveDocument.Revisions
        wpsRevision.ShowRevisionsFrame = false
      }

      // 控制是否需要显示历史版本面板
      if (this.showHistoryPanel) {
        app.CommandBars('HeaderHistoryMenuBtn').Execute()
        app.CommandBars('HistoryVersion').Execute()
      }
    },
    doSave(options) {
      this.loading = true
      // 退出修订模式进行保存
      // this.wps.Application.ActiveDocument.TrackRevisions = false

      this.wps.save().then(result => {
        // 保存后重新打开修订模式
        // this.wps.Application.ActiveDocument.TrackRevisions = true

        console.log('文件保存成功：', result)
        if (result.result === 'ok') {
          if (this.targetWindow) {
            wpsOnlineApi.getFileIdByFileIdAndVersion({
              fileId: this.fileId,
              version: result.version
            }).then(res => {
              console.log(res.fileId)
              this.$router.replace({ path: this.$route.path, query: { fileId: res.fileId }})
              this.fileId = res.fileId
              if (res.fileId) {
                this.targetWindow.postMessage({ cmd: 'ON_SAVED', body: { id: res.fileId }}, this.postMessageTargetOrigin)
                this.$message('文件保存成功')
                this.loading = false
                if (options && options.autoCloseFlag) {
                  this.targetWindow.postMessage({ cmd: 'ON_CLOSE' }, this.postMessageTargetOrigin)
                  window.close()
                }
              } else {
                this.$message({
                  message: '抱歉，保存文档失败请重新尝试保存！',
                  type: 'warning'
                })
                this.loading = false
              }
            })
          } else {
            this.$message.error('回调通知父页面失败')
            this.loading = false
          }
        } else if (result.result === 'nochange') {
          this.$message('文档无更新，无需保存')
          this.loading = false
          if (options && options.autoCloseFlag) {
            this.targetWindow.postMessage({ cmd: 'ON_CLOSE' }, this.postMessageTargetOrigin)
            window.close()
          }
        }
      })
    },
    async doPrint() {
      await this.wps.ready()
      this.wps.executeCommandBar('TabPrintPreview')
    },
    async toggleRevisionStatus(newRevisionStatus) {
      this.revisionStatus = newRevisionStatus

      await this.wps.ready()
      const app = this.wps.Application
      // 获取节对象
      const wpsView = await app.ActiveDocument.ActiveWindow.View
      // 设置修订状态为 1原始状态 0最终状态
      wpsView.RevisionsView = this.revisionStatus !== 1 ? 1 : 0
      // 设置修订状态为 true带标记 false不带标记
      wpsView.ShowRevisionsAndComments = this.revisionStatus === 0
    },
    doClose() {
      this.targetWindow.postMessage({ cmd: 'ON_CLOSE' }, this.postMessageTargetOrigin)
    },
    renderBookmarks() {
      if (!this.targetWindow) {
        this.$message({
          message: '抱歉，无法从父页面获取数据，您可以保存文档后重新打开编辑页面进行再试！',
          type: 'warning'
        })
        return
      }
      this.targetWindow.postMessage({ cmd: 'GET_FORM_DATAMODELS', body: {}}, this.postMessageTargetOrigin)
    },
    async existsBookmark(bookmarkName) {
      const app = this.wps.Application
      const bookmarksData = await app.ActiveDocument.Bookmarks.Json()

      for (var i in bookmarksData) {
        if (bookmarksData[i].name === bookmarkName) {
          return true
        }
      }
      return false
    },
    async handleRenderBookmarks(dataModels) {
      const replaceBookmarkData = {}
      for (const j in bookmarkRules) {
        if (await this.existsBookmark(bookmarkRules[j].bookmark)) {
          try {
            // eslint-disable-next-line no-eval
            const bookmarkValue = eval('dataModels.' + bookmarkRules[j].field)
            if (bookmarkValue) {
              const bookmarkName = bookmarkRules[j].bookmark
              replaceBookmarkData[bookmarkName] = bookmarkValue
            }
          } catch (error) {
            console.log(`书签对应的模型{{bookmarkRules[j].field}}值不存在`)
          }
        }
      }

      if (Object.keys(replaceBookmarkData).length === 0) {
        this.$message({
          message: '该文档没有任何匹配的书签，无法进行一键成文！',
          type: 'warning'
        })
        return
      }

      this.wps.save().then(result => {
        if (result.result === 'ok') {
          wpsOnlineApi.getFileIdByFileIdAndVersion({
            fileId: this.fileId,
            version: result.version
          }).then(res => {
            wpsOnlineApi.renderBookmarks({ docStorageId: res.fileId, bookmarkData: replaceBookmarkData }).then(data => {
              console.log(data.taskId)
              this.checkConvertTaskId = data.taskId
              this.checkConvertTask()
            })
          })
        } else if (result.result === 'nochange') {
          wpsOnlineApi.renderBookmarks({ docStorageId: this.fileId, bookmarkData: replaceBookmarkData }).then(data => {
            console.log(data.taskId)
            this.checkConvertTaskId = data.taskId
            this.checkConvertTask()
          })
        }
      })

      this.wrapHeaderDialogVisible = false
      this.checkConvertTaskMessager = this.$message({
        message: '正在一键成文处理，请稍后',
        type: 'warning',
        duration: 0
      })
    },
    // 前端替换书签，暂时废弃使用，wps有bug
    async handleRenderBookmarks2(dataModels) {
      const replaceBookmarkData = []
      for (const j in bookmarkRules) {
        if (await this.existsBookmark(bookmarkRules[j].bookmark)) {
          try {
            // eslint-disable-next-line no-eval
            const bookmarkValue = eval('dataModels.' + bookmarkRules[j].field)
            if (bookmarkValue) {
              replaceBookmarkData.push({
                name: bookmarkRules[j].bookmark,
                type: 'text',
                value: bookmarkValue
              })
            }
          } catch (error) {
            console.log(`书签对应的模型{{bookmarkRules[j].field}}值不存在`)
          }
        }
      }

      if (replaceBookmarkData.length === 0) {
        this.$message({
          message: '该文档没有任何匹配的书签，无法进行一键成文！',
          type: 'warning'
        })
        return
      }

      const app = this.wps.Application
      const isReplaceSuccess = await app.ActiveDocument.Bookmarks.ReplaceBookmark(replaceBookmarkData)
      if (isReplaceSuccess) {
        this.$message({
          message: '一键成文完成',
          type: 'warning'
        })
      } else {
        console.log('一键成文失败')
      }
    },
    wrapHeader() {
      officeTemplateApi.list({}).then(res => { this.headerTemplateList = res.content })
      this.wrapHeaderDialogVisible = true
      // this.checkConvertTaskMessager.close()
    },
    async handleWrapHeader() {
      this.wps.save().then(result => {
        if (result.result === 'ok') {
          wpsOnlineApi.getFileIdByFileIdAndVersion({
            fileId: this.fileId,
            version: result.version
          }).then(res => {
            wpsOnlineApi.wrapHeader({ fileId: res.fileId, headerTemplateFileId: this.headerTemplateFileId }).then(data => {
              console.log(data.taskId)
              this.checkConvertTaskId = data.taskId
              this.checkConvertTask()
            })
          })
        } else if (result.result === 'nochange') {
          wpsOnlineApi.wrapHeader({ fileId: this.fileId, headerTemplateFileId: this.headerTemplateFileId }).then(data => {
            console.log(data.taskId)
            this.checkConvertTaskId = data.taskId
            this.checkConvertTask()
          })
        }
      })

      this.wrapHeaderDialogVisible = false
      this.checkConvertTaskMessager = this.$message({
        message: '正在套红处理，请稍后',
        type: 'warning',
        duration: 0
      })
    },
    async handleWrapHeader2() {
      const existsBookmark = await this.existsBookmark('红头')

      this.wps.save().then(result => {
        if (result.result === 'ok') {
          wpsOnlineApi.getFileIdByFileIdAndVersion({
            fileId: this.fileId,
            version: result.version
          }).then(res => {
            const promiseObj = existsBookmark
              ? wpsOnlineApi.wrapHeader({ fileId: res.fileId, headerTemplateFileId: this.headerTemplateFileId })
              : wpsOnlineApi.mergeDoc({ firstFileId: this.headerTemplateFileId, secondFileId: res.fileId })
            promiseObj.then(data => {
              console.log(data.taskId)
              this.checkConvertTaskId = data.taskId
              this.checkConvertTask()
            })
          })
        } else if (result.result === 'nochange') {
          const promiseObj = existsBookmark
            ? wpsOnlineApi.wrapHeader({ fileId: this.fileId, headerTemplateFileId: this.headerTemplateFileId })
            : wpsOnlineApi.mergeDoc({ firstFileId: this.headerTemplateFileId, secondFileId: this.fileId })
          promiseObj.then(data => {
            console.log(data.taskId)
            this.checkConvertTaskId = data.taskId
            this.checkConvertTask()
          })
        }
      })

      this.wrapHeaderDialogVisible = false
      this.checkConvertTaskMessager = this.$message({
        message: '正在套红处理，请稍后',
        type: 'warning',
        duration: 0
      })
    },
    checkConvertTask() {
      wpsOnlineApi.checkConvertTask({ taskId: this.checkConvertTaskId }).then(res => {
        if (!res.result) {
          setTimeout(this.checkConvertTask, 600)
        } else {
          wpsOnlineApi.getLatestVersion({ fileId: this.fileId }).then(data => {
            this.targetWindow.postMessage({ cmd: 'ON_SAVED', body: { id: data.storageId }}, this.postMessageTargetOrigin)
            this.checkConvertTaskMessager.close()

            wpsOnlineApi.getWpsUrlByFileId({ fileId: data.storageId, permission: 'write' }).then(res => {
              this.fileId = res.fileId
              this.jwpsUrl = res.url
              this.jtoken = res.token

              const routeQuery = { ...this.$route.query }
              routeQuery.fileId = res.fileId
              this.$router.replace({ path: this.$route.path, query: routeQuery })

              this.openWps(this.jwpsUrl, this.jtoken)
            })
          })
        }
      })
    },
    onReceiveMessage(event) {
      if (this.postMessageTargetOrigin !== '*' && event.origin !== this.popup.origin) return

      if (event.data.cmd === 'PUSH_FORM_DATAMODELS') {
        this.handleRenderBookmarks(event.data.body)
      }
    },
    // 拦截外链跳转函数
    onHyperLinkOpen(data) {
      this.wps.iframe.src = data.linkUrl
    }
  }
}
</script>

<style scoped lang="scss">
.btnBox{
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.wpsOnlineBox{
  height: calc(100% - 40px);
}
.redTemplate .el-radio{
  display: block;
  margin-bottom: 10px;
}

#wps-iframe{
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    height: 100%;
    /* 防止双击缩放 */
    touch-action: manipulation;
}
</style>
