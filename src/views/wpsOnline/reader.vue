<template>
  <div style="width:100%; height:100%">
    <el-button v-if="isMobile" type="success" icon="el-icon-arrow-left" circle class="btnBack" @click="doClose()" />
    <div class="wpsOnlineBox">
      <div id="wpsReader" style="width: 100%; height:100%" />
    </div>
  </div>
</template>

<script>
import WebOfficeSDK from '@/../lib/jwps/web-office-sdk.es'
import wpsOnlineApi from '@/api/wpsOnline'

export default {
  name: 'App',
  components: { },
  data() {
    return {
      wps: null,
      jwpsUrl: '',
      jtoken: '',
      isMobile: false,
      targetWindow: null,
      postMessageTargetOrigin: '*' // window.location
    }
  },
  created() {
    this.isMobile = this.$route.query.isMobile === 'true'
    this.targetWindow = this.isMobile ? window.parent : window.opener

    if (!this.$route.query.fileId) {
      alert('非法参数')
      return
    }

    wpsOnlineApi.getWpsUrlByFileId({ fileId: this.$route.query.fileId, permission: 'read' }).then(res => {
      this.jwpsUrl = res.url
      this.jtoken = res.token
      this.openWps(this.jwpsUrl, this.jtoken)
    })
  },
  methods: {
    openWps(url, token) {
      this.wps = WebOfficeSDK.config({
        mode: 'normal', // simple | normal
        mount: document.querySelector('#wpsReader'),
        wpsUrl: url,
        cooperUserAttribute: {
          isCooperUsersAvatarVisible: false // 是否显示协作用户头像
        },
        commandBars: [
          {
            cmbId: 'HeaderLeft', // 组件 ID
            attributes: {
              visible: false, // 隐藏组件
              enable: false // 禁用组件，组件显示但不响应点击事件
            }
          },
          {
            cmbId: 'FloatQuickHelp', // 组件 ID
            attributes: {
              visible: false, // 隐藏组件
              enable: false // 禁用组件，组件显示但不响应点击事件
            }
          }
        ],
        onHyperLinkOpen: this.onHyperLinkOpen
      })
      this.wps.setToken({ token })
      this.wps.on('fileStatus', (data) => {
        console.log('文件保存：', data)
      })
    },
    // 拦截外链跳转函数
    onHyperLinkOpen(data) {
      this.wps.iframe.src = data.linkUrl
    },
    doClose() {
      this.targetWindow.postMessage({ cmd: 'ON_CLOSE' }, this.postMessageTargetOrigin)
    }
  }
}
</script>

<style scoped lang="scss">
.btnBack {
  position: absolute;
  right: 20px;
  bottom: 50px;
  z-index:9999;
}
.btnBox{
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.wpsOnlineBox{
  height: 100%;
}

#wps-iframe{
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    height: 100%;
    /* 防止双击缩放 */
    touch-action: manipulation;
}
</style>
