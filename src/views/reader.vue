<template>
  <el-skeleton :rows="6" animated />
</template>

<script>
import browser from '../utils/browser'
import onlineEditApi from '@/api/docconvert'
import localStorageApi from '@/api/localStorage'

export default {
  data() {
    return {
      pdfjsViewerUrl: window.g.pdfjsViewerUrl,
      query: null
    }
  },
  created() {
    this.query = this.$route.query
  },
  mounted() {
    // 转化服务
    if (this.query.conversion && this.query.conversion === 'true') { // 有pdf格式
      onlineEditApi.fileConversionInfo({ 'storageId': this.query.fileId }).then(res => {
        var fileId = null
        var suffix = null
        if (res.conversionStorage && res.conversionStorage.pdf) {
          suffix = res.conversionStorage.pdf.suffix
          fileId = res.conversionStorage.pdf.id
        } else {
          suffix = res.suffix
          fileId = res.storageId
        }
        this.query = { ...this.query, fileId: fileId }
        this.$delete(this.query, 'conversion')
        this.reader(suffix)
      })
    } else {
      localStorageApi.postFindByIds({ ids: this.query.fileId }).then(res => {
        this.reader(res[0].suffix)
      })
    }
  },
  methods: {
    isocxReader(suffix) {
      // 插件是否支持预览
      return window.g.ocxSupportSuffix && window.g.ocxSupportSuffix.includes(suffix)
    },
    reader(suffix) {
      if (suffix === 'pdf') {
        this.pdfReader()
      } else if (this.isocxReader(suffix)) { // 其他一些插件支持预览的格式
        this.ocxReader()
      } else {
      // 浏览器文件查看，如果不支持会下载 /api/localStorage/previewFile/{fileId}
        this.preview()
      }
    },
    preview() {
      location.href = window.g.apiBaseUrl + '/api/localStorage/previewFile/' + this.query.fileId
    },
    pdfReader() {
      if (this.pdfjsViewerUrl) {
        this.$router.replace({ path: '/pdfjs/reader', query: this.query })
        // location.href = this.pdfjsViewerUrl + '?fileId=' + this.query.fileId
      } else {
        this.ocxReader()
      }
    },
    ocxReader() {
      var editorClasses = window.g.editorClasses
      var os = browser.detectOS()
      var path
      if (os === 'Win') {
        if (editorClasses.indexOf('goldgrid') > -1) {
          path = '/goldgrid/reader'
        } else if (editorClasses.indexOf('wps') > -1) {
          path = '/wpsOnline/reader'
        } else {
          path = '/debug/editor'
        }
      } else if (os === 'Mac') {
        if (editorClasses.indexOf('wps') > -1) {
          path = '/wpsOnline/reader'
        } else {
          path = '/debug/editor'
        }
      } else {
        if (editorClasses.indexOf('goldgrid-zzkk') > -1) {
          path = '/goldgrid/zzkkReader'
        } else if (editorClasses.indexOf('wps') > -1) {
          path = '/wpsOnline/reader'
        } else {
          path = '/debug/editor'
        }
      }
      this.$router.replace({ path, query: this.query })
    }
  }
}
</script>
