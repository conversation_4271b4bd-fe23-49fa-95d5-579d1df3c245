<template>
  <div class="app-container">
    TOKEN：<el-input v-model="token" style="width:600px" /><el-button type="primary" @click="handLogin()">登录</el-button><br>
    文件ID：<el-input v-model="fileId" name="fileId" style="width:200px" />
    客户端格式转化：<el-input v-model="formatConvertByOcx" name="formatConvertByOcx" style="width:200px" />
    <hr>
    <el-link type="primary" @click="openEditor('#/editor')">在线编辑（自动识别）</el-link><br>
    <hr>
    <el-link type="primary" @click="openEditor('#/goldgrid/zzkkEditor')">金格信创版在线编辑</el-link><br>
    <el-link type="primary" @click="openEditor('#/goldgrid/editor')">金格windows版在线编辑</el-link><br>
    <el-link type="primary" @click="openEditor('#/ntko/editor')">软航windows版在线编辑</el-link><br>
    <el-link type="primary" @click="openEditor('#/wpsOnline/editor')">wps在线编辑器</el-link><br>
    <hr>
    <div>天谷电子签章</div>
    <hr>
    <div>格式转化</div>
  </div>
</template>

<script>
import Vue from 'vue'
import {
  Link,
  Input
} from 'element-ui'
import { setToken } from '@/utils/auth'

Vue.use(Link)
Vue.use(Input)

export default {
  name: 'App',
  data() {
    return {
      token: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI5NWZkYTdkMGEwOTg0NjQxOGVkMzMzNjI0Yjk0ZTUzMiIsImF1dGgiOiJhZG1pbiIsInN1YiI6InN1cGVyQWRtaW4ifQ.EZdSd41CYXIpMDz0W_a6oKtBXxnaikJ8D9d4ADgFY6hKPs1V-JboY0-X73RX2qonw5KwAeoc3gO9RgTVs9mSAA',
      fileId: null,
      formatConvertByOcx: false,
      popup: null,
      postMessageTargetOrigin: '*' // window.location
    }
  },
  mounted() {
    // postMessage接收子页面参数
    this.popup = window.addEventListener('message', this.onReceiveMessage, false)
  },
  methods: {
    handLogin() {
      setToken(this.token, false)
    },
    openEditor(route) {
      if (this.fileId) {
        route += '?fileId=' + this.fileId
      }
      window.open(route)
    },
    onFileEditSave(data) {
      this.dataModel = {
        storageId: data.id,
        bizType: 'testBizType'
      }
      // onlineEditApi.convertFormat({ storageId, convertTypes: [] })
    },
    onReceiveMessage(event) {
      if (this.postMessageTargetOrigin !== '*' && event.origin !== this.popup.origin) return

      if (event.data.cmd === 'ON_SAVED') {
        console.log('ON_SAVED', event.data.body)
        const data = event.data.body
        this.fileId = data.id
      } else if (event.data.cmd === 'GET_FORM_DATAMODELS') {
        // this.popup.postMessage({ cmd: 'PUSH_FORM_DATAMODELS', body: this.dataModels }, this.postMessageTargetOrigin)
      }
    }
  }

}
</script>
