<template>
  <iframe v-if="fileUrl" :src="fileUrl" frameborder="0" style="width:100%; height:100%;" />
  <el-empty v-else description="文件不存在" />
</template>

<script>
import browser from '../../utils/browser'

export default {
  data() {
  },
  computed: {
    pdfjsViewerUrl: function() {
      let pdfjsViewerUrl = window.g.pdfjsViewerUrl
      pdfjsViewerUrl = pdfjsViewerUrl.replaceAll('web/viewer.html', '')

      if (browser.detectOSBrowser().browser === 'IE') {
        pdfjsViewerUrl += 'ie-comp/'
      }
      return pdfjsViewerUrl + 'web/viewer.html'
    },

    fileUrl: function() {
      if (this.$route.query.fileId) {
        let url = window.g.apiBaseUrl + '/api/localStorage/previewFile/' + this.$route.query.fileId
        url = this.pdfjsViewerUrl + '?file=' + encodeURIComponent(url)
        return url
      }
      return null
    }
  }
}
</script>
