<template>
  <div class="app-container" />
</template>

<script>
import browser from '../utils/browser'

export default {
  mounted() {
    var editorClasses = window.g.editorClasses
    var os = browser.detectOS()
    var path
    if (os === 'Win') {
      if (editorClasses.indexOf('goldgrid') > -1) {
        path = '/goldgrid/editor'
      } else if (editorClasses.indexOf('wps') > -1) {
        path = '/wpsOnline/history'
      } else {
        path = '/debug/editor'
      }
    } else if (os === 'Mac') {
      if (editorClasses.indexOf('wps') > -1) {
        path = '/wpsOnline/history'
      } else {
        path = '/debug/editor'
      }
    } else {
      if (editorClasses.indexOf('goldgrid-zzkk') > -1) {
        path = '/goldgrid/zzkkEditor'
      } else if (editorClasses.indexOf('wps') > -1) {
        path = '/wpsOnline/history'
      } else {
        path = '/debug/editor'
      }
    }
    this.$router.replace({ path, query: this.$route.query })
  }
}
</script>
