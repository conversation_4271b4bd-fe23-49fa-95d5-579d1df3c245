export default {

  // 判断操作系统
  detectOS() {
    var isWin = (navigator.platform === 'Win32') || (navigator.platform === 'Windows')
    if (isWin) {
      return 'Win'
    }
    var isMac = (navigator.platform === 'Mac68K') || (navigator.platform === 'MacPPC') || (navigator.platform === 'Macintosh') || (navigator.platform === 'MacIntel')
    if (isMac) {
      return 'Mac'
    }
    var isUnix = (navigator.platform === 'X11') && !isWin && !isMac
    if (isUnix) {
      return 'Unix'
    }
    var isLinux = (String(navigator.platform).indexOf('Linux') > -1)
    if (isLinux) {
      return 'Linux'
    }
    return 'Other'
  },

  // 判断操作系统及浏览器
  detectOSBrowser() {
    var userAgent = navigator.userAgent
    var ua = userAgent.toLowerCase()
    var rMsie = /(msie\s|trident.*rv:)([\w.]+)/
    var rFirefox = /(firefox)\/([\w.]+)/
    var rOpera = /(opera).+version\/([\w.]+)/
    var rChrome = /(chrome)\/([\w.]+)/
    var rSafari = /version\/([\w.]+).*(safari)/
    var match

    match = rMsie.exec(ua)
    if (match != null) {
      if ((window.ActiveXObject !== undefined) || (window.ActiveXObject != null) || 'ActiveXObject' in window) {
        if (window.navigator.platform === 'Win32') {
          return { platform: 'Win32', browser: 'IE', version: match[2] || '0' }
        } else if (window.navigator.platform === 'Win64') {
          return { platform: 'Win64', browser: 'IE', version: match[2] || '0' }
        }
      }
      return { platform: this.detectOS(), browser: 'IE', version: match[2] || '0' }
    }
    match = rFirefox.exec(ua)
    if (match != null) {
      return { platform: this.detectOS(), browser: match[1] || '', version: match[2] || '0' }
    }
    match = rOpera.exec(ua)
    if (match != null) {
      return { platform: this.detectOS(), browser: match[1] || '', version: match[2] || '0' }
    }
    match = rChrome.exec(ua)
    if (match != null) {
      return { platform: this.detectOS(), browser: match[1] || '', version: match[2] || '0' }
    }
    match = rSafari.exec(ua)
    if (match != null) {
      return { platform: this.detectOS(), browser: match[2] || '', version: match[1] || '0' }
    }
    return { platform: this.detectOS(), browser: 'other', version: '0' }
  }
}
