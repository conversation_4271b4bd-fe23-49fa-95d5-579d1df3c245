import Cookies from 'js-cookie'

const Token<PERSON>ey = process.env.VUE_APP_AUTH_TOKEN_KEY

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token, rememberMe) {
  if (rememberMe) {
    return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: process.env.VUE_APP_AUTH_TOKEN_EXPIRES })
  } else return Cookies.set(<PERSON><PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
