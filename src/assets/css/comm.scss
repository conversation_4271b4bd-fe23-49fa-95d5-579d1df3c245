/* CSS Document */
html {
  width: 100%;
  text-align: left;
  font-size: 18px;
  height: 100%;
  background-color: #eeeeee;
}
body {
  height: 100%;
  // font-family: arial, "Open Sans", "新宋体", sans-serif, "宋体";
  /* background: #ededed; */
}
* {
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
}
ul,
li {
  list-style: none;
}
h1,
h2,
h3,
h4,
h5,
h6,
td,
th,
div,
button,
input,
select {
  font-size: 100%;
  font-weight: normal;
}
strong,
b {
  font-weight: bold;
}
a {
  text-decoration: none;
  // color: #323233;
}
a:hover {
  /*text-decoration: underline;*/
  color: #323233;
}
a,
button {
  cursor: pointer;
}
.clearfix:after {
  content: " ";
  display: block;
  clear: both;
}
.centered {
  position: fixed;
  top: 50%;
  left: 50%;
  /* bring your own prefixes */
  transform: translate(-50%, -50%);
}

[v-cloak] {
  display: none;
}
.primary-color {
  color: #0b6fe9;
}
.font_block {
  color: #323233;
}
.font_block0 {
  color: #000000;
}

.font_grey {
  color: #666666;
}
.font_grey2 {
  color: #999999;
}
.font_grey8 {
  color: #888888;
}
.font_grey3 {
  color: #333;
}

.font_blue {
  color: #3296fa;
}

.font_red {
  color: #ea391c;
}
.bg_blue {
  background-color: #3296fa;
}

.white_box {
  background: #fff;
  margin-bottom: 2.5vw;
}

.grey_box {
  background: #f6f6f6;
}
.white_bg {
  background: #fff;
}

.font_size_small_2 {
  font-size: 0.875rem;
}
.font_size_small_4 {
  font-size: 0.75rem;
}
.font_size_small_6 {
  font-size: 0.625rem;
}
.font_size {
  font-size: 1rem;
}

.font_size_2 {
  font-size: 1.125rem;
}

.font_size_4 {
  font-size: 1.25rem;
}

.font_size_6 {
  font-size: 1.375rem;
}

.font_size_8 {
  font-size: 1.5rem;
}

.font_size_14 {
  font-size: 1.875rem;
}

.font_weight {
  font-weight: bold;
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

.pull_left {
  float: left;
}

.pull_right {
  float: right;
}

.t_center {
  text-align: center;
}

.fixe_bottom_btn {
  position: fixed;
  width: calc(100vw - 30px);
  padding: 15px 0;
  bottom: 0;
  background: #f6f6f6;
}

#__vconsole {
  display: none;
}
.borderbox {
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
.flex {
  display: flex;
}
.between {
  justify-content: space-between;
}
.require {
  color: #e71c1c;
  font-size: 16px;
}
.oneline-limit {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.twoline-limit {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.search_box .el-input {
  width: 220px;
}

.pointer {
  cursor: pointer;
}
.textHover {
  cursor: pointer;
  &:hover {
    color: #fff;
  }
}

// div::scrollbar {
//   display: none;
// }
// div::-webkit-scrollbar {
//   display: none;
// }

.el-table {
  font-size: 14px !important;
}
