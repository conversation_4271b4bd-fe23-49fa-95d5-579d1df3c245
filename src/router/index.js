import Vue from 'vue'
import VueRouter from 'vue-router'
import { getInfo } from '@/api/userInfo'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'index',
    component: () => import('@/views/index.vue')
  },
  {
    path: '/debug',
    component: () => import('@/views/debug.vue')
  },
  {
    path: '/editor',
    component: () => import('@/views/editor.vue')
  },
  {
    path: '/reader',
    component: () => import('@/views/reader.vue')
  },
  {
    path: '/history',
    component: () => import('@/views/history.vue')
  },
  {
    path: '/goldgrid/editor',
    component: () => import('@/views/goldgrid/editor.vue')
  },
  {
    path: '/goldgrid/reader',
    component: () => import('@/views/goldgrid/reader.vue')
  },
  {
    path: '/goldgrid/zzkkEditor',
    component: () => import('@/views/goldgrid/zzkkEditor.vue')
  },
  {
    path: '/goldgrid/zzkkReader',
    component: () => import('@/views/goldgrid/zzkkReader.vue')
  },
  {
    path: '/wpsOnline/editor',
    component: () => import('@/views/wpsOnline/editor.vue')
  },
  {
    path: '/wpsOnline/reader',
    component: () => import('@/views/wpsOnline/reader.vue')
  },
  {
    path: '/wpsOnline/history',
    component: () => import('@/views/wpsOnline/history.vue')
  },
  {
    path: '/pdfjs/reader',
    component: () => import('@/views/pdfjs/reader.vue')
  },
  {
    path: '/debug/editor',
    component: () => import('@/views/debug/editor.vue')
  },
  {
    path: '/tgsign/sealSign',
    component: () => import('@/views/tgsign/sealSign.vue')
  }
]

const router = new VueRouter({
  mode: 'hash',
  base: process.env.BASE_URL,
  routes
})

router.beforeEach((to, from, next) => {
  if (from.path === '/') {
    getInfo().then(res => {
      window.localStorage.wmUserInfo = JSON.stringify({
        userId: res.user.username,
        userTag: '未分类',
        projectVersion: '1.0.0'
      })
    })
  }
  next()
})

export default router
