import request from '@/utils/request'

export function init() {
  const projectid = window.g.tgSign.projectId
  const projectsecret = window.g.tgSign.projectSecret
  /*
  const data = JSON.stringify({
    'serverName': '{0DADE507-64D6-4306-956A-2ED144FF0ED1}',
    'funcName': 'TG_SetConfig',
    'param': JSON.stringify({
      'config': JSON.stringify({
        'sectionList': [
          {
            'TGPrintCtrl': {
              'ProjectID': projectid,
              'ProjectSecret': projectsecret
            }
          },
          {
            'GetSealList': {
              'project_id': projectid,
              'project_secret': projectsecret
            }
          }
        ]
      })
    })
  })
  console.log('init', data)
  */
  return request({
    url: 'https://localhost:7688/TGCtrlApi',
    method: 'POST',
    data: '{"serverName": "{0DADE507-64D6-4306-956A-2ED144FF0ED1}","funcName": "TG_SetConfig","param": "{\\"config\\": \\"{			\\\\\\"sectionList\\\\\\": [{\\\\\\"TGPrintCtrl\\\\\\": {\\\\\\"ProjectID\\\\\\": \\\\\\"' + projectid + '\\\\\\",	\\\\\\"ProjectSecret\\\\\\": \\\\\\"' + projectsecret + '\\\\\\"}},	{\\\\\\"GetSealList\\\\\\": {\\\\\\"project_id\\\\\\": \\\\\\"' + projectid + '\\\\\\",	\\\\\\"project_secret\\\\\\": \\\\\\"' + projectsecret + '\\\\\\"}}]}\\"}"}'
  })
}

export function uploadSignFile(filePathSrc, origStorageId) {
  /*
  const data = JSON.stringify({
    'serverName': '{0DADE507-64D6-4306-956A-2ED144FF0ED1}',
    'funcName': 'UploadFileToServer',
    'param': JSON.stringify({
      'serverUrl': window.g.formMaking.serviceBaseHost + '/api/seal/uploadSignFile',
      'filePath': filePathSrc,
      'fileForm': 'file',
      'extraParam': {
        'origStorageId': origStorageId,
        'token': 'todo-xxxx'
      }
    })
  })
  */

  const serverUrl = window.g.apiBaseUrl + '/api/seal/uploadSignFile'
  const fileForm = 'file'
  const extraParam = JSON.stringify({ origStorageId, token: 'todo' }).replaceAll('"', "'")
  return request({
    url: 'https://localhost:7688/TGCtrlApi',
    method: 'POST',
    data: '{"serverName":"{0DADE507-64D6-4306-956A-2ED144FF0ED1}","funcName":"UploadFileToServer","param":"{\\"serverUrl\\":\\"' + serverUrl + '\\",\\"filePath\\":\\"' + filePathSrc + '\\",\\"fileForm\\":\\"' + fileForm + '\\",\\"extraParam\\":\\"' + extraParam + '\\"}"}'
  })
}

/**
 * 获取系统临时目录路径,通过此目录获取到的文件路径会有读写权限；
 */
function getSystemTempPath() {
  return request({
    url: 'https://localhost:7688/TGCtrlApi',
    method: 'POST',
    data: '{"serverName":"{0DADE507-64D6-4306-956A-2ED144FF0ED1}","funcName":"TGGetSystemTempPath","param":"{}"}'
  })
}

export default { init, uploadSignFile, getSystemTempPath }
