import request from '@/utils/request'

// 根据文件ID获取在线编辑URL
function getWpsUrlByFileId(params) {
  return request({
    url: '/api/wps/webOffice/getWpsUrlByFileId',
    method: 'get',
    params
  })
}

// 获取新建文件URL
function getWpsUrlByNew(params) {
  return request({
    url: '/api/wps/webOffice/getWpsUrlByNew',
    method: 'get',
    params
  })
}

// 获取文件的所有历史版本记录
function fileHistories(params) {
  return request({
    url: '/api/wps/webOffice/fileHistories',
    method: 'get',
    params
  })
}

// 根据文件ID和版本号获取对应文件ID
function getFileIdByFileIdAndVersion(params) {
  return request({
    url: '/api/wps/webOffice/getFileIdByFileIdAndVersion',
    method: 'get',
    params
  })
}

// 根据文件ID和版本号获取对应文件ID
function getLatestVersion(params) {
  return request({
    url: '/api/wps/webOffice/getLatestVersion',
    method: 'get',
    params
  })
}

// 套红
function wrapHeader(params) {
  return request({
    url: '/api/wps/formatConvert/wrapHeader',
    method: 'post',
    params
  })
}

// 文档合并
function mergeDoc(params) {
  return request({
    url: '/api/wps/formatConvert/mergeDoc',
    method: 'post',
    params
  })
}

// 替换书签
function renderBookmarks(data) {
  return request({
    url: '/api/wps/formatConvert/renderBookmarks',
    method: 'post',
    data
  })
}

function checkConvertTask(params) {
  return request({
    url: '/api/wps/formatConvert/checkConvertTask',
    method: 'get',
    params
  })
}

export default { getWpsUrlByFileId, getWpsUrlByNew, fileHistories, getFileIdByFileIdAndVersion, getLatestVersion, wrapHeader, mergeDoc, renderBookmarks, checkConvertTask }
