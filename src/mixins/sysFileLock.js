import sysFileLockApi from '@/api/sysFileLock'
import { getToken } from '@/utils/auth'
export default {
  data() {
    return {
      apiBaseUrl: window.g.apiBaseUrl,
      fileLock: {
        status: false,
        eessage: null
      },
      websock: null
    }
  },
  created() {
  },
  mounted() {
    this.sysFileLock()
    window.addEventListener('beforeunload', e => this.beforeunloadHandler(e))
  },
  destroyed() {
    window.removeEventListener('beforeunload', e => this.beforeunloadHandler(e))
  },
  methods: {
    sysFileLock() {
      if (this.fileId) {
        sysFileLockApi.add(this.fileId).then(res => {
          if (res.code === 400) {
            this.fileLock.status = true
            this.fileLock.message = res.message
          } else if (res.code === 10000) {
            this.initWebSocket()
            if (this.websock) { // 关闭websocket连接
              this.websock.close()
            }
          }
        })
      }
    },
    async beforeunloadHandler(e) {
      if (this.websock) { // 页面销毁时关闭websocket
        this.websock.close()
      }
      await this.deleteByFileId() // 退出登录接口
    },
    deleteByFileId() {
      if (!this.fileLock.status && this.fileId) {
        fetch(this.apiBaseUrl + `/api/aigov/sysFileLock/deleteByFileId?fileId=` + this.fileId, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': getToken()
          },
          // 保持连接
          keepalive: true
        })
      }
    },
    initWebSocket() {
      if (typeof WebSocket === 'undefined') {
        this.$message({
          message: '您的浏览器不支持WebSocket',
          type: 'warning'
        })
        return false
      }
      const wsuri = window.g.WsApi + '/webSocket/sysFileLock/' + this.$route.query.fileId
      this.websock = new WebSocket(wsuri)
      this.websock.onopen = this.websocketonopen
      this.websock.onmessage = this.websocketonmessage
      this.websock.onerror = this.websocketonerror
      this.websock.onclose = this.websocketclose
    },
    // 连接成功
    websocketonopen() {
      console.log('WebSocket连接成功')
      // 添加心跳检测，每3秒发一次数据，防止连接断开（跟服务器设置有关，如果服务器没有设置每隔多长时间不发消息断开，可以不进行心跳设置）
      const self = this
      this.timer = setInterval(() => {
        try {
          self.websock.send('test')
          console.log('发送消息')
        } catch (err) {
          console.log('断开了：' + err)
          self.connection()
        }
      }, 30000)
    },
    // 接收后端返回的数据
    websocketonmessage(e) {
      const data1Json = JSON.parse(e.data)
      console.log(data1Json)
    },
    // 连接建立失败重连
    websocketonerror(e) {
      console.log(`连接失败的信息：`, e)
      this.initWebSocket() // 连接失败后尝试重新连接
    },
    // 关闭连接
    websocketclose(e) {
      console.log('断开连接', e)
    }
  }
}

