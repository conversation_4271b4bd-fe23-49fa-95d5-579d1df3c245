import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

import './assets/css/comm.scss'
import 'whatwg-fetch'
import {
  Button,
  ButtonGroup,
  Col,
  Row,
  Tabs,
  TabPane,
  Table,
  TableColumn,
  Select,
  Option,
  Descriptions,
  DescriptionsItem,
  Progress,
  Cascader,
  Radio,
  RadioGroup,
  RadioButton,
  Pagination,
  PageHeader,
  Message,
  Dialog,
  Loading,
  Empty,
  Skeleton,
  SkeletonItem
} from 'element-ui'

Vue.use(Button)
Vue.use(ButtonGroup)
Vue.use(Col)
Vue.use(Row)
Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Select)
Vue.use(Option)
Vue.use(Descriptions)
Vue.use(DescriptionsItem)
Vue.use(Progress)
Vue.use(Cascader)
Vue.use(Radio)
Vue.use(RadioGroup)
Vue.use(RadioButton)
Vue.use(Pagination)
Vue.use(PageHeader)
Vue.use(Dialog)
Vue.use(Loading)
Vue.use(Empty)
Vue.use(Skeleton)
Vue.use(SkeletonItem)
Vue.config.productionTip = false
Vue.prototype.$message = Message

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')
