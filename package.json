{"name": "web-office", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "eslint --ext .js,.vue src", "lint-fix": "eslint --fix --ext .js,.vue src"}, "dependencies": {"axios": "0.21.0", "core-js": "^3.6.5", "element-ui": "^2.15.6", "js-cookie": "2.2.1", "moment": "^2.29.1", "sass": "^1.43.4", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0", "whatwg-fetch": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.12.0", "prettier": "^2.2.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}