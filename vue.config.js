'use strict'

const name = '在线编辑器' // 网址标题
const port = 8015 // 端口配置

module.exports = {
  publicPath: process.env.VUE_PUBILC_PATH, // 打包输出路径
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: true, // process.env.NODE_ENV === 'development',
  devServer: {
    port: port,
    open: true, // 启动服务器时自动打开浏览器访问
    disableHostCheck: true,
    headers: {
      'Access-control-Allow-Origin': '*'
    },
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: process.env.VUE_APP_BASE_API,
        changOrigin: true, // 开启代理
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  },
  configureWebpack: {
    name: name,
    devtool: 'source-map', // 调试模式
    resolve: {
      alias: {
        assets: '@/assets',
        components: '@/components',
        views: '@/views'
      }
    }
  },
  chainWebpack: (config) => {

  }
}
