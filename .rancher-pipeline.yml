stages:
- name: Build
  steps:
  - runScriptConfig:
      image: maven:3.6.3-jdk-8
      shellScript: mvn clean package
- name: Publish
  steps:
  - publishImageConfig:
      dockerfilePath: ./Dockerfile
      buildContext: .
      tag: unioa-mgt-server:${CICD_EXECUTION_SEQUENCE}
      pushRemote: true
      registry: registry.ec.hzwangda.com
    env:
      PLUGIN_DEBUG: "true"
      PLUGIN_INSECURE: "true"
      PLUGIN_MIRROR: https://g9r2bc0j.mirror.aliyuncs.com
- name: Deploy
  steps:
  - applyYamlConfig:
      path: ./deployment.yaml
timeout: 60
notification: {}
