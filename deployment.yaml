kind: Service
apiVersion: v1
metadata:
  name: unioa-mgt-server
spec:
  selector:
    workload.user.cattle.io/workloadselector: deployment-unioa-mgt-server
  type: NodePort
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: unioa-mgt-server
  labels:
    workload.user.cattle.io/workloadselector: deployment-unioa-mgt-server
spec:
  replicas: 1
  selector:
    matchLabels:
      workload.user.cattle.io/workloadselector: deployment-unioa-mgt-server
  template:
    metadata:
      labels:
        workload.user.cattle.io/workloadselector: deployment-unioa-mgt-server
    spec:
      imagePullSecrets:
      - name: pipeline-docker-registry
      containers:
      - name: unioa-mgt-server
        image: ${CICD_IMAGE}:${CICD_EXECUTION_SEQUENCE}
        ports:
        - containerPort: 8000
